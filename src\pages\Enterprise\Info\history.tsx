import { getHistory } from '@/services/enterprise';
import { Table, TableProps } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useEffect, useState } from 'react';
import style from './style.less';

dayjs.extend(utc);
const HistoryList = () => {
  const [historylist, setHistoryList] = useState([]);

  const getHistorylist = async () => {
    const res = await getHistory();
    setHistoryList(res);
  };

  useEffect(() => {
    getHistorylist();
  }, []);

  const columns: TableProps<any>['columns'] = [
    {
      title: '时间',
      dataIndex: 'CreatedOnUtc',
      key: 'CreatedOnUtc',
      render: (_, record) => {
        return (
          <span>
            {dayjs
              .utc(record.CreatedOnUtc)
              .add(8, 'hour')
              .format('YYYY-MM-DD HH:mm:ss')}
          </span>
        );
      },
    },
    {
      title: '操作内容',
      dataIndex: 'Operation',
      key: 'Operation',
    },
    {
      title: '备注',
      dataIndex: 'Comment',
      key: 'Comment',
    },
  ];

  return (
    <div>
      <div className={style.titlewapper}>
        <span className={style.cardtitle}></span>
        <span>流程记录</span>
      </div>
      <Table
        rowKey={'CreatedOnUtc'}
        columns={columns}
        dataSource={historylist}
      />
    </div>
  );
};
export default HistoryList;
