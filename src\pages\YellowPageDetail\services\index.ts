import { YellowPageSolutionData } from '@/pages/YellowPagesProducts/services';
import { request } from '@umijs/max';

export async function getYelloPageDetail(
  globalId: string,
): Promise<YellowPageSolutionData> {
  return request(`/api/Solutions/Displayed/${globalId}`);
}

export interface ContactInfo {
  name: string;
  phoneNumber: string;
  email: string;
  remark?: string;
  company: string;
  solutionId: number;
  type?: number;
  job?: string;
}

export async function contact(params: ContactInfo) {
  return request('/api/Contacts', { method: 'POST', data: params });
}
