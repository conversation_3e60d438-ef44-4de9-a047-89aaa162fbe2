:root {
  --nav-menu-primary-color: #114bed;
  --nav-menu-text-color: rgba(56, 56, 56, 88%);
}

.nav {
  display: flex;
  gap: 24px;
  margin-left: 30px;
}

.nav-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--nav-menu-text-color);
  text-decoration: none;
  padding: 0 10px;
  position: relative;
  height: 50px;
  cursor: pointer;
  border-bottom: solid 2px transparent;

  a {
    color: var(--nav-menu-text-color);
  }
}

.nav-item-name {
  display: flex;
  align-items: center;
  height: 100%;

  a {
    display: flex;
    align-items: center;
    height: 100%;
  }
}

.nav-item-name-disabled {
  color: var(--nav-menu-text-color);
}

.nav-item.selected {
  background-color: transparent;
  border-bottom: solid 2px var(--nav-menu-primary-color);

  a {
    color: var(--nav-menu-primary-color);
  }
}

.nav-item:hover {
  color: var(--nav-menu-primary-color);

  a {
    color: var(--nav-menu-primary-color);
  }
}

.dropdown {
  position: relative;
}

.dropdown-content,
.dropdown-list {
  position: absolute;
  left: -10px;
  top: 50px;
  min-width: 150px;
  background-color: #fff;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 20%);
  z-index: 999;
  border-radius: 4px;
  padding: 10px 0;
  visibility: hidden;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.dropdown:hover .dropdown-content,
.dropdown:hover .dropdown-list {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.dropdown-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 20px;
  background: transparent;
}

.category-list {
  display: flex;
}

.category {
  flex: 1;
  padding: 10px 15px;
  border-right: 1px solid #f0f0f0;

  .subcategory {
    padding-inline: 0;
    margin-inline: 0;
  }

  .subcategory:hover:not(.selected),
  .subcategory.selected {
    background-color: transparent;
    color: var(--nav-menu-primary-color);
  }
}

.category:last-child {
  border-right: none;
}

.category-title {
  font-weight: bold;
  padding: 8px 0;
  color: var(--nav-menu-text-color);
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;
  white-space: nowrap;
  cursor: pointer;

  &:hover:not(.selected),
  &.selected {
    background-color: transparent;
    color: var(--nav-menu-primary-color);
  }
}

.category-title:hover {
  color: var(--nav-menu-primary-color);
}

.subcategory {
  padding-inline: 16px;
  height: 38px;
  line-height: 38px;
  color: #666;
  font-size: 13px;
  transition: all 0.3s;
  cursor: pointer;
  white-space: nowrap;
}

.subcategory:hover:not(.selected) {
  color: var(--nav-menu-primary-color);
}

.selected {
  color: var(--nav-menu-primary-color) !important;
}

.disabled {
  pointer-events: none;
  cursor: not-allowed;
  color: #d5d5d5;
}
