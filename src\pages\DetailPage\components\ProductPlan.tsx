import { useToggle } from 'ahooks';
import { Flex, InputNumber, Typography } from 'antd';
import { createStyles } from 'antd-style';
import { SolutionEditionAndPricing } from '../services';
import CheckTagGroup from './CheckTag';

interface ProductPlanProps {
  desciption: string;
  areaList: SolutionEditionAndPricing[];
  planList: SolutionEditionAndPricing[];
  areaKey: number | undefined;
  planKey: number | undefined;
  count: number | undefined;
  purchaseLimit: number | undefined;
  disableSelected?: boolean;
  onSelectedInfo: (
    type: 'area' | 'plan' | 'count',
    value: number,
    plan?: SolutionEditionAndPricing,
  ) => void;
}

const useStyles = createStyles(({ css, prefixCls }) => ({
  container: css`
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 20px;
  `,
  description: css`
    min-height: 61px;
    display: flex;
    align-items: center;
    background: linear-gradient(
      to right,
      rgba(45, 140, 240, 0.05),
      rgba(45, 140, 240, 0.02)
    );
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 8px 18px;
    position: relative;
    overflow: hidden;
    .${prefixCls}-typography {
      margin-bottom: 0;
      font-size: 15px;
      color: #333;
      line-height: 1.5;
      font-weight: 400;
    }
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 4px;
      background: linear-gradient(to bottom, #2d8cf0, #36cfc9);
    }
  `,
  planWrapper: css`
    max-height: 272px;
    overflow-y: auto;
    overflow-x: hidden;
    overscroll-behavior: contain;
  `,
  skuChange: css`
    font-size: 14px;
    color: #6b6b6b;
    margin-bottom: 0;
    font-weight: 400;
    line-height: 1.5;
    a {
      color: #0b76dd;
    }
  `,
  inputNumber: css`
    width: 120px;
    height: 40px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 14px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    .${prefixCls}-input-number-input-wrap {
      height: 100%;
      input {
        height: 100%;
      }
    }
    :hover,
    :focus-within {
      border-color: #2d8cf0;
    }
  `,
}));

const ProductPlan: React.FC<ProductPlanProps> = ({
  desciption,
  areaList,
  planList,
  areaKey,
  planKey,
  count,
  purchaseLimit,
  disableSelected = false,
  onSelectedInfo,
}) => {
  const { styles } = useStyles();
  const [state, { toggle }] = useToggle(true);

  return (
    <Flex vertical gap={20} className={styles.container}>
      <div className={styles.description}>
        <Typography.Paragraph ellipsis={{ rows: 2, expandable: 'collapsible' }}>
          {desciption}
        </Typography.Paragraph>
      </div>
      <Flex gap={20} align="center">
        <Typography.Text>售卖区域：</Typography.Text>
        <CheckTagGroup
          disabled={disableSelected}
          items={areaList}
          value={areaKey!}
          onClick={(key) => {
            onSelectedInfo('area', key);
          }}
        />
      </Flex>
      <Flex gap={20}>
        <Typography.Text>购买计划：</Typography.Text>

        <Flex vertical gap={10} style={{ flex: 1 }}>
          <div className={styles.skuChange}>
            请选择商品规格，
            <a
              onClick={(e) => {
                e.preventDefault();
                toggle();
              }}
            >
              {state ? '网格视图' : '列表视图'}
            </a>
            查看。
          </div>
          <div className={styles.planWrapper}>
            <CheckTagGroup
              disabled={disableSelected}
              onClick={(key, item) => {
                onSelectedInfo('plan', key, item);
              }}
              value={planKey!}
              items={planList}
              type={state ? 'grid' : 'list'}
            />
          </div>
        </Flex>
      </Flex>
      <Flex gap={20} align="center">
        <Typography.Text>购买数量：</Typography.Text>
        <InputNumber
          className={styles.inputNumber}
          disabled={disableSelected}
          min={1}
          max={purchaseLimit}
          value={count}
          onChange={(value) => {
            onSelectedInfo('count', value as number);
          }}
        />
      </Flex>
    </Flex>
  );
};

export default ProductPlan;
