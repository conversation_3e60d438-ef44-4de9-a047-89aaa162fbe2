# useScrollAnimation Hook 使用说明

这是一个基于 GSAP 和 ScrollTrigger 的滚动动画 Hook，可以轻松为元素添加滚动进入动画效果。

## 功能特性

- 🎯 **简单易用**: 一行代码即可添加滚动动画
- 🎨 **高度可定制**: 支持自定义动画参数和效果
- 📦 **预设动画**: 提供多种常用动画预设
- 🔧 **TypeScript 支持**: 完整的类型定义
- ⚡ **性能优化**: 基于 GSAP 的高性能动画引擎
- 🎪 **作用域隔离**: 动画只影响指定容器内的元素

## 基础用法

### 1. 最简单的使用方式

```tsx
import { useScrollAnimation } from '@/hooks/useScrollAnimation';

const MyComponent = () => {
  const containerRef = useScrollAnimation();

  return (
    <div ref={containerRef}>
      <div className="animate-item">这个元素会有动画</div>
      <div className="animate-item">这个元素也会有动画</div>
    </div>
  );
};
```

### 2. 自定义参数

```tsx
const containerRef = useScrollAnimation({
  selector: '.my-card',        // 自定义选择器
  initialY: 100,               // 初始Y轴偏移
  duration: 1.2,               // 动画持续时间
  stagger: 0.3,                // 交错延迟
  ease: 'back.out(1.7)',       // 缓动函数
  start: 'top 90%',            // 触发位置
});
```

### 3. 使用预设动画

```tsx
import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';

// 缩放淡入
const containerRef = useScrollAnimation(animationPresets.fadeInScale);

// 从左侧淡入
const containerRef2 = useScrollAnimation(animationPresets.fadeInLeft);

// 快速淡入
const containerRef3 = useScrollAnimation(animationPresets.fadeInFast);
```

### 4. 完全自定义动画

```tsx
const containerRef = useScrollAnimation({
  selector: '.custom-element',
  customAnimation: {
    from: { 
      scale: 0, 
      rotation: 180, 
      opacity: 0 
    },
    to: { 
      scale: 1, 
      rotation: 0, 
      opacity: 1, 
      duration: 1.5,
      stagger: 0.2,
      ease: 'elastic.out(1, 0.3)'
    },
  },
});
```

## API 参数

### ScrollAnimationOptions

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `selector` | `string` | `'.animate-item'` | 动画目标选择器 |
| `start` | `string` | `'top 85%'` | ScrollTrigger 触发位置 |
| `initialY` | `number` | `50` | 初始Y轴偏移量 |
| `initialOpacity` | `number` | `0` | 初始透明度 |
| `duration` | `number` | `0.8` | 动画持续时间（秒） |
| `stagger` | `number` | `0.2` | 元素间的交错延迟（秒） |
| `ease` | `string` | `'power2.out'` | GSAP 缓动函数 |
| `once` | `boolean` | `true` | 是否只触发一次 |
| `customAnimation` | `object` | `undefined` | 自定义动画配置 |

### 预设动画

| 预设名称 | 描述 | 效果 |
|----------|------|------|
| `fadeInUp` | 从下方淡入 | Y轴向上移动 + 透明度变化 |
| `fadeInDown` | 从上方淡入 | Y轴向下移动 + 透明度变化 |
| `fadeInLeft` | 从左侧淡入 | X轴向右移动 + 透明度变化 |
| `fadeInRight` | 从右侧淡入 | X轴向左移动 + 透明度变化 |
| `fadeInScale` | 缩放淡入 | 缩放 + 透明度变化 + 弹性效果 |
| `fadeInFast` | 快速淡入 | 较短的动画时间和距离 |
| `fadeInSlow` | 慢速淡入 | 较长的动画时间和距离 |

## 实际应用示例

### 产品卡片列表

```tsx
const ProductList = () => {
  const containerRef = useScrollAnimation({
    selector: '.product-card',
    initialY: 60,
    duration: 0.8,
    stagger: 0.15,
  });

  return (
    <div ref={containerRef}>
      {products.map(product => (
        <div key={product.id} className="product-card">
          <img src={product.image} alt={product.name} />
          <h3>{product.name}</h3>
          <p>{product.description}</p>
        </div>
      ))}
    </div>
  );
};
```

### 特性介绍区块

```tsx
const Features = () => {
  const containerRef = useScrollAnimation(animationPresets.fadeInScale);

  return (
    <section ref={containerRef}>
      <div className="animate-item">
        <Icon type="speed" />
        <h3>高性能</h3>
        <p>基于GSAP的高性能动画引擎</p>
      </div>
      <div className="animate-item">
        <Icon type="easy" />
        <h3>易使用</h3>
        <p>简单的API，快速上手</p>
      </div>
    </section>
  );
};
```

## 注意事项

1. **选择器作用域**: 动画只会影响容器内的元素，不会影响页面其他部分
2. **性能考虑**: 避免在同一页面使用过多的动画实例
3. **响应式设计**: 在移动设备上可能需要调整动画参数
4. **无障碍访问**: 考虑为用户提供关闭动画的选项

## 常见问题

**Q: 为什么动画没有触发？**
A: 检查选择器是否正确，确保目标元素在容器内部。

**Q: 如何禁用动画？**
A: 可以通过条件渲染或设置 `duration: 0` 来禁用动画。

**Q: 可以同时使用多个动画实例吗？**
A: 可以，但要确保使用不同的选择器避免冲突。
