import { request } from '@umijs/max';

export type SolutionEditionAndPricing = {
  Name: string;
  SalesId: number;
  SolutionId: number;
  AttributesXml: string | null;
  Sku: string;
  OverriddenPrice: number;
  SolutionEditionTypeId: number;
  SolutionEditionType: string | null;
  PeriodTypeId: number;
  PeriodType: string | null;
  PeriodValue: number;
  PeriodUnit: string | null;
  IdeaFor: string | null;
  Users: number;
  Description: string | null;
  DiscountTypeId: number;
  Discount: number;
  SolutionSubTotal: number;
  DiscountName: string | null;
  PurchaseLimit: number;
  Region: number;
  IsMspSku: boolean;
  Currency: string;
  Id: number;
};

export type UserAgreementItem = {
  FileName: string;
  Uri: string;
  AgreementType: number;
};

export type ProductDetail = {
  GlobalId: string;
  ImageUrl: string;
  Name: string;
  ShortDescription: string;
  FullDescription: string;
  FullDescriptionMobile: string;
  EditionsDescription: string | null;
  Success: string;
  Resources: string;
  SolutionSupports: { Name: string; ValueRaw: string }[];
  SolutionEditionsAndPricing: SolutionEditionAndPricing[];
  Related: any[];
  PurchaseParameter: any;
  LogoUrl: string;
  IsApplyForTrial: boolean;
  SupportEmail: string | null;
  IsShelved: boolean;
  HasPurchaseParameters: boolean;
  LimitPurchase: boolean;
  Msps: any[];
  UserAgreement: UserAgreementItem[];
  Id: number;
  BlueCloudSalesEmail: string;
  Categories: string[];
  DeliveryMethod: 0 | 1;
  DeliveryPersonEmail: string;
  DeliveryPersonName: string;
  Supplier: string;
  SupplierId: number;
};

export async function getDetail(globalId: string): Promise<ProductDetail> {
  return request(`/api/Solutions/${globalId}`);
}

export type PurchaseRequest = {
  SolutionGlobalId: string;
  SolutionId: number;
  SolutionEiditionId: number;
  Quantity: number;
};
export async function addCart(params: PurchaseRequest): Promise<any> {
  return request(`/api/ShoppingCarts/items`, {
    method: 'POST',
    data: params,
  });
}

interface Product {
  Id: number;
  GlobalId: string;
  Name: string;
  ShortDescription: string;
  LogoUrl: string;
  IsHot: boolean;
  SalesTotal: number;
  CategoryId: number;
  Rating: number;
  LimitPurchase: boolean;
  ShowedPrice: number;
  Supplier: string;
}
export async function getRelatedSolutions(id: number): Promise<Product[]> {
  return request(`/api/Solutions/${id}/RelatedSolutions`);
}
