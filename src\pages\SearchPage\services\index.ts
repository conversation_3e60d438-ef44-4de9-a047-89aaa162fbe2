import { YellowPageSolutionData } from '@/pages/YellowPagesProducts/services';
import { request } from '@umijs/max';

export type OnlineListItem = {
  Id: number;
  Name: string;
  ShortDescription: string;
  LogoUrl: string;
  GlobalId: string;
  ShowedPrice: number;
  Currency: string;
  LimitPurchase: boolean;
  Supplier: string;
};

export type ListResponse<T> = {
  Data: T[];
  Total: number;
  ExtraData: string;
  Errors: string;
};
export type SearchParams = {
  Keywords?: string;
  Page: number;
  PageSize: number;
  DeliveryMethod?: string;
  CategoryId?: string;
  Supplier?: string;
};
export async function searchOnline(
  params: SearchParams,
): Promise<ListResponse<OnlineListItem>> {
  return request(`/api/Solutions`, {
    method: 'GET',
    params: params,
  });
}

export async function searchConsult(
  params: SearchParams,
): Promise<ListResponse<YellowPageSolutionData>> {
  return request(`/api/Solutions/Displayed`, {
    method: 'GET',
    params: params,
  });
}
