{"private": true, "author": "Sun<PERSON><PERSON>ao <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.4.4", "@gsap/react": "^2.1.2", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@types/qs": "^6.9.18", "@umijs/max": "^4.4.2", "ahooks": "^3.8.4", "antd": "^5.26.3", "antd-style": "^3.7.1", "big.js": "^6.2.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "derive-valtio": "^0.2.0", "dompurify": "^3.2.4", "gsap": "^3.13.0", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "pdfmake-support-chinese-fonts": "^1.0.3", "qs": "^6.14.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "valtio": "^2.1.5", "valtio-persist": "^2.2.4"}, "devDependencies": {"@types/big.js": "^6.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "lodash-es": "^4.17.21", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}