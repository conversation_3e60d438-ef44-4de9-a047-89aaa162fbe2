export interface SolutionDetailModel {
  GlobalId: string;
  ImageUrl: string;
  Name: string;
  ShortDescription: string;
  FullDescription: string;
  FullDescriptionMobile: string;
  EditionsDescription: string;
  Success: string;
  Resources: string;
  SolutionSupports:	SolutionSupportModel[];
  SolutionEditionsAndPricing:	SolutionEditionsAndPricingModel[];
  Related: [];
  PurchaseParameter: [];
  LogoUrl: string;
  IsApplyForTrial: boolean;
  SupportEmail: string;
  IsShelved: boolean;
  HasPurchaseParameters: boolean;
  LimitPurchase: boolean;
  Msps: MspItemModel[];
  Id:	number;
  Region: number;
  UserAgreement: UserAgreementModel[];
}

export interface UserAgreementModel {
  FileName: string;
  Uri: string;
}

export interface MspItemModel {
  Id: number;
  CompanyName: string;
  UserName: string;
  SolutionMspServices: SolutionMspServiceModel[];
}

export interface SolutionMspServiceModel {
  Id: number;
  Name: string;
  Skus: SolutionMspServiceSkuModel[];
}

export interface SolutionMspServiceSkuModel {
  Name: string;
  SolutionId: number;
  AttributesXml: string;
  Sku: string;
  OverriddenPrice: number;
  SolutionEditionTypeId: number;
  SolutionEditionType: string;
  PeriodTypeId: number;
  PeriodType: string;
  PeriodValue: number;
  PeriodUnit: string;
  IdeaFor: string;
  Users: number;
  Description: string;
  DiscountTypeId: number;
  Discount: number;
  SolutionSubTotal: number;
  DiscountName: string;
  Region: number;
  IsMspSku: boolean;
  Currency: string;
  Id: number;
}

export interface SolutionSupportModel {
  SolutionProfileId: number;
  Name: string;
  ValueRaw: string;
  Id: number;
  Form: string;
  CustomProperties: any;
}

export interface SolutionEditionsAndPricingModel {
  Name: string;
  SolutionId: number;
  AttributesXml: string;
  Sku: string;
  OverriddenPrice: number;
  SolutionEditionTypeId: number;
  SolutionEditionType: string;
  PeriodTypeId: number;
  PeriodType: string;
  PeriodValue: number;
  PeriodUnit: string;
  IdeaFor: string;
  Users: number;
  Description: string;
  DiscountTypeId: number;
  Discount: number;
  SolutionSubTotal: number;
  DiscountName: string;
  Id: number;
  Form: string;
  CustomProperties: any;
  Quantity: number;
  Region: number;
  EditionId: number;
  IsMspSku: boolean;
  Currency: string;
}

export interface SolutionModel {
  IsHot: boolean;
  LogoUrl: string;
  ShortDescription: string;
  SalesTotal: number;
  LimitPurchase: boolean;
  Id: number;
  Name: string;
  CategoryId: number;
  GlobalId: string;
}

export interface CommitModel {
  TotalRating: number;
  Data: CommitItemModel[];
  Total: number;
  ExtraData: any;
  Errors: any;
}

export interface CommitItemModel {
  CommentText: string;
  CreatedOn: string;
  Id: number;
  Rating: number;
  SolutionId: number;
  UserEmail: string;
  UserRole: string;
}

export interface MspServiceDetailModel {
  Name: string;
  Contact: string;
  CretFiles: CretFileModel[];
  Description: string;
  Id: number;
  Items: string;
  MspServiceSkus: MspDetailServiceSkuModel[];
}

export interface CretFileModel {
  AgreementType: number;
  FileName: string;
  Uri: string;
}

export interface MspDetailServiceSkuModel {
  Currency: string;
  Description: string;
  Name: string;
  Price: number;
  Sku: string;
  SkuRegion: number;
  TaxRatePercent: number;
  Unit: string;
}

export const Regions = [
  {
    label: '中国大陆',
    value: 1,
    name: 'Mainland',
    currency: '¥'
  },
  {
    label: '中国香港',
    value: 2,
    name: 'HongKong',
    currency: 'HK$'
  },
  {
    label: '中国台湾',
    value: 3,
    name: 'Taiwan',
    currency: 'NT$'
  },
  {
    label: '中国澳门',
    value: 4,
    name: 'Macao',
    currency: 'MOP$'
  }
];
