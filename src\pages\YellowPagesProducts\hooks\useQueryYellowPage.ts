import useObjectState from '@/hooks/useObjectState';
import { useRequest } from 'ahooks';
import { getYellowPagesList, YellowPagesListParams } from '../services';

const useQueryYellowPage = () => {
  const PAGE_SIZE = 20;
  const [searchValues, updateSearchValues] =
    useObjectState<YellowPagesListParams>({
      CategoryId: undefined,
      DeliveryMethod: 'all', // 交付类型
      Supplier: undefined,
      Page: 0,
      PageSize: PAGE_SIZE,
    });

  const { data, loading } = useRequest(() => getYellowPagesList(searchValues), {
    refreshDeps: [JSON.stringify(searchValues)],
    debounceWait: 300,
  });

  const setPage = (page: number) => {
    updateSearchValues({ Page: page });
  };

  const setSupplier = (supplier: string) => {
    updateSearchValues({ Supplier: supplier });
  };

  return {
    yellopageList: data,
    loading,
    searchValues,
    updateSearchValues,
    setPage,
    setSupplier,
  };
};

export default useQueryYellowPage;
