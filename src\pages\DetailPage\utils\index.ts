import { Regions } from '@/constants';
import { SolutionEditionAndPricing } from '../services';

export function filterValidModels(
  solutionEditionsAndPricing: SolutionEditionAndPricing[],
  region: number,
) {
  return solutionEditionsAndPricing.filter((item) => item.Region === region);
}
export function getValidRegions(list: SolutionEditionAndPricing[]) {
  if (!list) {
    return [];
  }
  const map = new Map<number, any>();
  list.forEach((item) => {
    const items = Regions.filter((subitem) => subitem.Id === item.Region);
    if (!map.has(item.Region) && items.length > 0) {
      map.set(item.Region, items[0]);
    }
  });
  return Array.from(map.values());
}

export function isValidUrl(url: string) {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}
