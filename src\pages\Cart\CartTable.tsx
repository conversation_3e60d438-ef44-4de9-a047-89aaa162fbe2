import ColorTag from '@/components/ColorTag';
import useUserStore from '@/store/user';
import { CartItemModel, Regions } from '@/types';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { useModel, useNavigate, useRequest } from '@umijs/max';
import {
  Button,
  Form,
  Popconfirm,
  Select,
  Table,
  TableColumnsType,
  TableProps,
  Tooltip,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
import InputNum from './component/InputNum';
import { deleteCartItem, getCart, putCartItemsQuantity } from './services';
const useStyles = createStyles(({ css }) => ({
  overlayImage: css`
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background-color: rgba(200, 200, 200, 0.5); /* 半透明灰色 */
  `,
  antTableCell: css`
    max-width: 200px; /* 根据需求调整 */
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  `,
}));
const CartTable = () => {
  const { user } = useUserStore();
  const { styles } = useStyles();
  const navigate = useNavigate();
  const [selectedDeliveryMethod, setSelectedDeliveryMethod] =
    useState<number>(-1); // 当前选中类型
  const [selecteBrand, setSelecteBrand] = useState<number>(0); // 当前选中品牌
  const { cart, setCart } = useModel('Cart.cartModel', (model) => ({
    cart: model.cart,
    setCart: model.setCart,
  }));
  const { refreshCartCount, setCartRegion, cartRegion } = useModel('cart');
  useEffect(() => {
    if (cart.length > 0) {
      setSelecteBrand(cart[0].BrandZone.Id ?? 0);
      setSelectedDeliveryMethod(cart[0].DeliveryMethod ?? -1);
    } else {
      setSelecteBrand(0);
      setSelectedDeliveryMethod(-1);
    }
  }, [cart]);
  // todo: 搜索功能,参数
  const { data, loading, mutate, refresh } = useRequest(
    () => getCart(cartRegion),
    {
      refreshDeps: [cartRegion],
      formatResult: (res) => {
        return res.Items;
      },
    },
  );
  const handleDelete = (Id: number) => {
    setCart(cart.filter((item) => item.Id !== Id));
    deleteCartItem(Id).then(() => {
      refresh();
      refreshCartCount();
    });
  };
  const columns: TableColumnsType<CartItemModel> = [
    {
      dataIndex: 'PictureUrl',
      render: (value, item) => {
        return (
          <a
            style={{ position: 'relative' }}
            onClick={() => {
              goSolutionDetail(item.SolutionGlobalId);
            }}
          >
            <img src={value} style={{ height: 80, width: 80 }}></img>
            {!item.IsActive && (
              <img
                src={require('@/assets/offshelves.png')}
                className={styles.overlayImage}
              ></img>
            )}
          </a>
        );
      },
    },
    {
      title: '商品名称',
      dataIndex: 'SolutionName',
      render: (value, item) => {
        return (
          <div>
            <div>
              <Tooltip title={value}>
                <a
                  style={{
                    display: 'inline-block',
                    maxWidth: '150px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: '#000',
                  }}
                  onClick={() => {
                    goSolutionDetail(item.SolutionGlobalId);
                  }}
                >
                  {value}
                </a>
              </Tooltip>
            </div>
            <div>
              {item.DeliveryMethod === 1 ? (
                <ColorTag
                  textColor="rgb(0, 186, 173)"
                  backgroundColor="rgba(77, 219, 210, 0.2)"
                  style={{ fontSize: 12, fontWeight: 400 }}
                >
                  SAAS
                </ColorTag>
              ) : (
                <ColorTag
                  textColor="#2D8CF0"
                  backgroundColor="#e9f3ff"
                  style={{ fontSize: 12, fontWeight: 400 }}
                >
                  软件
                </ColorTag>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: '商品SKU',
      dataIndex: 'Name',
      render: (value, record) => {
        return (
          <>
            <div>{record.Name}</div>
            {/* <div>SKU: {record.Sku}</div> */}
          </>
        );
      },
    },
    // {
    //   title: '交付方式',
    //   dataIndex: 'DeliveryMethod',
    //   render: (value) => {
    //     return value===1?"SAAS":"软件"
    //   }
    // },
    {
      title: '品牌',
      dataIndex: 'BrandZone',
      render: (value) => {
        return value.BrandName;
      },
    },
    {
      title: '商品单价',
      dataIndex: 'UnitPrice',
      render: (value) => {
        return '￥' + formatWithThousandSeparator(value);
      },
    },
    {
      title: '购买数量',
      dataIndex: 'Quantity',
      render: (value, record) => {
        return (
          <InputNum
            maxValue={
              record.Editions?.find((c) => c.Id === record.EditionId)
                ?.PurchaseLimit ?? 1
            }
            value={value}
            disabled={!record.IsActive}
            onChange={(value) => {
              const index = cart.findIndex(
                (item) => item.SolutionId === record.SolutionId,
              );
              if (index !== -1) {
                setCart(
                  cart.map((item) => {
                    if (item.Id === record.Id) {
                      item.Quantity = value;
                    }
                    return item;
                  }),
                );
              }
              mutate(
                data.map((item: any) => {
                  if (item.Id === record.Id) {
                    item.Quantity = value;
                  }
                  return item;
                }),
              );
              putCartItemsQuantity(record.Id, value).then((res) => {
                refresh();
              });
            }}
          />
        );
      },
    },
    {
      title: '小计',
      dataIndex: 'SubTotal',
      render: (value) => {
        return '￥' + formatWithThousandSeparator(value);
      },
    },

    {
      title: '操作',
      dataIndex: 'Id',
      render: (value) => {
        return (
          <Popconfirm
            title="确定删除吗？"
            onConfirm={() => handleDelete(value)}
          >
            <Button type="text" danger>
              删除
            </Button>
          </Popconfirm>
        );
      },
    },
  ];
  const goSolutionDetail = (solutionGlobalId: string) => {
    navigate({
      pathname: `/solution/detail/${solutionGlobalId}`,
    });
  };
  const rowSelection: TableProps<CartItemModel>['rowSelection'] = {
    selectedRowKeys: cart.map((item) => item.Id),
    onChange: (selectedRowKeys, selectedRows) => {
      setCart(selectedRows);
      setSelecteBrand(selectedRows[0]?.BrandZone.Id ?? 0);
      setSelectedDeliveryMethod(selectedRows[0]?.DeliveryMethod ?? -1);
    },
    renderCell: (checked, record, index, originNode) => {
      if (!record.IsActive) {
        return (
          <Tooltip title={'产品已下架'}>
            <span>{originNode}</span>
          </Tooltip>
        );
      } else if (selecteBrand !== 0 && selecteBrand !== record.BrandZone.Id) {
        return (
          <Tooltip title={'请选择相同的品牌'}>
            <span>{originNode}</span>
          </Tooltip>
        );
      } else if (
        selectedDeliveryMethod !== -1 &&
        selectedDeliveryMethod !== record.DeliveryMethod
      ) {
        return (
          <Tooltip title={'请选择相同的交付方式'}>
            <span>{originNode}</span>
          </Tooltip>
        );
      } else {
        return <span>{originNode}</span>;
      }
    },
    getCheckboxProps: (record: CartItemModel) => ({
      disabled:
        !record.IsActive ||
        (selecteBrand !== 0 && selecteBrand !== record.BrandZone.Id) ||
        (selectedDeliveryMethod !== -1 &&
          selectedDeliveryMethod !== record.DeliveryMethod),
      name: record.SolutionName,
    }),
  };
  return (
    <div>
      <Form
        onValuesChange={(changedValues, allValues) => {
          setCartRegion(allValues.region);
          setCart([]);
        }}
        initialValues={{ region: cartRegion }}
      >
        <Form.Item label="地区" name="region">
          <Select
            style={{ width: 200 }}
            options={Regions.filter((item) =>
              user?.Regions.includes(item.value),
            ).map((c) => {
              return { value: c.value, label: c.label };
            })}
          ></Select>
        </Form.Item>
      </Form>
      <Table<CartItemModel>
        pagination={false}
        rowKey={(row) => row.Id}
        columns={columns}
        loading={loading}
        dataSource={data}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
          hideSelectAll: true,
        }}
      />
    </div>
  );
};

export default CartTable;
