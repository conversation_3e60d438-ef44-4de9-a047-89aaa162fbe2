import {
  postBillOfflineInformation,
  postOfflineInformation,
} from '@/services/order/PurchaseController';
import { formatDate } from '@/utils/format';
import { CheckOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, DatePicker, Divider, Modal, Row } from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import { forwardRef, useImperativeHandle, useState } from 'react';
const dateFormat = 'YYYY-MM-DD';
const useStyles = createStyles(({ css }) => ({
  paymentOfflineInfo: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 36px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    vertical-align: top;
  `,
  paymentOfflineAlert: css`
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 20.27px;
    color: rgb(222, 50, 50);
    text-align: left;
    vertical-align: top;
    margin-top: 16px;
  `,
}));
interface IOfflinePayment {
  confirmCallBack: () => void;
}
const OfflinePayment = forwardRef(
  (props: IOfflinePayment, ref: React.Ref<any>) => {
    const { confirmCallBack } = props;
    const { styles } = useStyles();
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [isPay, setIsPay] = useState<boolean>(false);
    const [payDate, setPayDate] = useState<any>(null);
    const [paymentNumber, setPaymentNumber] = useState<string>('');
    const [type, setType] = useState<'pay' | 'bill'>('pay');
    const [InstallmentNumber, setInstallmentNumber] = useState<string>('');
    const [billNumber, setBillNumber] = useState<string>('');
    const [orderNumber, setOrderNumber] = useState<string>('');
    useImperativeHandle(ref, () => ({
      modalHandleShow,
    }));
    const modalHandleShow = (
      type: 'pay' | 'bill',
      data: {
        paymentNumber?: string;
        customOrderNumber?: string;
        billNumber?: string;
        installmentNumber?: string;
      },
      isPay = true,
    ) => {
      console.log('modalHandleShow', type, data, isPay);
      setType(type);
      if (type === 'pay') {
        setPaymentNumber(data.paymentNumber ?? '');
        setOrderNumber(data.customOrderNumber ?? '');
        setInstallmentNumber(data.installmentNumber ?? '');
      } else {
        setBillNumber(data.billNumber!);
      }
      setIsModalOpen(true);
      setIsPay(isPay);
    };
    const modalHandleOk = () => {
      if (!isPay) {
        setIsModalOpen(false);
        return;
      }
      if (type === 'bill') {
        setConfirmLoading(true);
        postBillOfflineInformation(billNumber, {
          paidOn: payDate,
        }).then(
          (res) => {
            setConfirmLoading(false);
            setIsModalOpen(false);
            if (confirmCallBack) {
              confirmCallBack();
            }
          },
          () => {
            setConfirmLoading(false);
          },
        );
      } else {
        setConfirmLoading(true);
        postOfflineInformation(paymentNumber, {
          OrderNumber: orderNumber,
          paidOn: payDate,
          InstallmentNumber: InstallmentNumber,
        }).then(
          (res) => {
            setConfirmLoading(false);
            setIsModalOpen(false);
            if (confirmCallBack) {
              confirmCallBack();
            }
          },
          () => {
            setConfirmLoading(false);
          },
        );
      }
    };
    const modalHandleCancel = () => {
      setIsModalOpen(false);
    };
    const modalFonter = () => (
      <>
        <Divider />
        <div style={{ textAlign: 'right' }}>
          {isPay && (
            <Button
              color="primary"
              variant="outlined"
              style={{ width: 80 }}
              onClick={modalHandleCancel}
            >
              取消
            </Button>
          )}
          <Button
            loading={confirmLoading}
            type="primary"
            disabled={payDate === null && isPay}
            icon={<CheckOutlined />}
            style={{ width: 80, marginLeft: 16 }}
            onClick={modalHandleOk}
          >
            确认
          </Button>
        </div>
      </>
    );

    return (
      <Modal
        destroyOnHidden={false}
        title={isPay ? '请确认您的付款信息' : '线下付款指南'}
        open={isModalOpen}
        width={'50%'}
        footer={modalFonter}
        onCancel={modalHandleCancel}
      >
        <Divider />
        <div className={styles.paymentOfflineInfo}>
          <div>
            {isPay
              ? '请确认已向以下蓝云账户收款信息'
              : '请向以下账户完成转账付款'}
          </div>
          <div>账户名称：上海蓝云网络科技有限公司</div>
          <div>账户名称：中国工商银行国航大厦支行</div>
          <div>开户行帐号：0200227919200053244</div>
        </div>
        {isPay && (
          <Row className={styles.paymentOfflineInfo}>
            <Col>付款日期：</Col>
            <Col>
              {' '}
              <DatePicker
                onChange={(v) => {
                  if (v) {
                    setPayDate(v.format('YYYY-MM-DD'));
                  } else {
                    setPayDate(null);
                  }
                }}
                format={dateFormat}
                maxDate={dayjs(formatDate(new Date()), dateFormat)}
              />
            </Col>
          </Row>
        )}

        <div className={styles.paymentOfflineAlert}>
          {!isPay &&
            '线下转账付款完成后，进入“订单管理”页或“订单详情”页进行付款确认。'}
        </div>
      </Modal>
    );
  },
);

export default OfflinePayment;
