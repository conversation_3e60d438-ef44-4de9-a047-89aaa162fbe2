import { transformToTree } from '@/components/CategoriesTree';
import SearchPageContent from '@/components/SearchPageContent';
import { useCreation } from 'ahooks';
import { memo } from 'react';
import {
  ListResponse,
  OnlineListItem,
  searchOnline,
} from '../SearchPage/services';
import SearchCard from './components/SearchCard';
import useSearch from './hooks/useSearch';
import useVnetCategroies from './hooks/useVnetCategroies';
import { vnetCategroies } from './services';

const Online = () => {
  const { data, loading, searchValues, updateState } =
    useSearch<ListResponse<OnlineListItem>>(searchOnline);

  const { categories } = useVnetCategroies(vnetCategroies);

  const categoriesTreeData = useCreation(() => {
    return transformToTree(categories);
  }, [categories]);

  return (
    <SearchPageContent
      to="solution"
      searchPanel={
        <SearchCard
          categoriesTreeData={categoriesTreeData}
          searchValues={searchValues}
          updateSearchValues={updateState}
        />
      }
      searchParams={searchValues}
      updateSearchParams={updateState}
      dataSource={data?.Data || []}
      total={data?.Total || 0}
      loading={loading}
    />
  );
};

export default memo(Online);
