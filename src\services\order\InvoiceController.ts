import { request } from '@umijs/max';

export async function getInvoices(params: {
  PageIndex: number;
  PageSize: number;
  CustomOrderNumber?: string;
  TitleCompanyName?: string;
}) {
  return request(`/api/Orders/invoices`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
export async function postInvoice(body: any, options?: { [key: string]: any }) {
  return request('/api/Customers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function putCustomers(
  id: number,
  body?: any,
  options?: { [key: string]: any },
) {
  return request(`/api/Customers/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function deleteCustomer(
  ids: number[],
  options?: { [key: string]: any },
) {
  return request(`/api/Customers`, {
    method: 'DELETE',
    data: ids,
    ...(options || {}),
  });
}
export async function getInvoiceTitle(params: any) {
  return request(`/api/fptaitou/search`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
export async function postInvoiceTitle(
  body: any,
  options?: { [key: string]: any },
) {
  return request('/api/fptaitou', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function putInvoiceTitle(
  id: number,
  body?: any,
  options?: { [key: string]: any },
) {
  return request(`/api/fptaitou/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function deleteInvoiceTitle(
  ids: number[],
  options?: { [key: string]: any },
) {
  return request(`/api/fptaitou`, {
    method: 'DELETE',
    data: ids,
    ...(options || {}),
  });
}
export async function getInvoiceAddress(params: any) {
  return request(`/api/fpdizhi/search`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
export async function postInvoiceAddress(
  body: any,
  options?: { [key: string]: any },
) {
  return request('/api/fpdizhi', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function putInvoiceAddress(
  id: number,
  body?: any,
  options?: { [key: string]: any },
) {
  return request(`/api/fpdizhi/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function deleteInvoiceAddress(
  ids: number[],
  options?: { [key: string]: any },
) {
  return request(`/api/fpdizhi`, {
    method: 'DELETE',
    data: ids,
    ...(options || {}),
  });
}

export async function getInvoiceDetail(customOrderNumber: string) {
  return request(`/api/Orders/${customOrderNumber}/invoices`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export const addInvoice = async (customOrderNumber: string, data: any) => {
  return request(`/api/Orders/${customOrderNumber}/invoices`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  });
};
