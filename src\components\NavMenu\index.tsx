import { useMemoizedFn } from 'ahooks';
import clsx from 'clsx';
import { FC, ReactNode } from 'react';
import style from './style.less';

export interface NavMenuItem {
  name: ReactNode | string;
  key: string;
  block?: boolean;
  disabled?: boolean;
  children?: NavMenuItem[];
}

export interface NavMenuProps {
  items: NavMenuItem[] | undefined;
  selectedKeys: string[];
  onSelect: ({ key, keyPath }: { key: string; keyPath: string[] }) => void;
}
const NavMenu: FC<NavMenuProps> = ({ items, onSelect, selectedKeys }) => {
  const handleClick = (node: NavMenuItem, currentKeyPath: string[]) => {
    onSelect({ key: node.key, keyPath: currentKeyPath });
  };

  const isActive = (
    currentKeyPath: string[],
    selectedKeyPath: string[],
  ): boolean => {
    return (
      selectedKeyPath.length >= currentKeyPath.length &&
      currentKeyPath.every((key, i) => key === selectedKeyPath[i])
    );
  };

  const renderItems = useMemoizedFn(() => {
    const fun = (
      list: NavMenuItem[],
      levelOne: boolean = true,
      parentKeys: string[] = [],
    ) => {
      if (!list?.length) return;

      /* 计算当前层级是否要做 Grid 分栏（仅对非 block 的 children 生效） */
      return list.map((item, idx) => {
        const currentKeyPath = [...parentKeys, item.key];
        const isItemActive = isActive(currentKeyPath, selectedKeys);

        /* ---------- 有子节点 ---------- */
        if (item.children && item.children.length > 0) {
          /* ====== block 模式 ====== */
          if (item.block) {
            return (
              <div
                key={item.key}
                className={clsx(
                  style['nav-item'],
                  style.dropdown,
                  isItemActive && style.selected,
                )}
              >
                <div className={style['nav-item-name']}>{item.name}</div>
                <div className={style['dropdown-content']}>
                  <div className={style['category-list']}>
                    {item.children.map((child, childIdx) => {
                      const childKeyPath = [...currentKeyPath, child.key];
                      return (
                        <div className={style.category} key={childIdx}>
                          <div
                            className={clsx(
                              style['category-title'],
                              isActive(childKeyPath, selectedKeys) &&
                              style.selected,
                              child.disabled && style.disabled,
                            )}
                            onClick={() => handleClick(child, childKeyPath)}
                          >
                            {child.name}
                          </div>
                          {child.children?.length
                            ? fun(child.children, false, childKeyPath)
                            : null}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          }

          /* ====== 普通下拉 + Grid 分栏 ====== */
          /* ========== 内层 children 分栏 + 竖线 ========== */
          /* ========== 内层：每列最多 10 个 ========== */
          const innerLen = item.children.length;
          const maxRow = Math.min(innerLen, 10); // ✅ 动态行数
          const columns = Math.ceil(innerLen / maxRow);
          const needLine = columns > 1;

          /* 新增按钮样式：独占一行 */
          const addBtnStyle: React.CSSProperties = {
            gridColumn: '1 / -1', // 横跨所有列
            padding: '10px 16px',
          };

          const menuStyle: React.CSSProperties = {
            display: 'grid',
            gridAutoFlow: 'column', // 先竖后横
            gridTemplateRows: `repeat(${maxRow}, 1fr)`, // 固定 10 行
            gridTemplateColumns: `repeat(${columns}, 1fr)`,
          };

          return (
            <div
              key={item.key}
              className={clsx(
                style['nav-item'],
                style.dropdown,
                isItemActive && style.selected,
              )}
            >
              <div className={style['nav-item-name']}>{item.name}</div>

              
                <div className={style['dropdown-list']} style={menuStyle}>
                  {item.key === 'brand' && <div style={addBtnStyle}>蓝云云助手</div>}
                  {item.children.map((child, idx) => {
                    const childKeyPath = [...currentKeyPath, child.key];

                    const isLastColumn = idx >= (columns - 1) * maxRow; // 最后一列元素
                    const childStyle: React.CSSProperties = {
                      /* 多列且非最后一列 → 右边框 */
                      borderRight:
                        needLine && !isLastColumn
                          ? '1px solid #e5e7eb'
                          : undefined,
                    };

                    return (
                      <div
                        key={child.key}
                        style={childStyle}
                        className={clsx(
                          style.subcategory,
                          isActive(childKeyPath, selectedKeys) && style.selected,
                          child.disabled && style.disabled,
                        )}
                        onClick={() => handleClick(child, childKeyPath)}
                      >
                        {child.name}
                      </div>
                    );
                  })}
                </div>
              
            </div>
          );
        }

        /* ---------- 叶子节点 ---------- */
        return (
          <div
            key={item.key}
            className={clsx(
              levelOne ? style['nav-item'] : style.subcategory,
              isItemActive && style.selected,
              item.disabled && style.disabled,
            )}
            onClick={() => handleClick(item, currentKeyPath)}
          >
            <div className={style['nav-item-name']}>{item.name}</div>
          </div>
        );
      });
    };

    return fun(items || []);
  });
  return <div className={style.nav}>{renderItems()}</div>;
};

export default NavMenu;
