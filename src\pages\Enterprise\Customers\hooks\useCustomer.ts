import { useBoolean, useRequest, useSafeState } from 'ahooks';
import {
  addCustomer,
  deleteCustomer,
  getCustomersList,
  updateCustomer,
} from '../services';

const useCustomer = () => {
  // 弹窗
  const [state, { setTrue, setFalse }] = useBoolean(false);
  // 编辑弹窗
  const [searchText, setSearchText] = useSafeState<string>('');
  // 当前页
  const [PageIndex, setPageIndex] = useSafeState<number>(1);
  // 表格选中项
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);

  // 获取客户列表
  const {
    data: list,
    loading: listLoading,
    refresh,
  } = useRequest(
    () =>
      getCustomersList({
        PageIndex,
        PageSize: 10,
        Region: 1, // TODO: 获取当前用户所在地区
        SearchText: searchText,
      }),
    {
      refreshDeps: [searchText],
      debounceWait: 500,
    },
  );

  // 添加客户
  const { run: addItem, loading: addLoading } = useRequest(addCustomer, {
    manual: true,
    onSuccess: () => {
      setFalse();
      refresh();
    },
  });

  // 删除客户
  const { run: deleteItem, loading: deleteLoading } = useRequest(
    () => deleteCustomer(selectedRowKeys as number[]),
    {
      manual: true,
      onSuccess: () => {
        setSelectedRowKeys([]);
        refresh();
      },
    },
  );

  // 更新客户
  const { run: updateItem, loading: updateLoading } = useRequest(
    updateCustomer,
    {
      manual: true,
      onSuccess: () => {
        setFalse();
        refresh();
      },
    },
  );

  return {
    list,
    listLoading,
    addItem,
    addLoading,
    deleteItem,
    deleteLoading,
    updateItem,
    updateLoading,
    state,
    setTrue,
    setFalse,
    searchText,
    setSearchText,
    PageIndex,
    setPageIndex,
    selectedRowKeys,
    setSelectedRowKeys,
  };
};

export default useCustomer;
