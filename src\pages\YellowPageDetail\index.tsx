import ErrorImage from '@/components/ErrorImage';
import NotFound from '@/components/NotFound';
import RichTextViewer from '@/components/RichTextViewer';
import { Link, useParams } from '@umijs/max';
import { Anchor, Col, Flex, Row, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { useState } from 'react';
import ContactForm from './components/ContactForm';
import FlipCard from './components/FlipCard';
import useYellowPageDetail from './hooks/useYellowPageDetail';

const useStyles = createStyles(({ css, prefixCls }) => ({
  container: css`
    // min-height: calc(100vh - 50px);
  `,
  banner: css`
    width: 100%;
    height: 400px;
    padding: 0 10%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #e6f7ff 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
  `,
  productIntro: css`
    flex: 2;
    padding-right: 30px;
    position: relative;
    z-index: 2;
  `,
  productTitle: css`
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.2;
  `,
  productSubTitle: css`
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
    width: 90%;
  `,
  actionButtons: css`
    display: flex;
    margin-top: 25px;
    align-items: center;
  `,
  supplierInfo: css`
    display: inline-block;
    color: #666;
    font-size: 14px;
    margin-left: 16px;
    font-weight: 600;
    a {
      text-decoration: none;
      color: #6b8eff;
    }
  `,
  productImage: css`
    flex: 1;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
    img {
      max-width: 100%;
      max-height: 300px;
    }
  `,
  tabsContainer: css`
    position: sticky;
    z-index: 99;
    top: 38px;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    .ant-anchor-link .ant-anchor-link-title {
      padding: 15px 30px;
    }
  `,
  detailContainer: css`
    background-color: #fff;
  `,
  detailGrid: css`
    padding: 30px 10%;
    background-color: #fff;
    margin-bottom: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
  `,
  sectionHeader: css`
    font-size: 32px;
    font-weight: bold;
    color: #4285f4;
    margin-bottom: 30px;
    text-align: center;
  `,
  active: css`
    color: #4285f4;
  `,
  caseGrid: css`
    margin-top: 20px;
  `,
}));

const YellowPageDetail = () => {
  const { styles } = useStyles();
  const params = useParams();

  const [activeAncherKey, setActiveAncherKey] = useState('#detail');
  const getCurrentAnchor = () => activeAncherKey;

  const { detail, loading } = useYellowPageDetail(params.globalId as string);

  const anchorTitle = [
    {
      key: 'detail',
      href: '#detail',
      title: (
        <span className={activeAncherKey === '#detail' ? styles.active : ''}>
          方案详情
        </span>
      ),
    },
    ...(detail?.SuccessCases && detail.SuccessCases.length > 0
      ? [
          {
            key: 'success',
            href: '#success',
            title: '成功案例',
          },
        ]
      : []),
    {
      key: 'support',
      href: '#support',
      title: (
        <span className={activeAncherKey === '#support' ? styles.active : ''}>
          商务支持
        </span>
      ),
    },
  ];

  if (loading) {
    return (
      <Skeleton
        active
        style={{ minHeight: 'calc(100vh - 400px)' }}
        loading={loading}
      />
    );
  }

  if (!detail) {
    return <NotFound jumpType="back" />;
  }

  return (
    <div className={styles.container}>
      <div className={styles.banner}>
        <div className={styles.productIntro}>
          <div className={styles.productTitle}>{detail?.SolutionName}</div>
          <div className={styles.productSubTitle}>{detail?.Description}</div>
          <div className={styles.actionButtons}>
            <ContactForm solutionId={detail?.Id} />
            <span className={styles.supplierInfo}>
              <Link
                to={{
                  pathname: `/supplier/${detail.SupplierId}/consultation`,
                  search: `n=${detail?.Supplier}`,
                }}
              >
                {detail?.Supplier}
              </Link>
            </span>
          </div>
        </div>
        <div className={styles.productImage}>
          <ErrorImage
            src={detail?.MainImage as string}
            alt={detail?.SolutionName as string}
          />
        </div>
      </div>
      <Flex justify="center" align="center" className={styles.tabsContainer}>
        <Anchor
          bounds={10}
          direction="horizontal"
          targetOffset={68}
          items={anchorTitle}
          getCurrentAnchor={getCurrentAnchor}
          onChange={setActiveAncherKey}
        />
      </Flex>
      <div className={styles.detailContainer}>
        <div id="detail" className={styles.detailGrid}>
          <h2 className={styles.sectionHeader}>产品详情</h2>
          <RichTextViewer htmlContent={detail?.Detail || ''} />
        </div>
        {detail?.SuccessCases.length > 0 && (
          <div id="success" className={styles.detailGrid}>
            <h2 className={styles.sectionHeader}>成功案例</h2>
            <Row
              gutter={[24, 24]}
              justify={'center'}
              className={styles.caseGrid}
            >
              {detail?.SuccessCases?.map((item, index) => (
                <Col key={index} span={8}>
                  <FlipCard
                    title={item.Title}
                    scenario={item.Scenario}
                    outcome={item.Outcome}
                  />
                </Col>
              ))}
            </Row>
          </div>
        )}
        <div id="support" className={styles.detailGrid}>
          <h2 className={styles.sectionHeader}>商务支持</h2>
          <RichTextViewer htmlContent={detail?.CustomerSupport || ''} />
        </div>
      </div>
    </div>
  );
};

export default YellowPageDetail;
