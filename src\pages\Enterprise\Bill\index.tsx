import { ReactComponent as SearchSV<PERSON> } from '@/assets/search.svg';
import { ReactComponent as UnpaidSVG } from '@/assets/unpaid.svg';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { BillModel, getBillssList, getBillsSummary } from './services';

import CustomCollapse from '@/components/CustomCollapse';
import useUserStore from '@/store/user';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { formatDate } from '@/utils/format';
import { useNavigate } from '@umijs/max';
import {
  Button,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  notification,
  Row,
  Select,
  Space,
  Table,
  TableProps,
} from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import React, { useEffect, useRef, useState } from 'react';

dayjs.extend(utc);
dayjs.extend(timezone);

const OfflinePayment = React.lazy(() => import('@/components/OfflinePayment'));
const BillProtocol = React.lazy(() => import('@/components/BillProtocol'));
const { RangePicker } = DatePicker;
const useStyles = createStyles(({ css }) => ({
  search: css`
    border-radius: 16px;
  `,
  borderBox: css`
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* 轻微投影 */
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      padding: 0 !important;
    }
  `,
}));
const InvoiceTitle = () => {
  interface DataType {
    FullName: string;
    Phone: string;
    UserRoleNames: string;
    CreatedOn: string;
    LastLoginDate: string;
    Active: boolean;
    Id: number;
    RoleId: number;
  }
  const { company } = useUserStore();
  console.log('company', company);
  const navigate = useNavigate();
  const [api, contextHolder] = notification.useNotification();
  const { styles } = useStyles();
  useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [data, setData] = useState<BillModel[]>([]);
  const [billsSummary, setBillsSummary] = useState<any>({});

  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [searchForm] = Form.useForm();
  const billProtocolRef = useRef<any>(null);
  const offlinePaymentRef = useRef<any>(null);
  const columns: TableProps<BillModel>['columns'] = [
    {
      title: '账单编号',
      dataIndex: 'BillNumber',
      key: 'BillNumber',
    },
    {
      title: '订单号',
      dataIndex: 'CustomOrderNumbers',
      key: 'CustomOrderNumbers',
      render: (CustomOrderNumbers) => {
        return <CustomCollapse items={CustomOrderNumbers} />;
      },
    },
    {
      title: '客户',
      dataIndex: 'CustomerCompany',
      key: 'CustomerCompany',
    },
    {
      title: '出账日期',
      key: 'BillDateUtc',
      dataIndex: 'BillDateUtc',
      render: (value) => {
        return dayjs.utc(value).tz('Asia/Shanghai').format('YYYY-MM-DD');
      },
    },
    {
      title: '账单金额',
      dataIndex: 'BillAmount',
      key: 'BillAmount',
      render: (value) => {
        return `￥${value}`;
      },
    },
    {
      title: '最晚付款日',
      dataIndex: 'DeadLinePayDateUtc',
      key: 'DeadLinePayDateUtc',
      render: (value) => {
        return dayjs.utc(value).tz('Asia/Shanghai').format('YYYY-MM-DD');
      },
    },
    {
      title: '逾期',
      key: 'IsOverDue',
      dataIndex: 'IsOverDue',
      render: (IsOverDue) => {
        return IsOverDue ? (
          <span style={{ color: 'red' }}>是</span>
        ) : (
          <span style={{ color: 'green' }}>否</span>
        );
      },
    },
    {
      title: '逾期天数',
      key: 'DueDays',
      dataIndex: 'DueDays',
      render: (DueDays) => {
        return DueDays ? <span style={{ color: 'red' }}>{DueDays}</span> : '--';
      },
    },
    {
      title: '账单状态',
      key: 'BillStatus',
      dataIndex: 'BillStatus',
      render: (value, item: any) => {
        switch (item.BillStatusId) {
          case 0:
            return (
              <span style={{ color: '#FF8D1A' }}>
                <UnpaidSVG
                  viewBox="0 -6 16 22"
                  style={{ width: '16', height: '16' }}
                />
                {value}
              </span>
            );
          case 1:
            return (
              <span style={{ color: '#43CF7C' }}>
                <CheckCircleOutlined />
                &nbsp;{value}
              </span>
            );
          case 2:
            return (
              <span style={{ color: '#FF8D1A' }}>
                <ClockCircleOutlined />
                &nbsp;{value}
              </span>
            );
          case 3:
            return (
              <span style={{ color: '#FF8D1A' }}>
                <CloseCircleOutlined />
                &nbsp;{value}
              </span>
            );
          default:
            return '--';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              billProtocolClick(record.BillNumber);
            }}
          >
            查看账单
          </Button>
          {(record.BillStatusId === 0 || record.BillStatusId === 3) && (
            <Button
              size="small"
              type="link"
              onClick={() => {
                offlinePaymentClick(record.BillNumber);
              }}
            >
              线下付款
            </Button>
          )}
        </Space>
      ),
    },
  ];
  const billProtocolClick = (billNumber: any) => {
    if (billProtocolRef.current) {
      billProtocolRef.current.modalHandleShow(2, billNumber);
    }
  };
  const offlinePaymentClick = (billNumber: number) => {
    if (offlinePaymentRef.current) {
      offlinePaymentRef.current.modalHandleShow('bill', {
        billNumber: billNumber,
      });
    }
  };
  const billProtocolConfirmClick = (billNumber: any) => {
    if (billProtocolRef.current) {
      billProtocolRef.current.modalHandleShow(1, billNumber);
    }
  };
  const getList = () => {
    setTableLoading(true);

    let values = searchForm.getFieldsValue();
    let searchPar = values;
    if (values.RangePicker) {
      searchPar.StartBillDate = values.RangePicker[0].format('YYYY-MM-DD');
      searchPar.EndBillDate =
        values.RangePicker[1].format('YYYY-MM-DD') + ' 23:59:59.9999';
    }
    searchPar.RangePicker = undefined;
    getBillssList({ ...searchPar, PageIndex: 1, PageSize: 10 }).then((res) => {
      setData(res.Data);
      setTableLoading(false);
    });
  };
  const getSummary = () => {
    getBillsSummary().then((res) => {
      console.log('getBillsSummary', res);
      setBillsSummary(res);
      // setData(res.Data);
      // setTableLoading(false);
    });
  };

  useEffect(() => {
    getList();
    getSummary();
  }, []);
  const getPaymentStatusIcon = (paymentStatusId: number) => {
    switch (paymentStatusId) {
      case 1:
        return <CheckCircleOutlined />;
      default:
        return (
          <UnpaidSVG
            viewBox="0 -6 16 22"
            style={{ width: '16', height: '16' }}
          />
        );
    }
  };
  return (
    <div>
      {contextHolder}
      <Row style={{ textAlign: 'center' }} gutter={[16, 16]}>
        <Col span={8}>
          <div className={styles.borderBox}>
            <div style={{ color: '#3498DB', fontWeight: 600, fontSize: 20 }}>
              账单总数
            </div>
            <div
              style={{
                color: '#3498DB',
                fontWeight: 600,
                fontSize: 20,
                marginTop: 16,
              }}
            >
              {billsSummary.BillsTotal}
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div className={styles.borderBox}>
            <div style={{ color: '#E84B3B', fontWeight: 600, fontSize: 20 }}>
              未付款账单
            </div>
            <div
              style={{
                color: '#E84B3B',
                fontWeight: 600,
                fontSize: 20,
                marginTop: 16,
              }}
            >
              {billsSummary.UnpaidBillsCount}
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div className={styles.borderBox}>
            <div style={{ color: '#E84B3B', fontWeight: 600, fontSize: 20 }}>
              待付款
            </div>
            <div
              style={{
                color: '#E84B3B',
                fontWeight: 600,
                fontSize: 20,
                marginTop: 16,
              }}
            >
              ￥{formatWithThousandSeparator(billsSummary.PendingPayment)}
            </div>
          </div>
        </Col>
      </Row>
      {/* <Typography.Title level={5}>账单管理</Typography.Title> */}
      <Flex
        justify="space-between"
        style={{ width: '100%', marginBottom: 20, paddingRight: 8 }}
      >
        <Form
          layout={'inline'}
          labelAlign="left"
          form={searchForm}
          style={{
            maxWidth: 'none',
            paddingTop: 16,
            paddingBottom: 16,
          }}
        >
          <Row gutter={[4, 20]}>
            <Col span={6}>
              <Form.Item label="账单编号" name="BillNumber">
                <Input
                  placeholder="搜索账单编号"
                  suffix={<SearchSVG />}
                ></Input>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="客户名称" name="CustomerCompany">
                <Input
                  placeholder="搜索客户名称"
                  suffix={<SearchSVG />}
                ></Input>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="订单编号" name="CustomOrderNumber">
                <Input
                  placeholder="搜索订单编号"
                  suffix={<SearchSVG />}
                ></Input>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="出账日期" name="RangePicker">
                <RangePicker
                  showTime={false}
                  format="YYYY-MM-DD"
                  maxDate={dayjs(formatDate(new Date()), 'YYYY-MM-DD')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="是否逾期" name="IsOverDue">
                <Select allowClear>
                  <Select.Option value={true}>是</Select.Option>
                  <Select.Option value={false}>否</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="账单状态" name="BillStatusId">
                <Select allowClear>
                  <Select.Option value={0}>未支付</Select.Option>
                  <Select.Option value={1}>已支付</Select.Option>
                  <Select.Option value={2}>线下支付审核中</Select.Option>
                  <Select.Option value={3}>线下支付审核失败</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Space>
                <Button
                  type="primary"
                  style={{ width: 80 }}
                  onClick={() => {
                    getList();
                  }}
                >
                  搜索
                </Button>
                <Button
                  color="primary"
                  style={{ width: 80 }}
                  variant="outlined"
                  onClick={() => {
                    searchForm.resetFields();
                    getList();
                  }}
                >
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Flex>
      <Table<BillModel>
        loading={tableLoading}
        pagination={false}
        rowKey={(record) => record.id}
        columns={columns}
        dataSource={data}
      />
      <React.Suspense>
        <BillProtocol
          confirmCallBack={(customerNumber) => {}}
          ref={billProtocolRef}
        />
        <OfflinePayment confirmCallBack={getList} ref={offlinePaymentRef} />
      </React.Suspense>
    </div>
  );
};

export default InvoiceTitle;
