import { getBrands } from '@/pages/Home/services';
import { useRequest, useSafeState } from 'ahooks';
import { useEffect } from 'react';

const useBrandList = () => {
  const [selectedKey, setSelectedKey] = useSafeState<number>();
  const { data: brands } = useRequest(getBrands, {
    staleTime: 1000 * 60,
  });

  useEffect(() => {
    if (brands?.length) {
      setSelectedKey(brands?.[0]?.Id);
    }
  }, [JSON.stringify(brands)]);

  return {
    brands,
    selectedKey,
    setSelectedKey,
  };
};

export default useBrandList;
