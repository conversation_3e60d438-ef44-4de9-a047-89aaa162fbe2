import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  star: css`
    position: relative;
    display: inline-block;
    &:before {
      content: '***';
      font-size: 30px;
      vertical-align: middle;
      display: inline-block;
      line-height: 23px;
      height: 14px;
    }
  `,
}));

const PriceStar = () => {
  const { styles } = useStyles();
  return <span className={styles.star}></span>;
};

export default PriceStar;
