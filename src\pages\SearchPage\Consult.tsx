import { transformToTree } from '@/components/CategoriesTree';
import SearchPageContent from '@/components/SearchPageContent';
import { useCategoriesStore } from '@/store/categroies';
import { useSupplierStore } from '@/store/supplier';
import { useCreation } from 'ahooks';
import { memo } from 'react';
import SearchCard from './components/SearchCard';
import useSearch from './hooks/useSearch';
import { searchConsult } from './services';

const Consult = () => {
  const { data, loading, searchValues, updateState } = useSearch(searchConsult);
  const supplierStore = useSupplierStore();
  const categoriesStore = useCategoriesStore();

  const supplierOptions = useCreation(() => {
    return supplierStore.displayedList.map((item) => ({
      label: item,
      value: item,
    }));
  }, [supplierStore.displayedList]);

  const categoriesTreeData = useCreation(() => {
    return transformToTree(categoriesStore.rawDataDisplayed);
  }, [categoriesStore.rawDataDisplayed]);

  return (
    <SearchPageContent
      to="yellow-pages-products"
      searchPanel={
        <SearchCard
          categoriesTreeData={categoriesTreeData}
          supplierOptions={supplierOptions}
          searchValues={searchValues}
          updateSearchValues={updateState}
        />
      }
      searchParams={searchValues}
      updateSearchParams={updateState}
      dataSource={
        data?.Data?.map((item) => ({
          Id: item.Id,
          Name: item.SolutionName,
          ShortDescription: item.Description,
          LogoUrl: item.Logo,
          GlobalId: item.GlobalId,
          ShowedPrice: 0,
          Currency: '',
          LimitPurchase: false,
          Supplier: item.Supplier,
        })) || []
      }
      total={data?.Total || 0}
      loading={loading}
    />
  );
};

export default memo(Consult);
