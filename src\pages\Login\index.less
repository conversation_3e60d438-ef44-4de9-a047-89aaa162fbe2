.container {
  width: 100%;
  min-height: 100vh;
  margin-top: -48px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-image: url('@/assets/brandZoneBg.png'),
    linear-gradient(
      180deg,
      rgba(247, 249, 252, 100%) 0%,
      rgba(247, 249, 252, 0%) 100%
    );
  background-position: center;
  background-size: cover;
}

.loginpan {
  width: 500px;
}

.ant-pro-page-container {
  position: relative;
  display: flex;
  align-items: center;
}

.ant-form-item-control-input {
  width: 330px;
}

.ant-btn {
  width: 380px;
}

.ant-form {
  width: 445px;
}

.forgetpassword {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 17.38px;
  color: rgba(17, 61, 237, 100%);
  width: 100%;
  display: block;
  text-align: center;
}

.line {
  width: 130px;
  height: 0;
  display: block;
  border-top: 1px solid rgba(229, 229, 229, 100%);
}

.flex {
  display: flex;
  justify-content: center;
}

.code {
  width: 100px;
  height: 38px;
  cursor: pointer;
}

.space {
  display: flex;
  gap: 10px;
}

.login,
.reg {
  width: 100%;
  height: 42px;
}

.login::before {
  content: '';
  background: linear-gradient(
    132.34deg,
    rgba(17, 75, 237, 100%) 0%,
    rgba(54, 152, 217, 100%) 69.77%,
    rgba(117, 225, 255, 100%) 100%
  );
  position: absolute;
  inset: -1px;
  opacity: 1;
  transition: all 0.3s;
  border-radius: inherit;
}

.login > span {
  position: relative;
}

.title {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
  padding: 0;
}

.subtitle {
  font-size: 12px;
  font-weight: 400;
  color: rgba(128, 128, 128, 100%);
}
