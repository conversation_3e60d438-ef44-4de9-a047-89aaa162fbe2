import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useEffect, useRef, useState } from 'react';

interface CustomCollapseProps {
  items: any[];
}

const CustomCollapse = ({ items }: CustomCollapseProps) => {
  const [expanded, setExpanded] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      // 获取内容实际高度
      setContentHeight(contentRef.current.scrollHeight);
    }
  }, [items]);

  if (!items || items.length === 0) return null;

  if (items.length === 1) {
    return <span>{items[0]}</span>;
  }

  return (
    <div style={{ lineHeight: '22px' }}>
      {/* 总是显示第一个项目 */}
      <span>{items[0]}</span>

      {/* 展开/收起按钮 */}
      <Button
        type="link"
        color="primary"
        style={{
          marginLeft: 8,
          cursor: 'pointer',
          display: 'inline-flex',
          alignItems: 'center',
          padding: '0 4px',
          height: '22px',
          color: '#1677f',
        }}
        onClick={() => setExpanded(!expanded)}
      >
        {expanded ? (
          <UpOutlined style={{ fontSize: 10, color: '#1677f' }} />
        ) : (
          <DownOutlined style={{ fontSize: 10, color: '#1677f' }} />
        )}
      </Button>

      {/* 可折叠内容区域 */}
      <div
        ref={contentRef}
        style={{
          overflow: 'hidden',
          height: expanded ? contentHeight : 0,
          transition: 'height 0.3s ease-out',
          marginTop: expanded ? 8 : 0,
          opacity: expanded ? 1 : 0,
          transitionProperty: 'height, opacity, margin-top',
          transitionDuration: '0.3s',
          transitionTimingFunction: 'ease-out',
        }}
      >
        <div>
          {items.slice(1).map((item, index) => (
            <div
              key={index}
              style={{
                marginBottom: 4,
                transform: expanded ? 'translateY(0)' : 'translateY(-10px)',
                transition: `transform 0.3s ease-out ${index * 0.05}s`,
              }}
            >
              {item}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CustomCollapse;
