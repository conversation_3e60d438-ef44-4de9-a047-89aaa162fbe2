import {
  CompanyInfo,
  fetchUser,
  getCompanyInfo,
  UserProfile,
} from '@/services/api';
import { login } from '@/services/user';
import { history } from '@umijs/max';
import { derive } from 'derive-valtio';
import { persist } from 'valtio-persist';
import { useProxy } from 'valtio/utils';

type UserState = {
  token: string;
  user: UserProfile | null;
  company: CompanyInfo | null;
};

const { store: userState } = await persist<UserState>(
  {
    token: '',
    user: null,
    company: null,
  },
  'user-info',
);

const userDeriveState = derive({
  isLogin: (get) => !!get(userState).token,
  isAdmin: (get) => get(userState).user?.UserRoleNames === '管理员',
  showReminder: (get) =>
    !get(userState).company?.StatusEnum ||
    get(userState).company?.StatusEnum === 20,
  isRegistEntity: (get) => get(userState).user?.IsRegistEntity,
});

const useUserDeriveState = () => useProxy(userDeriveState);

const userLogin = async (params: {
  email: string;
  password: string;
  captchaId: string;
  captchaCode: string;
}) => {
  try {
    const res = await login(params);
    if (res?.token) {
      userState.token = res.token;
      return true;
    } else {
      return false;
    }
  } catch (error: any) {
    return false;
  }
};

const getUser = async () => {
  const res = await fetchUser();
  userState.user = res;
};

const getCompany = async () => {
  const res = await getCompanyInfo();
  userState.company = res;
};

const resetState = () => {
  userState.token = '';
  userState.user = null;
  userState.company = null;
};

const userLogout = () => {
  resetState();
  history.push('/login');
};

const useUserStore = () => useProxy(userState);

export {
  getCompany,
  getUser,
  resetState,
  userLogin,
  userLogout,
  userState,
  useUserDeriveState,
};

export default useUserStore;
