import Error404 from '@/pages/404';
import { useUserDeriveState } from '@/store/user';
import { PropsWithChildren } from 'react';
import Authorize from './index';

const Admin: React.FC<PropsWithChildren> = ({ children }) => {
  const { isAdmin } = useUserDeriveState();
  if (!isAdmin) {
    return <Error404 />;
  }
  return (
    <Authorize error={<div>您没有权限访问此页面</div>}>{children}</Authorize>
  );
};

export default Admin;
