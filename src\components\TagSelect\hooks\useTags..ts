import { useCallback, useEffect, useState } from 'react';

interface IUseTags {
  type: 'radio' | 'multiple';
  dataSource: { label: string; value: string }[];
  defaultValue?: string[];
}

const useTags = ({
  dataSource,
  type = 'multiple',
  defaultValue = [],
}: IUseTags) => {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  useEffect(() => {
    setSelectedKeys(defaultValue);
  }, [JSON.stringify(defaultValue)]);

  const onTagClick = useCallback(
    (value: string, checked: boolean) => {
      setSelectedKeys((prevSelectedKeys) => {
        if (type === 'radio') {
          return [value];
        } else {
          return checked
            ? [...prevSelectedKeys, value]
            : prevSelectedKeys.filter((key) => key !== value);
        }
      });
    },
    [type],
  );

  const selectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allValues = dataSource.map((item) => item.value);
        setSelectedKeys(allValues);
      } else {
        setSelectedKeys([]);
      }
    },
    [dataSource],
  );

  return {
    selectedKeys,
    onTagClick,
    selectAll,
  };
};

export default useTags;
