import PageWrapper from '@/components/PageWrapper';
import PriceStar from '@/components/pricestar';
import ProductList from '@/components/Product/List';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { useCreation } from 'ahooks';
import { Breadcrumb, Flex, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import useBrand from './hooks/useBrand';

const useStyles = createStyles(
  ({ css, prefixCls }, props: { imageUrl: string }) => ({
    container: css`
      margin-top: -50px;
      background: url(${props.imageUrl}) no-repeat center;
      background-size: cover;
    `,
    banner: css`
      width: 90%;
      height: 400px;
      margin: 0 auto;
      padding: 100px 0;
    `,
    title: css`
      color: rgba(56, 56, 56, 0.88);
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 12px;
    `,
    description: css`
      width: 46%;
      font-size: 20px;
      color: rgba(56, 56, 56, 0.88);
    `,
    list: css`
      padding: 24px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 10px;
      margin-bottom: 24px;
    `,
  }),
);
const Brand = () => {
  const { brand, loading } = useBrand();
  const { styles } = useStyles({ imageUrl: brand?.FocusPictureUrl as string });

  const list = useCreation(() => {
    if (brand?.BrandZoneSolutionsModels) {
      return brand.BrandZoneSolutionsModels.map((item) => ({
        id: item.Id,
        globalId: item.GlobalId,
        name: item.Name,
        image: item.LogoUrl,
        description: item.ShortDescription,
        price: item.LimitPurchase ? (
          <PriceStar />
        ) : (
          formatWithThousandSeparator(item.ShowedPrice)
        ),
        currency: item.Currency,
        supplier: '',
      }));
    }
    return [];
  }, [JSON.stringify(brand?.BrandZoneSolutionsModels)]);
  return (
    <div style={{ background: 'rgba(255, 255, 255, 1)' }}>
      <Skeleton
        active
        loading={loading}
        style={{ minHeight: 'calc(100vh - 400px)', paddingTop: 50 }}
      >
        <div className={styles.container}>
          <div className={styles.banner}>
            <Flex
              justify="space-between"
              align="center"
              style={{ width: '100%' }}
            >
              <div style={{ width: '100%' }}>
                <h1 className={styles.title}>{brand?.BrandName}</h1>
                <div className={styles.description}>
                  {brand?.BrandDetailsDescription}
                </div>
                {/* <Button type="primary">
                  立即咨询 <ArrowRightOutlined />
                </Button> */}
              </div>
            </Flex>
          </div>
        </div>

        <PageWrapper
          style={{
            background: 'transparent',
            padding: '24px 0',
            height: '100%',
          }}
        >
          <Breadcrumb
            separator=">"
            items={[
              {
                title: '品牌专区',
              },
              {
                title: brand?.BrandName,
              },
            ]}
            style={{ marginBottom: '17px' }}
          />
          <div className={styles.list}>
            <ProductList list={list} type="brand" tag="online" />
          </div>
        </PageWrapper>
      </Skeleton>
    </div>
  );
};

export default Brand;
