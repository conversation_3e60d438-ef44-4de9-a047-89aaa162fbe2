import useCsrfToken from '@/hooks/useCsrfToken';
import useUserStore from '@/store/user';
import { CloseOutlined } from '@ant-design/icons';
import { useUnmount } from 'ahooks';
import {
  Button,
  ConfigProvider,
  Flex,
  Form,
  Input,
  message,
  Select,
} from 'antd';
import { createStyles } from 'antd-style';
import { debounce } from 'lodash-es';
import { useState } from 'react';
import { feedBack, FeedbackParams } from './services';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    width: 300px;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  `,
  header: css`
    background: ${token.colorPrimary};
    height: 30px;
    padding: 0 20px;
    font-size: 12px;
    color: #fff;
  `,
  content: css`
    padding: 12px;
  `,
  textarea: css`
    &.ant-form-item-vertical .ant-form-item-control {
      flex: auto;
    }
  `,
  input: css`
    &.ant-input-disabled {
      color: ${token.colorText} !important;
    }
  `,
}));
const ContactForm: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { styles } = useStyles();
  const userStore = useUserStore();
  const { runAsync: getCsrfToken } = useCsrfToken();
  const [form] = Form.useForm();

  const [feedBackLoading, setFeedBackLoading] = useState(false);

  const onFinish = debounce((values: FeedbackParams) => {
    setFeedBackLoading(true);
    getCsrfToken().then((res) => {
      const formData = new FormData();
      Object.keys(values).forEach((key) => {
        formData.append(key, values[key as keyof FeedbackParams] as any);
      });
      feedBack(formData, res.Token).then(() => {
        message.success('感谢您的咨询！我们会尽快与您联系');
        setFeedBackLoading(false);
        onClose();
      });
    });
  });

  useUnmount(() => {
    form.resetFields();
  });

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            itemMarginBottom: 20,
            controlHeight: 28,
            algorithm: true,
            labelFontSize: 12,
            labelHeight: 28,
            fontSize: 12,
          },
          Input: {
            controlHeight: 28,
            fontSize: 12,
            borderRadius: 4,
          },
          Select: {
            controlHeight: 28,
            fontSize: 12,
            borderRadius: 4,
          },
          Button: {
            controlHeight: 28,
            fontSize: 12,
          },
        },
      }}
    >
      <div className={styles.container}>
        <Flex justify="space-between" align="center" className={styles.header}>
          <span>联系我们</span>
          <CloseOutlined onClick={onClose} />
        </Flex>
        <div className={styles.content}>
          <Form
            form={form}
            initialValues={{
              CompanyName: userStore.company?.Name,
              CompanyType: 30,
            }}
            labelCol={{ span: 7 }}
            wrapperCol={{ span: 17 }}
            labelAlign="left"
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="企业名称"
              name="CompanyName"
              colon={false}
              rules={[
                { required: true, message: '请输入企业名称' },
                { max: 30, message: '长度不能超过30个字符' },
              ]}
            >
              <Input
                placeholder="请输入"
                maxLength={30}
                showCount
                disabled={!!userStore.company?.Name}
                className={styles.input}
              />
            </Form.Item>
            <Form.Item
              label="企业类型"
              name="CompanyType"
              colon={false}
              rules={[{ required: true, message: '请选择企业类型' }]}
              hidden
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="联系人"
              name="Contact"
              colon={false}
              rules={[
                { required: true, message: '请输入联系人' },
                { max: 30, message: '长度不能超过30个字符' },
              ]}
            >
              <Input placeholder="请输入" maxLength={30} showCount />
            </Form.Item>
            <Form.Item
              label="联系邮箱"
              name="Email"
              colon={false}
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入正确的邮箱' },
              ]}
            >
              <Input type="email" placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="咨询分类"
              name="CategoryId"
              colon={false}
              rules={[{ required: true, message: '请选择咨询分类' }]}
            >
              <Select
                placeholder="请选择"
                options={[
                  {
                    label: '账户',
                    value: 0,
                  },
                  {
                    label: '产品',
                    value: 1,
                  },
                  {
                    label: '订单',
                    value: 2,
                  },
                  {
                    label: '其他',
                    value: 3,
                  },
                ]}
              />
            </Form.Item>
            <Form.Item
              label="咨询主题"
              name="Title"
              colon={false}
              rules={[
                { required: true, message: '请输入咨询主题' },
                { max: 30, message: '长度不能超过30个字符' },
              ]}
            >
              <Input placeholder="请输入咨询主题" maxLength={30} showCount />
            </Form.Item>
            <Form.Item
              layout="vertical"
              label="咨询详情"
              name="Description"
              rules={[
                { required: true, message: '请输入咨询详情' },
                { max: 200, message: '请输入200字以内的咨询详情' },
              ]}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              className={styles.textarea}
            >
              <Input.TextArea
                placeholder="请输入咨询详情"
                maxLength={200}
                showCount
              />
            </Form.Item>
            <Form.Item
              wrapperCol={{ span: 24 }}
              style={{ marginBottom: 0, marginTop: 30 }}
            >
              <Button
                type="primary"
                htmlType="submit"
                block
                loading={feedBackLoading}
              >
                提交
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default ContactForm;
