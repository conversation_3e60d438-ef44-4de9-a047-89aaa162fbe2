import { CartItemModel } from '@/types';
import Big from 'big.js';
import { useMemo, useState } from 'react';
export default () => {
  const [cart, setCart] = useState<CartItemModel[]>([]);

  const amount = useMemo(() => {
    return cart.reduce((acc, cur) => {
      const price = new Big(cur.UnitPrice);
      const quantity = new Big(cur.Quantity);
      return acc.plus(price.times(quantity));
    }, new Big(0));
  }, [cart]);

  return {
    cart,
    setCart,
    amount: amount.toString(),
  };
};
