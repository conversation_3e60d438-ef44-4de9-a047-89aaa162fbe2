import { request } from '@umijs/max';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
import Markdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
const useStyles = createStyles(() => ({
  container: {
    background: '#fff',
    paddingTop: 50,
  },
  fullpage: {
    width: 1200,
    margin: '0 auto',
  },
}));
export default () => {
  const { styles } = useStyles();
  const [content, setContent] = useState('');
  useEffect(() => {
    request('/protocol.md').then((res) => {
      setContent(res);
    });
  }, []);
  return (
    <div className={styles.container}>
      <div className={styles.fullpage}>
        <Markdown rehypePlugins={[rehypeRaw]}>{content}</Markdown>
      </div>
    </div>
  );
};
