import { CategoryItem } from '@/pages/Solution/services';
import { request } from '@umijs/max';

type AvailableEntity = {
  Disabled: boolean;
  Group: string | null;
  Selected: boolean;
  Text: string;
  Value: string;
};

export type UserProfile = {
  Email: string;
  Username: string | null;
  FullName: string;
  Phone: string;
  Address: string;
  CompanyName: string;
  CompanyStatusId: number;
  CompanyStatus: string | null;
  SetAdministratorEmail: string | null;
  Active: boolean;
  CreatedOn: string; // ISO 8601 date string
  LastLoginDate: string | null; // ISO 8601 date string
  UserRoleNames: string;
  Password: string | null;
  SelectedUserRoleIds: number[];
  AvailableUserRoles: string[];
  CompanyTypeId: number;
  AssistanceEmail: string;
  AvailableEntities: AvailableEntity[];
  CurrentEntityId: number;
  Regions: number[];
  AdminEmail: string;
  AdminPhone: string;
  AdminPosition: string;
  CurrentDistributorGlobalId: string;
  IsRegistEntity: boolean;
  Id: number;
};
export async function fetchUser(): Promise<UserProfile> {
  return request('/api/Users/<USER>');
}

export async function getCartCount(regin: number): Promise<{ Count: number }> {
  return request(`/api/ShoppingCarts/items/count?region=${regin}`, {
    method: 'GET',
  });
}

export interface CompanyInfo {
  Address: string;
  AssistanceManager: string;
  AssistanceEmail: string;
  USCI: string;
  Comment: string;
  Contact: string;
  Disabled: boolean;
  Email: string;
  Id: number;
  IsCreated: boolean;
  IsUploadedLicense: boolean;
  IsUploadedResource: boolean;
  Name: string;
  Phone: string;
  Status: string;
  StatusEnum: number;
  IsCredit: boolean;
  License: any[];
  Resource: any[];
  BlueCloudAgreementNumber: string;
  BlueCloudAgreementEndDate: string;
  BlueCloudAgreementStartDate: string;
  BillingNoticeEmail: string;
}
export async function getCompanyInfo(): Promise<CompanyInfo> {
  return request('/api/Company/info');
}

export type CategoryItemDisplayed = CategoryItem & { HasProducts: boolean };

export async function getAllCategroies(): Promise<CategoryItemDisplayed[]> {
  return request('/api/Categories?SolutionAvailable=true');
}
export async function getAllCategroiesDisplayed(): Promise<
  CategoryItemDisplayed[]
> {
  return request('/api/Categories/displayed?SolutionAvailable=true');
}

export async function getSuppliers() {
  return request('/api/Suppliers');
}
export async function getSuppliersDisplayed() {
  return request('/api/Suppliers/displayed');
}
