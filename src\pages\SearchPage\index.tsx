import useRouteKey from '@/hooks/useRouteKey';
import useUrlState from '@ahooksjs/use-url-state';
import { Outlet, useNavigate } from '@umijs/max';
import { useSafeState } from 'ahooks';
import { Button, Flex, Input, Tabs } from 'antd';
import { createStyles } from 'antd-style';
import { trim } from 'lodash-es';

const useStyles = createStyles(({ prefixCls, css }) => ({
  container: css`
    padding-top: 80px;
    padding-bottom: 50px;
    min-height: calc(100vh - 400px);
  `,
  search: css`
    width: 600px;
    height: 38px;
    border-radius: 20px;
    background: #ffffff;
  `,
  searchButton: css`
    width: 88px;
    height: 38px;
    border-radius: 20px;
    &.${prefixCls}-btn-primary:not([disabled]):not(
        .${prefixCls}-btn-dangerous
      ) {
      > span {
        position: relative;
      }

      &::before {
        content: '';
        background: linear-gradient(139.53deg, #113ded 0%, #3bc1ff 100%);
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        opacity: 0;
      }
    }
  `,
  list: css`
    width: 1200px;
    margin: 0 auto;
  `,
}));

const SearchPage = () => {
  const { styles } = useStyles();
  const [urlState, setUrlState] = useUrlState();
  const navigate = useNavigate();
  const routeKey = useRouteKey();

  const [keywords, setKeywords] = useSafeState<string>(urlState.k);

  const handleChange = (e: any) => {
    setKeywords(e.target.value);
  };
  const handleSearch = () => {
    setUrlState({ k: trim(keywords) });
  };

  const handleChangeTabs = (key: string) => {
    navigate(`/search/${key}?k=${trim(keywords)}`);
  };

  return (
    <div className={styles.container}>
      <Flex vertical gap={50}>
        <Flex justify="center" gap={16}>
          <Input
            className={styles.search}
            value={keywords}
            onChange={handleChange}
            onPressEnter={handleSearch}
          />
          <Button
            type="primary"
            className={styles.searchButton}
            onClick={handleSearch}
          >
            搜索
          </Button>
        </Flex>

        <div className={styles.list}>
          <Tabs
            centered
            activeKey={routeKey}
            items={[
              {
                label: '在线购买',
                key: 'online',
              },
              {
                label: '咨询专属',
                key: 'consultation',
              },
            ]}
            onChange={handleChangeTabs}
          />
          <div>
            <Outlet />
          </div>
        </div>
      </Flex>
    </div>
  );
};

export default SearchPage;
