import { ReactComponent as RightSvg } from '@/assets/right.svg';
import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import PageWrapper from '@/components/PageWrapper';
import PurchaseSteps from '@/components/PurchaseSteps';
import {
  deleteCartItem,
  getCustomers,
  postCustomers,
  postOrders,
  putCustomers,
} from '@/services/order/PurchaseController';
import useUserStore from '@/store/user';
import { CartItemModel, IndustryList } from '@/types';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { PlusCircleOutlined } from '@ant-design/icons';
import { useModel, useNavigate } from '@umijs/max';
import type { PaginationProps, TableProps } from 'antd';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  Modal,
  notification,
  Pagination,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  TableColumnsType,
  Typography,
} from 'antd';
import { createStyles } from 'antd-style';
import TextArea from 'antd/es/input/TextArea';
import Big from 'big.js';
import React, { useEffect, useRef, useState } from 'react';
import ParameterExpandedRowRender from './components/ParameterExpandedRowRender';
import style from './index.less';
const Protocol = React.lazy(() => import('@/components/Protocol'));
const useStyles = createStyles(({ css, prefixCls }) => ({
  linearGradientButton: css`
    width: 120px;
    height: 46px;
    &.${prefixCls}-btn-primary:not([disabled]):not(
        .${prefixCls}-btn-dangerous
      ) {
      > span {
        position: relative;
      }

      &::before {
        content: '';
        background: linear-gradient(224.07deg, #2480e3ff 0%, #2b24e3ff 100%);
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        opacity: 0;
      }
    }
  `,
}));
interface SolutionForms {
  [key: string]: FormInstance;
}
const Purchase = () => {
  const { company } = useUserStore();
  const { styles } = useStyles();
  const [api, contextHolder] = notification.useNotification();
  const pageSize = 10;
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [total, setTotal] = useState<number>(1);
  const [searchText, setSeachText] = useState<string>('');
  const [changePrice, setChangePrice] = useState<boolean>(false);
  const protocolRef = useRef<any>(null);
  const { refreshCartCount } = useModel('cart');
  let debounceTimer: NodeJS.Timeout;
  useEffect(() => {
    debounceTimer = setTimeout(async () => {
      const response = await getCustomers({
        PageIndex: 1,
        PageSize: pageSize,
        SearchText: searchText,
      });
      setTotal(response.Total);
      setCustomerDataSource(response.Data);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [searchText]);
  const { cart, amount } = useModel('Cart.cartModel');
  const { cartRegion } = useModel('cart');
  useEffect(() => {
    console.log('cart', cart);
    if (cart === null || cart === undefined || cart.length === 0) {
      setTimeout(() => {
        navigate('/cart');
      }, 200);
    }
  }, [cart]);
  const [modalConfirmLoading, setModalConfirmLoading] =
    useState<boolean>(false);
  const [payLoading, setPayloading] = useState<boolean>(false);
  const [customerForm] = Form.useForm();
  const [addressForm] = Form.useForm();
  const [solutionForms] = useState<SolutionForms>({});
  const solutionFormRefs = useRef<any>({});
  const navigate = useNavigate();
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] =
    useState<boolean>(false);
  const [customerModalType, setCustomerModalType] = useState<number>(1);
  const [paymentMethod, setPaymentMethod] = useState<number>(2);
  const [paymentPlan, setPaymentPlan] = useState<number>(0);
  const [selectCustomer, setSelectCustomer] = useState<any>({});
  const [customer, setCustomer] = useState<any>();
  const [customerDataSource, setCustomerDataSource] = useState<any[]>([]);
  const [remartk, setRemark] = useState<string>('');
  const customerColumns: TableProps['columns'] = [
    {
      title: '姓名',
      dataIndex: 'Name',
      key: 'Name',
    },
    {
      title: '公司',
      dataIndex: 'Company',
      key: 'Company',
    },
    {
      title: '地址',
      dataIndex: 'Address',
      key: 'Address',
    },
    {
      title: '邮箱',
      dataIndex: 'Email',
      key: 'Email',
    },
    {
      title: '电话',
      dataIndex: 'PhoneNumber',
      key: 'PhoneNumber',
    },
    {
      title: '操作',
      dataIndex: 'Id',
      key: 'Id',
      render: (id, row) => {
        return (
          <Button
            type="link"
            onClick={() => {
              modifyCustomerClick(row);
            }}
          >
            修改
          </Button>
        );
      },
    },
  ];
  const modifyCustomerClick = (row: any) => {
    setCustomerModalType(2);
    setIsAddCustomerModalOpen(true);
    setSelectCustomer(row);
    customerForm.setFieldsValue(row);
  };

  const getCustomerData = (pgIndex: number = 1) => {
    setPageIndex(pgIndex);
    getCustomers({
      PageIndex: pgIndex,
      PageSize: pageSize,
      SearchText: searchText,
    }).then((res) => {
      setTotal(res.Total);
      setCustomerDataSource(res.Data);
    });
  };
  const solutionColumns: TableColumnsType<CartItemModel> = [
    {
      title: '商品图片',
      dataIndex: 'PictureUrl',
      render: (value) => {
        return <img src={value} style={{ height: 80 }}></img>;
      },
    },
    {
      title: '商品名称',
      dataIndex: 'SolutionName',
      key: 'SolutionName',
    },
    {
      title: '商品SKU',
      dataIndex: 'Name',
      render: (value, record) => {
        return (
          <>
            <div>{record.Name}</div>
            {/* <div>SKU: {record.Sku}</div> */}
          </>
        );
      },
    },
    {
      title: '商品单价',
      dataIndex: 'UnitPrice',
      render: (value) => {
        return <span>￥{formatWithThousandSeparator(value)}</span>;
      },
    },
    {
      title: '购买数量',
      dataIndex: 'Quantity',
      key: 'Quantity',
    },
    {
      title: '小计',
      dataIndex: 'SubTotal',
      key: 'SubTotal',
      render: (value) => {
        return <span>￥{formatWithThousandSeparator(value)}</span>;
      },
    },
  ];
  const rowSelection: TableProps<any>['rowSelection'] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows,
      );
      if (selectedRows.length > 0) {
        setCustomer(selectedRows[0]);
      }
    },
    getCheckboxProps: (record: any) => ({
      name: record.name,
    }),
    columnWidth: 120,
    columnTitle: '选择客户',
  };
  const addCustomerHandleShow = () => {
    setIsAddCustomerModalOpen(true);
    customerForm.resetFields();
    setCustomerModalType(1);
  };
  const addCustomerHandleOk = () => {
    setModalConfirmLoading(true);
    customerForm
      .validateFields()
      .then((res: any) => {
        if (customerModalType === 1) {
          res.Id = 0;
          res.competitor = 'no';
          postCustomers(res).then((re) => {
            getCustomerData(1);
            setIsAddCustomerModalOpen(false);
            setModalConfirmLoading(false);
          });
        } else {
          res.competitor = 'no';
          putCustomers(selectCustomer.Id, res).then((re) => {
            getCustomerData(1);
            setIsAddCustomerModalOpen(false);
            setModalConfirmLoading(false);
          });
        }
      })
      .catch((res) => {
        setModalConfirmLoading(false);
      });
  };
  const addCustomerHandleCancel = () => {
    setIsAddCustomerModalOpen(false);
  };
  const submitOrderClick = async () => {
    let parameterResult = await getSolutionsPurchaseParameter();
    if (!parameterResult.success) {
      return;
    }
    setPayloading(true);
    const items = cart.map((item) => ({
      Quantity: item.Quantity,
      SolutionId: item.SolutionId,
      EditionId: item.EditionId,
      PurchaseParameter:
        item.DeliveryMethod === 1
          ? JSON.stringify(parameterResult.data[item.Id.toString()])
          : '',
      Region: cartRegion,
    }));
    //const purchase = cart.some(item => item.SolutionPurchaseParameter && item.SolutionPurchaseParameter.length > 0);
    let orderData: any = {
      CustomerId: customer.Id,
      PaymentMethod: paymentMethod,
      IsLeads: false,
      OrderItems: items,
      DeliveryMethod: cart[0].DeliveryMethod,
      Region: cartRegion,
      Remark: remartk,
      //EstimatedStatementTime: '2025-01-13',
      // EstimatedStatementTime: this.estimatedTime,
      ChangePrice: changePrice,
      // PaymentTermDays: 1,
      // IsInstallment: this.ApplyChange
    };
    if (cart[0].DeliveryMethod === 10) {
      try {
        let res = await addressForm.validateFields();
        let { ReceivePerson, ReceivePhoneNumber, ReceiveAddress } = res;
        orderData.ReceivePerson = ReceivePerson;
        orderData.ReceivePhoneNumber = ReceivePhoneNumber;
        orderData.ReceiveAddress = ReceiveAddress;
        orderData.PaymentPlan = paymentPlan;
      } catch (error) {
        console.log('校验失败:', error);
        setPayloading(false);
        const element = document.getElementById('address');
        window.scrollTo({
          top: element?.offsetTop,
          behavior: 'smooth',
        });
        return;
      }
    }
    postOrders(orderData).then(
      (data) => {
        let deleteCartPromise: any[] = [];
        cart.forEach((ca) => {
          if (ca.Id && ca.Id !== 0) {
            deleteCartPromise.push(deleteCartItem(ca.Id));
          }
        });
        if (deleteCartPromise.length !== 0) {
          Promise.all(deleteCartPromise).then((res) => {
            refreshCartCount();
          });
        }
        setPayloading(false);
        api['success']({
          message: '订单生成成功',
          duration: 2,
        });
        if (changePrice) {
          goPaymentPage(data.CustomOrderNumber);
        } else if (paymentMethod === 3) {
          goPaymentPage(data.CustomOrderNumber);
        } else if (protocolRef.current) {
          protocolRef.current.modalHandleShow(1, data.CustomOrderNumber);
        }
      },
      (err) => {
        setPayloading(false);
        api['error']({
          message: '订单生成失败',
          duration: 2,
        });
      },
    );
  };
  const previousStepCllick = () => {
    navigate('/cart');
  };
  const goPaymentPage = (customOrderNumber: any) => {
    console.log('goPaymentPage', customOrderNumber);
    navigate('/payment', {
      state: { CustomOrderNumber: customOrderNumber },
    });
  };
  const onPageChange: PaginationProps['onChange'] = (page) => {
    getCustomerData(page);
  };
  const getSolutionsPurchaseParameter = async () => {
    try {
      const formEntries = Object.entries(solutionForms);
      const results = await Promise.all(
        formEntries.map(([key, form]) =>
          form
            .validateFields()
            .then((values) => ({ key, values }))
            .catch((error) => Promise.reject({ key, error })),
        ),
      );
      const allData: any = results.reduce(
        (acc: Record<string, any>, { key, values }) => {
          acc[key] = values;
          return acc;
        },
        {},
      );
      return { success: true, data: allData };
    } catch (errorInfo: any) {
      scrollToFirstError();
      return { success: false, data: [] };
    }
  };
  const scrollToFirstError = () => {
    const element = document.getElementById('solutions');
    element?.scrollIntoView({ behavior: 'smooth' });
  };
  const expandedRowRender = (record: any) => {
    return (
      <ParameterExpandedRowRender
        changeForm={changeSolutionForms}
        changeFormRefs={changeSolutionFormRefs}
        record={record}
      />
    );
  };
  const changeSolutionForms = (id: any, form: any) => {
    solutionForms[id] = form;
  };
  const changeSolutionFormRefs = (id: any, form: any) => {
    solutionFormRefs.current[id] = form;
  };
  return (
    <div style={{ paddingTop: 50 }}>
      <PageWrapper
        style={{
          paddingBottom:
            cart.length > 0 &&
            cart[0].DeliveryMethod === 10 &&
            paymentPlan === 1
              ? 200
              : 120,
        }}
      >
        {contextHolder}
        <div>
          <Row>
            <Col span={12}>
              <Typography.Title level={3}>订单页面</Typography.Title>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <PurchaseSteps stepValue={1} />
            </Col>
          </Row>
          <Row style={{ marginTop: 16, marginBottom: 16 }}>
            <Col span={12} className={style.purchaseTitle}>
              填写并核对订单信息
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Space>
                <Input
                  onChange={(oc: any) => {
                    setSeachText(oc.target.value);
                  }}
                  prefix={<SearchSVG />}
                  style={{ width: 200 }}
                  placeholder="请输入姓名/公司/邮箱/电话进行搜索"
                />
                <Button
                  icon={<PlusCircleOutlined />}
                  type="primary"
                  onClick={addCustomerHandleShow}
                >
                  添加客户
                </Button>
              </Space>
            </Col>
          </Row>
          <div className={style.purchaaseTablaBorder}>
            <div className={style.purchaseTableTitle}>
              <span style={{ color: '#FF5733' }}>*</span>交付信息
            </div>
            <Table
              style={{
                borderRadius: '8px',
                overflow: 'hidden',
                border: '1px solid #d9d9d9',
              }}
              rowSelection={{ type: 'radio', ...rowSelection }}
              rowKey={'Id'}
              pagination={false}
              dataSource={customerDataSource}
              columns={customerColumns}
            ></Table>
            {total > pageSize && (
              <Pagination
                style={{ marginTop: 16 }}
                align="end"
                current={pageIndex}
                pageSize={pageSize}
                onChange={onPageChange}
                total={total}
              />
            )}
          </div>
          <div
            className={style.purchaaseTablaBorder}
            style={{ marginTop: 16 }}
            id="solutions"
          >
            <div className={style.purchaseTableTitle}>购买商品信息</div>
            <Table
              pagination={false}
              dataSource={cart}
              style={{
                borderRadius: '8px',
                overflow: 'hidden',
                border: '1px solid #d9d9d9',
              }}
              rowKey={'SolutionId'}
              expandable={{
                expandedRowRender,
                expandIcon: () => <></>,
                expandedRowKeys: cart
                  .filter((c) => c.DeliveryMethod === 1)
                  .map((c) => c.SolutionId),
              }}
              columns={solutionColumns}
            ></Table>
          </div>
          <div className={style.specialBox}>
            <span className={style.specialTitle}>申请特价</span>
            <Switch
              defaultValue={false}
              onChange={(v) => {
                setChangePrice(v);
              }}
            />
            <span className={style.specialInfo}>
              若申请特价，请联系厂商就采购的产品进行协商。
            </span>
          </div>
          {cart.length > 0 && cart[0].DeliveryMethod === 10 && (
            <>
              <div
                className={style.purchaseTitle}
                style={{ marginTop: 16 }}
                id="address"
              >
                收货信息
              </div>
              <div>
                <div style={{ marginTop: 16 }}>
                  <Form form={addressForm} labelCol={{ flex: '80px' }}>
                    <Row>
                      <Col span={11}>
                        <Form.Item
                          label="收货人"
                          name="ReceivePerson"
                          rules={[
                            { required: true },
                            { pattern: /^\S+$/, message: '收货人不能包含空格' },
                          ]}
                        >
                          <Input placeholder="请输入收货人" />
                        </Form.Item>
                      </Col>
                      <Col offset={2} span={11}>
                        <Form.Item
                          label="手机号码"
                          name="ReceivePhoneNumber"
                          rules={[
                            { required: true },
                            {
                              pattern: /^([-()+ 0-9]+)$/,
                              message: '请输入正确的电话',
                            },
                          ]}
                        >
                          <Input placeholder="请输入手机号码" />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          label="收货地址"
                          name="ReceiveAddress"
                          rules={[
                            { required: true },
                            {
                              pattern: /^\S+$/,
                              message: '收货地址不能包含空格',
                            },
                          ]}
                        >
                          <Input placeholder="收货地址" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                </div>
              </div>
              <div className={style.purchaseTitle} style={{ marginTop: 16 }}>
                付款方案
              </div>
              <div className={style.payFrame}>
                <div style={{ marginTop: 16 }}>
                  <Radio.Group
                    onChange={(oc) => {
                      setPaymentPlan(oc.target.value);
                    }}
                    defaultValue={0}
                    size="large"
                    options={[
                      { value: 0, label: '全额付款', style: { fontSize: 16 } },
                      {
                        value: 1,
                        label: '分期付款（首付款+尾款）',
                        style: { fontSize: 16 },
                      },
                    ]}
                  />
                  {paymentPlan === 1 && (
                    <div className={style.alertInfo}>
                      重要提示：选择分期付款后，订单提交时仅支付首付款，到货完成验收后的15日内支付全部尾款。
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          <div
            className={style.purchaseTitle}
            style={{ marginTop: 16, marginBottom: 16 }}
          >
            支付方式
          </div>
          <div className={style.payrame}>
            <div
              onClick={() => {
                setPaymentMethod(2);
              }}
              className={
                style.paymentMethod +
                (paymentMethod === 2 ? ' ' + style.purchaaseSelected : '')
              }
            >
              立即支付
              {paymentMethod === 2 && (
                <div className={style.purchaaseSquare}>
                  {' '}
                  <RightSvg className={style.purchaaseImg} />
                </div>
              )}
            </div>
            {company?.IsCredit &&
              cart.length > 0 &&
              cart[0]?.DeliveryMethod !== 10 && (
                <div
                  onClick={() => {
                    setPaymentMethod(3);
                  }}
                  className={
                    style.paymentMethod +
                    (paymentMethod === 3 ? ' ' + style.purchaaseSelected : '')
                  }
                >
                  账期支付
                  {paymentMethod === 3 && (
                    <div className={style.purchaaseSquare}>
                      {' '}
                      <RightSvg className={style.purchaaseImg} />
                    </div>
                  )}
                </div>
              )}
          </div>
          {paymentMethod !== 3 && (
            <div style={{ marginTop: 16 }}>
              <Radio.Group
                defaultValue={2}
                size="large"
                options={[
                  { value: 2, label: '线下支付', style: { fontSize: 16 } },
                ]}
              />
            </div>
          )}
          <div
            className={style.purchaseTitle}
            style={{ marginTop: 16, marginBottom: 16 }}
          >
            备注
          </div>
          <div>
            <TextArea
              placeholder="请填写备注"
              onChange={(oc) => {
                setRemark(oc.target.value);
              }}
            ></TextArea>
          </div>
          {(paymentMethod === 2 || paymentMethod === 3) && (
            <div>
              <div
                className={style.purchaseTitle}
                style={{ marginTop: 16, marginBottom: 16 }}
              >
                账户信息
              </div>
              <div className={style.accountInformation}>
                <span>
                  账户名称：上海蓝云网络科技有限公司
                  <br />
                </span>
                <span>
                  账户名称：0200227919200053244
                  <br />
                </span>
                <span>
                  账户名称：中国工商银行国航大厦支行
                  <br />
                </span>
              </div>
            </div>
          )}

          {/* <Divider /> */}
        </div>
        <Modal
          destroyOnHidden={false}
          title={customerModalType === 1 ? '添加客户' : '修改客户'}
          open={isAddCustomerModalOpen}
          onOk={addCustomerHandleOk}
          confirmLoading={modalConfirmLoading}
          onCancel={addCustomerHandleCancel}
        >
          <Form preserve={false} form={customerForm}>
            <Form.Item label="姓名" name="Name" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item label="公司" name="Company" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item
              label="邮箱"
              name="Email"
              rules={[
                { required: true },
                { type: 'email', message: '请输入正确的邮箱' },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="电话"
              name="PhoneNumber"
              rules={[
                { required: true },
                { pattern: /^([-()+ 0-9]+)$/, message: '请输入正确的电话' },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item label="地址" name="Address" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item
              label="行业"
              name="Industry"
              rules={[{ required: true }]}
            >
              <Select>
                {IndustryList.map((industry) => (
                  <Select.Option key={industry.id} value={industry.id}>
                    {industry.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
          {company?.IsCredit && (
            <div
              style={{
                color: '#409eff',
                marginTop: 10,
                borderLeft: '4px solid #409eff',
                backgroundColor: '#ecf5ff',
                maxWidth: 'fit-content',
                padding: 10,
                fontSize: 14,
              }}
            >
              重要提示：为确保账单合并流程顺利进行，请您务必确认并提交最终客户名称信息。
            </div>
          )}
        </Modal>
        {cart.length > 0 &&
          cart[0].DeliveryMethod === 10 &&
          paymentPlan === 1 && (
            <Row className={style.payInstallmentBox}>
              <Col span={12} className={style.payInstallmentAmountBox}>
                <div>
                  <span className={style.payAmountTitle}>
                    商品总额：￥{formatWithThousandSeparator(amount)}
                  </span>
                </div>
                <div>
                  <span className={style.payAmountTitle}>
                    首付款金额(50%)：
                  </span>
                  <span className={style.payAmountTitle}>￥</span>
                  <span className={style.payAmountTitle}>
                    {formatWithThousandSeparator(
                      new Big(amount).div(2).toFixed(2).toString(),
                    )}
                  </span>
                </div>
                <div>
                  <span className={style.payAmountTitle}>尾款金额(50%)：</span>
                  <span className={style.payAmountTitle}>￥</span>
                  <span className={style.payAmountTitle}>
                    {formatWithThousandSeparator(
                      new Big(amount)
                        .minus(new Big(amount).div(2).toFixed(2))
                        .toString(),
                    )}{' '}
                    (验收完成后支付)
                  </span>
                </div>
                <div>
                  <span className={style.payInstallAmount}>
                    应付金额：￥
                    {formatWithThousandSeparator(
                      new Big(amount).div(2).toFixed(2).toString(),
                    )}
                  </span>
                </div>
              </Col>

              <Col span={12} style={{ textAlign: 'right', paddingTop: 80 }}>
                <Space>
                  <Button
                    size="large"
                    style={{ fontSize: 18 }}
                    type="link"
                    onClick={previousStepCllick}
                  >
                    上一步
                  </Button>
                  {/* <div onClick={submitOrderClick} className={customer ? style.submitButton :style.disabledSubmitButton}>
                  提交订单
                </div> */}
                  <Button
                    className={styles.linearGradientButton}
                    disabled={customer ? false : true}
                    size="large"
                    loading={payLoading}
                    onClick={submitOrderClick}
                    type="primary"
                  >
                    提交订单
                  </Button>
                </Space>
              </Col>
            </Row>
          )}
        {!(
          cart.length > 0 &&
          cart[0].DeliveryMethod === 10 &&
          paymentPlan === 1
        ) && (
          <Row className={style.payBox}>
            <Col span={12} className={style.payAmountBox}>
              <span className={style.payAmountTitle}>商品总额：</span>
              <span className={style.payAmountUnit}>￥</span>
              <span className={style.payAmount}>
                {formatWithThousandSeparator(amount)}
              </span>
            </Col>

            <Col span={12} style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  size="large"
                  style={{ fontSize: 18 }}
                  type="link"
                  onClick={previousStepCllick}
                >
                  上一步
                </Button>
                {/* <div onClick={submitOrderClick} className={customer ? style.submitButton :style.disabledSubmitButton}>
                  提交订单
                </div> */}
                <Button
                  className={styles.linearGradientButton}
                  disabled={customer ? false : true}
                  size="large"
                  loading={payLoading}
                  onClick={submitOrderClick}
                  type="primary"
                >
                  提交订单
                </Button>
              </Space>
            </Col>
          </Row>
        )}
      </PageWrapper>
      <React.Suspense>
        <Protocol confirmCallBack={goPaymentPage} ref={protocolRef} />
      </React.Suspense>
    </div>
  );
};

export default Purchase;
