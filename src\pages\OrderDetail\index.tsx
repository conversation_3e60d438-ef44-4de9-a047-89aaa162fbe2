import PageWrapper from '@/components/PageWrapper';
import { getOrderDetail } from '@/services/order/PurchaseController';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from '@umijs/max';
import { Button, Skeleton } from 'antd';
import { useEffect, useState } from 'react';
import DetailPage from './components/DetailPage';
const OrderDetail = () => {
  const [menuType, setMenuType] = useState<string>('detail');
  const navigate = useNavigate();
  const location = useLocation();
  const [orderDetail, setOrderDetail] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(true);
  const { CustomOrderNumber, BackType } = location.state as any;
  useEffect(() => {
    if (CustomOrderNumber) {
      getOrderDetailData();
    } else {
      navigate('/home');
    }
  }, []);
  const getOrderDetailData = () => {
    setLoading(true);
    getOrderDetail(CustomOrderNumber).then((res) => {
      setLoading(false);
      setOrderDetail(res);
    });
  };
  const renderComponent = () => {
    switch (menuType) {
      case 'detail':
        return (
          <DetailPage
            customOrderNumber={CustomOrderNumber}
            orderDetail={orderDetail}
            getData={getOrderDetailData}
          />
        );
    }
  };
  const backClick = () => {
    if (BackType === 1) {
      navigate('/enterprise/orders');
    } else {
      navigate('/payment', { state: { CustomOrderNumber: CustomOrderNumber } });
    }
  };
  return (
    <div style={{ paddingTop: 2 }}>
      <PageWrapper>
        <Skeleton loading={loading} active>
          <div>
            <div>
              <Button
                style={{ marginLeft: '-6px' }}
                icon={<ArrowLeftOutlined />}
                type="text"
                onClick={backClick}
              ></Button>
              <span>订单详情</span>
            </div>

            <div>{renderComponent()}</div>
          </div>
        </Skeleton>
      </PageWrapper>
    </div>
  );
};

export default OrderDetail;
