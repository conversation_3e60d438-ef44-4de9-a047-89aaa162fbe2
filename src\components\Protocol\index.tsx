import workerUrl from '@/assets/js/pdf.worker.min.js';
import {
  getOrderContract,
  getOrderDetail,
  putOrderContract,
} from '@/services/order/PurchaseController';
import { ContractType, OrderModel } from '@/types';
import { digitUppercase } from '@/utils/currencyUtil';
import { dataToBlob } from '@/utils/fileUtils';
import { formatDate } from '@/utils/format';
import { createAndDownload, getPdfPreviewUrl } from '@/utils/pdfMake';
import { CheckOutlined } from '@ant-design/icons';
import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { Button, Col, Modal, Row } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
interface IProtocol {
  confirmCallBack: (customOrderNumber: any) => void;
}
const Protocol = forwardRef((props: IProtocol, ref: React.Ref<any>) => {
  const { confirmCallBack } = props;
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [protocolType, setProtocolType] = useState<number>(1);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [contractId, setContractId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [customOrderNumber, setCustomOrderNumber] = useState<string>('');
  useImperativeHandle(ref, () => ({
    modalHandleShow,
  }));
  function calculateSalesTax(salesPriceIncludingTax: any, taxRate: any) {
    let temTaxRate = taxRate / 100;
    const salesTax =
      Math.round(
        (salesPriceIncludingTax / (1 + temTaxRate)) * temTaxRate * 100,
      ) / 100;
    return salesTax;
  }
  const getOrders = (data: OrderModel, isMsp = false) => {
    const filterItems = data.OrderItems.filter((item) =>
      isMsp ? item.EnumType === 2 : item.EnumType !== 2,
    );
    const list = filterItems.map((item) => [
      item.SolutionName,
      item.SolutionAttributeCombinationName,
      item.Quantity,
      item.UnitPrice,
      item.TaxRatePercent,
      item.Total,
    ]);
    const reducer = (accumulator = 0, currentValue = 0) =>
      accumulator + currentValue;
    const total = data.OrderTotal;
    const taxAmount =
      filterItems && filterItems.length > 0
        ? filterItems
            .map((item) => +calculateSalesTax(item.Total, item.TaxRatePercent))
            .reduce(reducer)
        : 0;

    const actualAmount = ((total * 100 - taxAmount * 100) / 100).toFixed(2);
    let dist: any = [];
    dist = dist
      .concat([
        ['解决方案', '名称（物料）', '数量', '含税单价￥', '税率%', '总金额￥'],
      ])
      .concat(list)
      .concat([
        [
          {
            colSpan: 6,
            text: `总计（含税总金额）：￥${total}  大写（${digitUppercase(
              total,
            )}）,其中不含税总金额￥${actualAmount}，税额￥${taxAmount}`,
            alignment: 'left',
          },
        ],
      ]);
    return {
      marginTop: 20,
      marginBottom: 20,
      table: {
        widths: [160, 160, 40, 50, 40, 40],
        heights: 20,
        body: dist,
      },
    };
  };
  const logoFunction = () => {
    return `data:image/png;base64,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`;
  };
  const getDefalutProtocol = (data: OrderModel) => {
    const productOrders = getOrders(data);
    const dateNow = formatDate(new Date());
    const userList = data.OrderItems.map((item) => [
      item.SolutionName,
      item.Quantity,
      data.CustomerCompany,
      data.CustomerName,
      data.CustomerEmail,
      data.CustomerPhone,
    ]);
    let list: any[] = [];
    list = list
      .concat([['产品名称', '数量', '客户实体名称', '联系人', '邮箱', '电话']])
      .concat(userList);
    const paymentMethod = data.PaymentMethod;
    const fadada = data.ContractType === ContractType.FaDaDa;

    return {
      pageMargins: [20, 60, 20, 40],
      pageSize: 'A4',
      // watermark: {
      //   text: '上海蓝云网络科技有限公司 Shanghai Blue Cloud Technology Co ,Ltd.',
      //   color: '#f1f2f3',
      //   bold: true,
      //   italics: false,
      //   opacity: 0,
      // },
      header: {
        margin: 10,
        columns: [
          {
            image: logoFunction(),
            alignment: 'left',
            width: 80,
          },
          [
            { text: ' ' },
            {
              text: `合同编号:${data.ContractId}`,
              alignment: 'center',
              color: 'rgb(0,32,96)',
            },
          ],
          [
            { text: '上海蓝云网络科技有限公司', alignment: 'right' },
            {
              text: 'Shanghai Blue Cloud Technology Co., Ltd.',
              alignment: 'right',
            },
          ],
        ],
      },
      content: [
        {
          text: `有关软件产品的上海蓝云网络科技有限公司合作伙伴协议的订单表`,
          marginBottom: 20,
          fontSize: 18,
        },
        {
          text: `经销商（公司）名称：${data.BuyUserCompanyName}`,
          marginBottom: 5,
        },
        { text: `公司联系人姓名：${data.BuyUserName}`, marginBottom: 5 },
        { text: `公司联系人联系方式：${data.BuyUserPhone}`, marginBottom: 5 },
        { text: '供应商：上海蓝云网络科技有限公司', marginBottom: 5 },
        {
          text: `供应商联系人姓名：${data.BlueCloudSalesName || ''}`,
          marginBottom: 5,
        },
        {
          text: `供应商联系人联系方式：${data.BlueCloudSalesEmail || ''}`,
          marginBottom: 20,
        },
        {
          text: `本订单表是公司与上海蓝云网络科技有限公司（“上海蓝云”）达成的有关软件产品（以下称“产品”）的上海蓝云网络科技有限公司合作伙伴协议（“协议”，协议编号：${data.BlueCloudAggrementNo}）不可分割的组成部分，在公司与上海蓝云之间具有法律约束力。本订单表中未定义的术语，与协议中相关术语具有相同的含义。`,
          marginBottom: 10,
        },
        {
          text: '最终客户信息表（参见附录A）: 公司在向上海蓝云提交订单时,需提供准确的最终用户信息，进行购买产品的客户登记，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
          marginBottom: 10,
        },
        {
          text: `${data.OrderItems[0].SolutionName}  定价方式为按产品计价,货币为人民币。`,
          marginBottom: 10,
        },
        {
          text: `本订单支付方式为${paymentMethod}。公司在平台上提交订单后，立即向上海蓝云支付相应的订单款，上海蓝云在收到订单款及公司在平台上提交的开票申请后，向公司开具相应金额的发票。`,
          marginBottom: 10,
        },
        { text: '本订单产品交付方式：', marginBottom: 10 },
        {
          text: `产品交付： 上海蓝云在收到公司支付的订单款并确认订单信息无误后，于5个工作日内通过平台向公司交付产品及其附随的必要资料（以下称“交付物”）。若公司提交订单后未支付该笔订单的订单款，上海蓝云有权不向公司提供交付物，且自订单提交之日起30个自然日未能支付订单款的，上海蓝云有权随时在平台上取消该笔订单。上海蓝云在平台上传交付物后, 产品交付完成。公司应在收到交付物之后及时对交付物的完整性（包括但不限于产品的规格，数量和资料的完整性等）进行检查，并于收到交付物之后5个自然日内在平台上完成收货确认，如果公司未能在前述期限内确认收货的，系统将在第6个自然日自动确认收货，并视为公司已确认收货。如果公司对交付物有异议的，公司应在确认收货之前书面通知上海蓝云并提供有效证据，以便与上海蓝云就该有异议的交付物进行协商和妥善处理。确认收货视为公司对交付物无异议。`,
          marginBottom: 5,
        },
        {
          text: '本订单表下的产品存在不可回收性,在平台上经公司点击同意购买后,不可以进行退换货。 ',
          marginBottom: 5,
        },
        {
          text: '填写以下“订单部分（产品订单）”表格中的所有字段（必填）。',
          bold: true,
          marginBottom: 10,
        },

        { text: '产品订单：', bold: true },
        productOrders,

        // { text: '本订单表：', bold: true, marginBottom: 20 },
        // {
        //   text: `合计应收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotal
        //   } 大写（${digitUppercase(data.OrderTotal)}）.`,
        //   marginBottom: 5,
        // },

        // {
        //   text: `实收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotal
        //   } 大写（${digitUppercase(data.OrderTotal)}）.`,
        //   marginBottom: 15,
        // },
        {
          text: '如果国家税率发生变动,致本订单税率与国家的税率发生冲突,应以国家规定的税率为准。',
          marginBottom: 20,
        },
        {
          text: '本订单表、合作伙伴协议（经销商）共同组成完整的渠道销售合同。 本订单一经公司点击同意,即发生法律效力。',
          marginBottom: 20,
        },
        { text: '上海蓝云帐户信息：', marginBottom: 5 },
        { text: '开户银行: 中国工商银行国航大厦支行', marginBottom: 5 },
        { text: '账号：0200227919200053244', marginBottom: 10 },
        { text: `本订单表生效日期为：${dateNow}`, marginBottom: 5 },
        {
          marginTop: 50,
          columns: [
            {
              width: '50%',
              text: `公司：${data.BuyUserCompanyName}`,
            },
            {
              width: '50%',
              text: '供应商：上海蓝云网络科技有限公司',
            },
          ],
          columnGap: 10,
        },
        {
          columns: [
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
          ],
          columnGap: 10,
          marginTop: 50,
        },
        {
          columns: [
            {
              width: '50%',
              text: '',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '21vcontractseal',
              font: 'Roboto',
              color: 'white',
              opacity: 1,
              marginLeft: 80,
            },
          ],
          columnGap: 10,
          marginBottom: 70,
        },
        {
          columns: [
            {
              width: '50%',
              text: [
                '日期：',
                {
                  text: '        年    月    日',
                  fontSize: 10,
                  marginLeft: 20,
                },
              ],
            },
            {
              width: '50%',
              text: [
                '日期：',
                fadada
                  ? {
                      text: '21vdateseal',
                      font: 'Roboto',
                      color: 'white',
                      opacity: 0,
                      fontSize: 10,
                      marginLeft: 20,
                    }
                  : {
                      text: '        年    月    日',
                      fontSize: 10,
                      marginLeft: 20,
                    },
              ],
            },
          ],
          columnGap: 10,
          marginBottom: 80,
        },
        { text: '附录A', marginTop: 30, bold: true },
        {
          text: '最终客户信息表',
          alignment: 'center',
          fontSize: 18,
          marginBottom: 20,
          marginTop: 20,
        },
        {
          marginTop: 20,
          marginBottom: 20,
          table: {
            widths: ['17%', '16.6%', '16.6%', '16.6%', '16.6%', '16.6%'],
            heights: 20,
            body: list,
          },
        },
        {
          text: '请经销商准确填写，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
        },
      ],
      footer: (currentPage: number, pageCount: number) => ({
        style: ['footer'],
        columns: [
          { text: 'Blue Cloud', alignment: 'left' },
          {
            text: 'Page ' + currentPage.toString() + ' of ' + pageCount,
            alignment: 'center',
          },
          { text: 'Final& Confidential', alignment: 'right' },
        ],
      }),
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          heights: 160,
          margin: 20,
        },
        tableHeader: {
          fontSize: 10,
          bold: true,
          color: 'black',
        },
        menuStyle: {
          fontSize: 12,
          bold: true,
          color: 'black',
          margin: [0, 0, 0, 10],
        },
        olStyle: {
          fontSize: 10,
          bold: true,
          marginLeft: 20,
        },
        footer: {
          fontSize: 10,
          alignment: 'center',
          margin: 20,
        },
      },
      defaultStyle: {
        fontSize: 10,
        font: 'fangzhen',
        fontSmooth: 'auto',
        color: '#333',
        columnGap: 20,
      },
    };
  };
  const getInstalmentsProtocol = (data: OrderModel) => {
    const productOrders = getOrders(data);
    const dateNow = formatDate(new Date());
    const userList = data.OrderItems.map((item) => [
      item.SolutionName,
      item.Quantity,
      data.CustomerCompany,
      data.CustomerName,
      data.CustomerEmail,
      data.CustomerPhone,
    ]);
    let list: any[] = [];
    list = list
      .concat([['产品名称', '数量', '客户实体名称', '联系人', '邮箱', '电话']])
      .concat(userList);
    const paymentMethod = data.PaymentMethod;
    const fadada = data.ContractType === ContractType.FaDaDa;

    return {
      pageMargins: [20, 60, 20, 40],
      pageSize: 'A4',
      // watermark: {
      //   text: '上海蓝云网络科技有限公司 Shanghai Blue Cloud Technology Co ,Ltd.',
      //   color: '#f1f2f3',
      //   bold: true,
      //   italics: false,
      //   opacity: 0,
      // },
      header: {
        margin: 10,
        columns: [
          {
            image: logoFunction(),
            alignment: 'left',
            width: 80,
          },
          [
            { text: ' ' },
            {
              text: `合同编号:${data.ContractId}`,
              alignment: 'center',
              color: 'rgb(0,32,96)',
            },
          ],
          [
            { text: '上海蓝云网络科技有限公司', alignment: 'right' },
            {
              text: 'Shanghai Blue Cloud Technology Co., Ltd.',
              alignment: 'right',
            },
          ],
        ],
      },
      content: [
        {
          text: `有关软件产品的上海蓝云网络科技有限公司合作伙伴协议的订单表`,
          marginBottom: 20,
          fontSize: 18,
        },
        {
          text: `经销商（公司）名称：${data.BuyUserCompanyName}`,
          marginBottom: 5,
        },
        {
          text: `公司联系人姓名：${data.BuyUserName || ''}`,
          marginBottom: 5,
        },
        { text: `公司联系人联系方式：${data.BuyUserPhone}`, marginBottom: 5 },
        { text: '供应商名称：上海蓝云网络科技有限公司', marginBottom: 5 },
        {
          text: `供应商联系人姓名：${data.BlueCloudSalesName || ''}`,
          marginBottom: 5,
        },
        {
          text: `供应商联系人联系方式：${data.BlueCloudSalesEmail || ''}`,
          marginBottom: 20,
        },
        {
          text: `本订单表是公司与上海蓝云网络科技有限公司（“上海蓝云”）达成的有关软件产品（以下称“产品”）的上海蓝云网络科技有限公司合作伙伴协议（“协议”，协议编号：${data.BlueCloudAggrementNo}）不可分割的组成部分，在公司与上海蓝云之间具有法律约束力。本订单表中未定义的术语，与协议中相关术语具有相同的含义。`,
          marginBottom: 10,
        },
        {
          text: '最终客户信息表（参见附录A）: 公司在向上海蓝云提交订单时,需提供准确的最终用户信息，进行购买产品的客户登记，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
          marginBottom: 10,
        },
        {
          text: `${data.OrderItems[0].SolutionName} 定价方式为按产品计价，货币为人民币。`,
          marginBottom: 10,
        },
        {
          text: `本订单支付方式为${paymentMethod}。`,
          marginBottom: 10,
        },
        {
          text: `当公司具有账期支付许可,且选择用此方式支付本订单相关款项时,发票申请、产品交付流程依照本订单执行,公司需依据上海蓝云账期支付要求的账期进行偿付。本订单的账期为【${data.BillingPeriodDays}】天，账期的起算时间为本电子订单生效当日，即自本订单生效之日起30个自然日内，公司应当向上海蓝云支付本订单项下的订单款。`,
          marginBottom: 10,
        },
        {
          text: '订单生效且公司在平台上申请开票后, 上海蓝云向公司开具相应金额的发票。如因公司申请开票延迟，公司不得延迟支付此订单款项，上海蓝云仍依据上述约定时间节点收取订单款并且不承担任何责任。',
          marginBottom: 10,
        },
        { text: '本订单产品交付方式：', marginBottom: 10 },
        {
          text: `产品交付： 上海蓝云在收到公司通过平台提交的电子订单并确认无误后，于5个工作日内通过平台向公司交付产品及其附随的必要资料（以下称“交付物”）。上海蓝云在平台上传交付物后, 产品交付完成。公司应在收到交付物之后及时对交付物的完整性（包括但不限于产品的规格，数量和资料的完整性等）进行检查，并于收到交付物之后5个自然日内在平台上完成收货确认，如果公司未能在前述期限内确认收货的，系统将在第6个自然日自动确认收货，并视为公司已确认收货。如果公司对交付物有异议的，公司应在确认收货之前书面通知上海蓝云并提供有效证据，以便与上海蓝云就该有异议的交付物进行协商和妥善处理。确认收货视为公司对交付物无异议。`,
          marginBottom: 5,
        },
        {
          text: '本订单表下的产品存在不可回收性,在平台上经公司点击同意购买后,不可以进行退换货。',
          marginBottom: 5,
        },
        {
          text: '填写以下“订单部分（产品订单）”表格中的所有字段（必填）。',
          bold: true,
          marginBottom: 10,
        },

        { text: '产品订单：', bold: true },
        productOrders,
        // { text: '本订单表：', bold: true, marginBottom: 20 },
        // {
        //   text: `合计应收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotal
        //   } 大写（${digitUppercase(data.OrderTotal)}）.`,
        //   marginBottom: 5,
        // },
        // {
        //   text: `可抵扣优惠金额：￥ ${
        //     data.OrderDiscount
        //   } 大写（${digitUppercase(data.OrderDiscount)}）.`,
        //   marginBottom: 5,
        // },
        // {
        //   text: `实收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotalActual
        //   } 大写（${digitUppercase(data.OrderTotalActual)}）.`,
        //   marginBottom: 15,
        // },
        {
          text: '如果国家税率发生变动，致本订单税率与国家的税率发生冲突，应以国家规定的税率为准。',
          marginBottom: 20,
        },
        {
          text: '本订单表、合作伙伴协议（经销商）共同组成完整的渠道销售合同。 本订单一经公司点击同意,即发生法律效力。',
          marginBottom: 20,
        },
        { text: '上海蓝云帐户信息：', marginBottom: 5 },
        { text: '开户银行: 中国工商银行国航大厦支行', marginBottom: 5 },
        { text: '账号：0200227919200053244', marginBottom: 10 },
        { text: `本订单表生效日期为：${dateNow}`, marginBottom: 5 },
        {
          marginTop: 50,
          columns: [
            {
              width: '50%',
              text: `公司：${data.BuyUserCompanyName}`,
            },
            {
              width: '50%',
              text: '供应商：上海蓝云网络科技有限公司',
            },
          ],
          columnGap: 10,
        },
        {
          columns: [
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
          ],
          columnGap: 10,
          marginTop: 50,
        },
        {
          columns: [
            {
              width: '50%',
              text: '',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '21vcontractseal',
              font: 'Roboto',
              color: 'white',
              opacity: 1,
              marginLeft: 80,
            },
          ],
          columnGap: 10,
          marginBottom: 70,
        },
        {
          columns: [
            {
              width: '50%',
              text: [
                '日期：',
                {
                  text: '        年    月    日',
                  fontSize: 10,
                  marginLeft: 20,
                },
              ],
            },
            {
              width: '50%',
              text: [
                '日期：',
                fadada
                  ? {
                      text: '21vdateseal',
                      font: 'Roboto',
                      color: 'white',
                      opacity: 0,
                      fontSize: 10,
                      marginLeft: 20,
                    }
                  : {
                      text: '        年    月    日',
                      fontSize: 10,
                      marginLeft: 20,
                    },
              ],
            },
          ],
          columnGap: 10,
          marginBottom: 80,
        },
        { text: '附录A', marginTop: 30, bold: true },
        {
          text: '最终客户信息表',
          alignment: 'center',
          fontSize: 18,
          marginBottom: 20,
          marginTop: 20,
        },
        {
          marginTop: 20,
          marginBottom: 20,
          table: {
            widths: ['17%', '16.6%', '16.6%', '16.6%', '16.6%', '16.6%'],
            heights: 20,
            body: list,
          },
        },
        {
          text: '请经销商准确填写，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
        },
      ],
      footer: (currentPage: number, pageCount: number) => ({
        style: ['footer'],
        columns: [
          { text: 'Blue Cloud', alignment: 'left' },
          {
            text: 'Page ' + currentPage.toString() + ' of ' + pageCount,
            alignment: 'center',
          },
          { text: 'Final& Confidential', alignment: 'right' },
        ],
      }),
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          heights: 160,
          margin: 20,
        },
        tableHeader: {
          fontSize: 10,
          bold: true,
          color: 'black',
        },
        menuStyle: {
          fontSize: 12,
          bold: true,
          color: 'black',
          margin: [0, 0, 0, 10],
        },
        olStyle: {
          fontSize: 10,
          bold: true,
          marginLeft: 20,
        },
        footer: {
          fontSize: 10,
          alignment: 'center',
          margin: 20,
        },
      },
      defaultStyle: {
        fontSize: 10,
        font: 'fangzhen',
        fontSmooth: 'auto',
        color: '#333',
        columnGap: 20,
      },
    };
  };
  const getSaasProtocol = (data: OrderModel) => {
    const productOrders = getOrders(data);
    const dateNow = formatDate(new Date());
    const userList = data.OrderItems.map((item) => [
      item.SolutionName,
      item.Quantity,
      data.CustomerCompany,
      data.CustomerName,
      data.CustomerEmail,
      data.CustomerPhone,
      JSON.parse(item.PurchaseParameter)?.Email ?? '',
    ]);
    let list: any[] = [];
    list = list
      .concat([
        [
          '产品名称',
          '数量',
          '客户实体名称',
          '联系人',
          '邮箱',
          '电话',
          '开通服务邮箱',
        ],
      ])
      .concat(userList);
    const paymentMethod = data.PaymentMethod;
    const fadada = data.ContractType === ContractType.FaDaDa;

    return {
      pageMargins: [20, 60, 20, 40],
      pageSize: 'A4',
      header: {
        margin: 10,
        columns: [
          {
            image: logoFunction(),
            alignment: 'left',
            width: 80,
          },
          [
            { text: ' ' },
            {
              text: `合同编号:${data.ContractId}`,
              alignment: 'center',
              color: 'rgb(0,32,96)',
            },
          ],
          [
            { text: '上海蓝云网络科技有限公司', alignment: 'right' },
            {
              text: 'Shanghai Blue Cloud Technology Co., Ltd.',
              alignment: 'right',
            },
          ],
        ],
      },
      content: [
        {
          text: `有关软件产品的上海蓝云网络科技有限公司合作伙伴协议的订单表`,
          marginBottom: 20,
          fontSize: 18,
        },
        {
          text: `经销商（公司）名称：${data.BuyUserCompanyName}`,
          marginBottom: 5,
        },
        { text: `公司联系人姓名：${data.BuyUserName}`, marginBottom: 5 },
        { text: `公司联系人联系方式：${data.BuyUserPhone}`, marginBottom: 5 },
        { text: '供应商：上海蓝云网络科技有限公司', marginBottom: 5 },
        {
          text: `供应商联系人姓名：${data.BlueCloudSalesName || ''}`,
          marginBottom: 5,
        },
        {
          text: `供应商联系人联系方式：${data.BlueCloudSalesEmail || ''}`,
          marginBottom: 20,
        },
        {
          text: `本订单表是公司与上海蓝云网络科技有限公司（“上海蓝云”）达成的有关服务产品（以下称“产品”）的上海蓝云网络科技有限公司合作伙伴协议（“协议”，协议编号：${data.BlueCloudAggrementNo}）不可分割的组成部分，在公司与上海蓝云之间具有法律约束力。本订单表中未定义的术语，与协议中相关术语具有相同的含义。`,
          marginBottom: 10,
        },
        {
          text: '开通服务客户信息表（参见附录A）:  公司在向上海蓝云提交订单时,需提供准确的最终用户信息，进行购买产品的客户登记，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
          marginBottom: 10,
        },
        {
          text: `${data.OrderItems[0].SolutionName}  定价方式为按产品计价,货币为人民币。`,
          marginBottom: 10,
        },
        {
          text: `本订单支付方式为${paymentMethod}。公司在平台上提交订单后，立即向上海蓝云支付相应的订单款，上海蓝云在收到订单款及公司在平台上提交的开票申请后，向公司开具相应金额的发票。`,
          marginBottom: 10,
        },
        { text: '本订单产品交付方式：', marginBottom: 10 },
        {
          text: `服务产品交付： 上海蓝云在收到公司通过平台提交的电子订单并确认无误后，蓝云将通知服务提供方于5个工作日内根据订单提供的信息为最终客户开通服务并通知最终客户。若公司提交订单后未支付该笔订单的订单款，则订单将不会生效，且自订单提交之日起30个自然日未能支付订单款的，上海蓝云有权随时在平台上取消该笔订单。服务开通后，蓝云会向公司发出订单交付提醒。请公司在收到系统订单交付提醒5个自然日内对交付信息及状态进行核查，并在平台上完成收货确认，如果公司未能在前述期限内确认收货的，系统将在第6个自然日自动确认收货，并视为公司已确认收货。如果公司对服务交付有异议的，公司应在确认收货之前书面通知上海蓝云并提供有效证据，以便与上海蓝云就该异议进行协商和妥善处理。确认收货视为公司对服务开通无异议。`,
          marginBottom: 5,
        },
        {
          text: '本订单表下的服务产品,在平台上经公司点击同意购买后,不可以进行退换货。',
          marginBottom: 5,
        },
        {
          text: '填写以下“订单部分（产品订单）”表格中的所有字段（必填）。',
          bold: true,
          marginBottom: 10,
        },

        { text: '服务产品订单：', bold: true },
        productOrders,
        {
          text: '如果国家税率发生变动,致本订单税率与国家的税率发生冲突,应以国家规定的税率为准。',
          marginBottom: 20,
        },
        {
          text: '本订单表、合作伙伴协议（经销商）共同组成完整的渠道销售合同。 本订单一经公司点击同意,即发生法律效力。',
          marginBottom: 20,
        },
        { text: '上海蓝云帐户信息：', marginBottom: 5 },
        { text: '开户银行: 中国工商银行国航大厦支行', marginBottom: 5 },
        { text: '账号：0200227919200053244', marginBottom: 10 },
        { text: `本订单表生效日期为：${dateNow}`, marginBottom: 5 },
        {
          marginTop: 50,
          columns: [
            {
              width: '50%',
              text: `公司：${data.BuyUserCompanyName}`,
            },
            {
              width: '50%',
              text: '供应商：上海蓝云网络科技有限公司',
            },
          ],
          columnGap: 10,
        },
        {
          columns: [
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
          ],
          columnGap: 10,
          marginTop: 50,
        },
        {
          columns: [
            {
              width: '50%',
              text: '',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '21vcontractseal',
              font: 'Roboto',
              color: 'white',
              opacity: 1,
              marginLeft: 80,
            },
          ],
          columnGap: 10,
          marginBottom: 70,
        },
        {
          columns: [
            {
              width: '50%',
              text: [
                '日期：',
                {
                  text: '        年    月    日',
                  fontSize: 10,
                  marginLeft: 20,
                },
              ],
            },
            {
              width: '50%',
              text: [
                '日期：',
                fadada
                  ? {
                      text: '21vdateseal',
                      font: 'Roboto',
                      color: 'white',
                      opacity: 0,
                      fontSize: 10,
                      marginLeft: 20,
                    }
                  : {
                      text: '        年    月    日',
                      fontSize: 10,
                      marginLeft: 20,
                    },
              ],
            },
          ],
          columnGap: 10,
          marginBottom: 80,
        },
        { text: '附录A', marginTop: 30, bold: true },
        {
          text: '开通服务客户信息表',
          alignment: 'center',
          fontSize: 18,
          marginBottom: 20,
          marginTop: 20,
        },
        {
          marginTop: 20,
          marginBottom: 20,
          table: {
            widths: ['20%', '5%', '15%', '15%', '15%', '15%', '15%'],
            heights: 20,
            body: list,
          },
        },
        {
          text: '请经销商准确填写,以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
        },
      ],
      footer: (currentPage: number, pageCount: number) => ({
        style: ['footer'],
        columns: [
          { text: 'Blue Cloud', alignment: 'left' },
          {
            text: 'Page ' + currentPage.toString() + ' of ' + pageCount,
            alignment: 'center',
          },
          { text: 'Final& Confidential', alignment: 'right' },
        ],
      }),
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          heights: 160,
          margin: 20,
        },
        tableHeader: {
          fontSize: 10,
          bold: true,
          color: 'black',
        },
        menuStyle: {
          fontSize: 12,
          bold: true,
          color: 'black',
          margin: [0, 0, 0, 10],
        },
        olStyle: {
          fontSize: 10,
          bold: true,
          marginLeft: 20,
        },
        footer: {
          fontSize: 10,
          alignment: 'center',
          margin: 20,
        },
      },
      defaultStyle: {
        fontSize: 10,
        font: 'fangzhen',
        fontSmooth: 'auto',
        color: '#333',
        columnGap: 20,
      },
    };
  };
  const getSaasInstalmentsProtocol = (data: OrderModel) => {
    const productOrders = getOrders(data);
    const dateNow = formatDate(new Date());
    const userList = data.OrderItems.map((item) => [
      item.SolutionName,
      item.Quantity,
      data.CustomerCompany,
      data.CustomerName,
      data.CustomerEmail,
      data.CustomerPhone,
      JSON.parse(item.PurchaseParameter)?.Email ?? '',
    ]);
    let list: any[] = [];
    list = list
      .concat([
        [
          '产品名称',
          '数量',
          '客户实体名称',
          '联系人',
          '邮箱',
          '电话',
          '开通服务邮箱',
        ],
      ])
      .concat(userList);
    const paymentMethod = data.PaymentMethod;
    const fadada = data.ContractType === ContractType.FaDaDa;

    return {
      pageMargins: [20, 60, 20, 40],
      pageSize: 'A4',
      header: {
        margin: 10,
        columns: [
          {
            image: logoFunction(),
            alignment: 'left',
            width: 80,
          },
          [
            { text: ' ' },
            {
              text: `合同编号:${data.ContractId}`,
              alignment: 'center',
              color: 'rgb(0,32,96)',
            },
          ],
          [
            { text: '上海蓝云网络科技有限公司', alignment: 'right' },
            {
              text: 'Shanghai Blue Cloud Technology Co., Ltd.',
              alignment: 'right',
            },
          ],
        ],
      },
      content: [
        {
          text: `有关软件产品的上海蓝云网络科技有限公司合作伙伴协议的订单表`,
          marginBottom: 20,
          fontSize: 18,
        },
        {
          text: `经销商（公司）名称：${data.BuyUserCompanyName}`,
          marginBottom: 5,
        },
        {
          text: `公司联系人姓名：${data.BuyUserName || ''}`,
          marginBottom: 5,
        },
        { text: `公司联系人联系方式：${data.BuyUserPhone}`, marginBottom: 5 },
        { text: '供应商名称：上海蓝云网络科技有限公司', marginBottom: 5 },
        {
          text: `供应商联系人姓名：${data.BlueCloudSalesName || ''}`,
          marginBottom: 5,
        },
        {
          text: `供应商联系人联系方式：${data.BlueCloudSalesEmail || ''}`,
          marginBottom: 20,
        },
        {
          text: `本订单表是公司与上海蓝云网络科技有限公司（“上海蓝云”）达成的有关服务产品（以下称“产品”）的上海蓝云网络科技有限公司合作伙伴协议（“协议”，协议编号：${data.BlueCloudAggrementNo}）不可分割的组成部分，在公司与上海蓝云之间具有法律约束力。本订单表中未定义的术语，与协议中相关术语具有相同的含义。`,
          marginBottom: 10,
        },
        {
          text: '开通服务客户信息表（参见附录A）: 公司在向上海蓝云提交订单时,需提供准确的最终用户信息，进行购买产品的客户登记，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
          marginBottom: 10,
        },
        {
          text: `${data.OrderItems[0].SolutionName} 定价方式为按产品计价，货币为人民币。`,
          marginBottom: 10,
        },
        {
          text: `本订单支付方式为${paymentMethod}。`,
          marginBottom: 10,
        },
        {
          text: `当公司具有账期支付许可,且选择用此方式支付本订单相关款项时,发票申请、产品交付流程依照本订单执行,公司需依据上海蓝云账期支付要求的账期进行偿付。本订单的账期为【${data.BillingPeriodDays}】天，账期的起算时间为本电子订单生效当日，即自本订单生效之日起30个自然日内，公司应当向上海蓝云支付本订单项下的订单款。`,
          marginBottom: 10,
        },
        {
          text: '订单生效且公司在平台上申请开票后, 上海蓝云向公司开具相应金额的发票。如因公司申请开票延迟，公司不得延迟支付此订单款项，上海蓝云仍依据上述约定时间节点收取订单款并且不承担任何责任。',
          marginBottom: 10,
        },
        { text: '本订单产品交付方式：', marginBottom: 10 },
        {
          text: `服务产品交付： 上海蓝云在收到公司通过平台提交的电子订单并确认无误后，蓝云将通知服务提供方于5个工作日内根据订单提供的信息为最终客户开通服务并通知最终客户。服务开通后，蓝云会向公司发出订单交付提醒。请公司在收到系统订单交付提醒5个自然日内对交付信息及状态进行核查，并在平台上完成收货确认，如果公司未能在前述期限内确认收货的，系统将在第6个自然日自动确认收货，并视为公司已确认收货。如果公司对服务交付有异议的，公司应在确认收货之前书面通知上海蓝云并提供有效证据，以便与上海蓝云就该异议进行协商和妥善处理。确认收货视为公司对服务开通无异议。`,
          marginBottom: 5,
        },
        {
          text: '本订单表下的产品存在不可回收性,在平台上经公司点击同意购买后,不可以进行退换货。',
          marginBottom: 5,
        },
        {
          text: '填写以下“订单部分（产品订单）”表格中的所有字段（必填）。',
          bold: true,
          marginBottom: 10,
        },

        { text: '服务产品订单：', bold: true },
        productOrders,
        // { text: '本订单表：', bold: true, marginBottom: 20 },
        // {
        //   text: `合计应收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotal
        //   } 大写（${digitUppercase(data.OrderTotal)}）.`,
        //   marginBottom: 5,
        // },
        // {
        //   text: `可抵扣优惠金额：￥ ${
        //     data.OrderDiscount
        //   } 大写（${digitUppercase(data.OrderDiscount)}）.`,
        //   marginBottom: 5,
        // },
        // {
        //   text: `实收金额（含产品订单和服务/部署订单）：￥ ${
        //     data.OrderTotalActual
        //   } 大写（${digitUppercase(data.OrderTotalActual)}）.`,
        //   marginBottom: 15,
        // },
        {
          text: '如果国家税率发生变动，致本订单税率与国家的税率发生冲突，应以国家规定的税率为准。',
          marginBottom: 20,
        },
        {
          text: '本订单表、合作伙伴协议（经销商）共同组成完整的渠道销售合同。 本订单一经公司点击同意,即发生法律效力。',
          marginBottom: 20,
        },
        { text: '上海蓝云帐户信息：', marginBottom: 5 },
        { text: '开户银行: 中国工商银行国航大厦支行', marginBottom: 5 },
        { text: '账号：0200227919200053244', marginBottom: 10 },
        { text: `本订单表生效日期为：${dateNow}`, marginBottom: 5 },
        {
          marginTop: 50,
          columns: [
            {
              width: '50%',
              text: `公司：${data.BuyUserCompanyName}`,
            },
            {
              width: '50%',
              text: '供应商：上海蓝云网络科技有限公司',
            },
          ],
          columnGap: 10,
        },
        {
          columns: [
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
          ],
          columnGap: 10,
          marginTop: 50,
        },
        {
          columns: [
            {
              width: '50%',
              text: '',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '21vcontractseal',
              font: 'Roboto',
              color: 'white',
              opacity: 1,
              marginLeft: 80,
            },
          ],
          columnGap: 10,
          marginBottom: 70,
        },
        {
          columns: [
            {
              width: '50%',
              text: [
                '日期：',
                {
                  text: '        年    月    日',
                  fontSize: 10,
                  marginLeft: 20,
                },
              ],
            },
            {
              width: '50%',
              text: [
                '日期：',
                fadada
                  ? {
                      text: '21vdateseal',
                      font: 'Roboto',
                      color: 'white',
                      opacity: 0,
                      fontSize: 10,
                      marginLeft: 20,
                    }
                  : {
                      text: '        年    月    日',
                      fontSize: 10,
                      marginLeft: 20,
                    },
              ],
            },
          ],
          columnGap: 10,
          marginBottom: 80,
        },
        { text: '附录A', marginTop: 30, bold: true },
        {
          text: '最终客户信息表',
          alignment: 'center',
          fontSize: 18,
          marginBottom: 20,
          marginTop: 20,
        },
        {
          marginTop: 20,
          marginBottom: 20,
          table: {
            widths: ['20%', '5%', '15%', '15%', '15%', '15%', '15%'],
            heights: 20,
            body: list,
          },
        },
        {
          text: '请经销商准确填写，以便有关方在提供服务时能准确判断出是否是我们的最终客户。',
        },
      ],
      footer: (currentPage: number, pageCount: number) => ({
        style: ['footer'],
        columns: [
          { text: 'Blue Cloud', alignment: 'left' },
          {
            text: 'Page ' + currentPage.toString() + ' of ' + pageCount,
            alignment: 'center',
          },
          { text: 'Final& Confidential', alignment: 'right' },
        ],
      }),
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          heights: 160,
          margin: 20,
        },
        tableHeader: {
          fontSize: 10,
          bold: true,
          color: 'black',
        },
        menuStyle: {
          fontSize: 12,
          bold: true,
          color: 'black',
          margin: [0, 0, 0, 10],
        },
        olStyle: {
          fontSize: 10,
          bold: true,
          marginLeft: 20,
        },
        footer: {
          fontSize: 10,
          alignment: 'center',
          margin: 20,
        },
      },
      defaultStyle: {
        fontSize: 10,
        font: 'fangzhen',
        fontSmooth: 'auto',
        color: '#333',
        columnGap: 20,
      },
    };
  };
  const geAiInstalmentsProtocol = (data: OrderModel) => {
    const productOrders = getOrders(data);
    const dateNow = formatDate(new Date());
    const userList = data.OrderItems.map((item) => [
      item.SolutionName,
      item.Quantity,
      data.CustomerCompany,
      data.CustomerName,
      data.CustomerEmail,
      data.CustomerPhone,
    ]);
    let list: any[] = [];
    list = list
      .concat([['产品名称', '数量', '客户实体名称', '联系人', '邮箱', '电话']])
      .concat(userList);
    const paymentMethod = data.PaymentMethod;
    const fadada = data.ContractType === ContractType.FaDaDa;

    return {
      pageMargins: [20, 60, 20, 40],
      pageSize: 'A4',
      header: {
        margin: 10,
        columns: [
          {
            image: logoFunction(),
            alignment: 'left',
            width: 80,
          },
          [
            { text: ' ' },
            {
              text: `合同编号:${data.ContractId}`,
              alignment: 'center',
              color: 'rgb(0,32,96)',
            },
          ],
          [
            { text: '上海蓝云网络科技有限公司', alignment: 'right' },
            {
              text: 'Shanghai Blue Cloud Technology Co., Ltd.',
              alignment: 'right',
            },
          ],
        ],
      },
      content: [
        {
          text: `有关软件产品的上海蓝云网络科技有限公司合作伙伴协议的订单表`,
          marginBottom: 20,
          fontSize: 18,
        },
        {
          text: `经销商（公司）名称：${data.BuyUserCompanyName}`,
          marginBottom: 5,
        },
        {
          text: `公司联系人姓名：${data.BuyUserName || ''}`,
          marginBottom: 5,
        },
        { text: `公司联系人联系方式：${data.BuyUserPhone}`, marginBottom: 5 },
        { text: '供应商名称：上海蓝云网络科技有限公司', marginBottom: 5 },
        {
          text: `供应商联系人姓名：${data.BlueCloudSalesName || ''}`,
          marginBottom: 5,
        },
        {
          text: `供应商联系人联系方式：${data.BlueCloudSalesEmail || ''}`,
          marginBottom: 20,
        },
        {
          text: `本订单表是公司与上海蓝云网络科技有限公司（“上海蓝云”）达成的有关     蓝云AI一体机（以下称“产品”）的上海蓝云网络科技有限公司合作伙伴协议（“协议”，协议编号：${data.BlueCloudAggrementNo}）不可分割的组成部分，在公司与上海蓝云之间具有法律约束力。本订单表中未定义的术语，与协议中相关术语具有相同的含义。`,
          marginBottom: 10,
        },
        {
          text: `产品订单:`,
          marginBottom: 10,
        },
        {
          text: `请填写以下“订单部分（产品订单）”表格中的所有字段（必填）:`,
          marginBottom: 10,
        },
        productOrders,
        {
          text: '如果国家税率发生变动，致本订单税率与国家的税率发生冲突，应以国家规定的税率为准。',
          marginBottom: 10,
        },
        {
          text: `一体机最终客户信息表（参见附录A）: 公司在向上海蓝云提交订单时,需提供准确的最终用户信息，进行购买产品的客户登记，以便一体机能通过物流配送到最终客户。`,
          marginBottom: 10,
        },
        {
          text: `付款`,
          marginBottom: 10,
          fontSize: 12,
          bold: true,
        },
        {
          text: `1.   ${data.OrderItems[0].SolutionName} 定价方式为按产品计价，货币为人民币。`,
          marginBottom: 5,
        },
        {
          text: `2.   本订单支付方式为${
            data.PaymentPlanId === 1 ? '首付款50%+尾款50%' : '一次性全额付款'
          }方式。`,
          marginBottom: 5,
        },
        {
          text: `3.    ${
            data.PaymentPlanId === 0
              ? '公司在平台上提交订单后，立即向上海蓝云支付相应的订单款，上海蓝云在收到订单款及公司在平台上提交的开票申请后，向公司开具相应金额的发票。'
              : '公司在平台上提交订单后，立即向上海蓝云支付订单金额50%的首付款，产品到货验收合格后15日内支付50%尾款，上海蓝云在收到订单款及公司在平台上提交的开票申请后，向公司开具相应金额的发票。'
          }`,
          marginBottom: 10,
        },
        {
          text: `交付`,
          marginBottom: 10,
          fontSize: 12,
          bold: true,
        },
        {
          text: `1.   公司完成${
            data.PaymentPlanId === 0 ? '付款' : '首付款'
          }后22天发货。`,
          marginBottom: 5,
        },
        { text: `2.   交货地址：${data.ReceiveAddress}。`, marginBottom: 5 },
        { text: '3.   交付方式：物流配送。', marginBottom: 10 },
        {
          text: `质量及验收`,
          marginBottom: 10,
          fontSize: 12,
          bold: true,
        },
        {
          text: '1.   质量标准：产品应符合国家标准、行业标准、原生产厂商出厂标准。',
          marginBottom: 5,
        },
        {
          text: `2.   验收期限：公司应在收货后3天内完成验收，逾期未验收的视为验收合格。`,
          marginBottom: 5,
        },
        {
          text: '3.   保修期限：硬件提供3年免费维保，自产品验收合格之日起算。',
          marginBottom: 10,
        },
        {
          text: `上海蓝云收款帐户信息:`,
          marginBottom: 10,
          fontSize: 12,
          bold: true,
        },
        { text: '开户银行: 中国工商银行国航大厦支行', marginBottom: 5 },
        { text: '账号：0200227919200053244', marginBottom: 10 },
        {
          text: `其他条款:`,
          marginBottom: 10,
          fontSize: 12,
          bold: true,
        },
        {
          text: '本订单表、合作伙伴协议（经销商）共同组成完整的渠道销售合同。本订单一经公司点击同意,即发生法律效力。',
          marginBottom: 10,
        },
        { text: `本订单表生效日期为：${dateNow}`, marginBottom: 5 },
        {
          marginTop: 50,
          columns: [
            {
              width: '50%',
              text: `公司：${data.BuyUserCompanyName}`,
            },
            {
              width: '50%',
              text: '供应商：上海蓝云网络科技有限公司',
            },
          ],
          columnGap: 10,
        },
        {
          columns: [
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '（盖章）',
              marginLeft: 100,
            },
          ],
          columnGap: 10,
          marginTop: 50,
        },
        {
          columns: [
            {
              width: '50%',
              text: '',
              marginLeft: 100,
            },
            {
              width: '50%',
              text: '21vcontractseal',
              font: 'Roboto',
              color: 'white',
              opacity: 1,
              marginLeft: 80,
            },
          ],
          columnGap: 10,
          marginBottom: 70,
        },
        {
          columns: [
            {
              width: '50%',
              text: [
                '日期：',
                {
                  text: '        年    月    日',
                  fontSize: 10,
                  marginLeft: 20,
                },
              ],
            },
            {
              width: '50%',
              text: [
                '日期：',
                fadada
                  ? {
                      text: '21vdateseal',
                      font: 'Roboto',
                      color: 'white',
                      opacity: 0,
                      fontSize: 10,
                      marginLeft: 20,
                    }
                  : {
                      text: '        年    月    日',
                      fontSize: 10,
                      marginLeft: 20,
                    },
              ],
            },
          ],
          columnGap: 10,
          marginBottom: 80,
        },
        { text: '附录A', marginTop: 30, bold: true },
        {
          text: '一体机客户信息表',
          alignment: 'center',
          fontSize: 18,
          marginBottom: 20,
          marginTop: 20,
        },
        {
          marginTop: 20,
          marginBottom: 20,
          table: {
            widths: ['25%', '15%', '15%', '15%', '15%', '15%'],
            heights: 20,
            body: list,
          },
        },
      ],
      footer: (currentPage: number, pageCount: number) => ({
        style: ['footer'],
        columns: [
          { text: 'Blue Cloud', alignment: 'left' },
          {
            text: 'Page ' + currentPage.toString() + ' of ' + pageCount,
            alignment: 'center',
          },
          { text: 'Final& Confidential', alignment: 'right' },
        ],
      }),
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          heights: 160,
          margin: 20,
        },
        tableHeader: {
          fontSize: 10,
          bold: true,
          color: 'black',
        },
        menuStyle: {
          fontSize: 12,
          bold: true,
          color: 'black',
          margin: [0, 0, 0, 10],
        },
        olStyle: {
          fontSize: 10,
          bold: true,
          marginLeft: 20,
        },
        footer: {
          fontSize: 10,
          alignment: 'center',
          margin: 20,
        },
      },
      defaultStyle: {
        fontSize: 10,
        font: 'fangzhen',
        fontSmooth: 'auto',
        color: '#333',
        columnGap: 20,
      },
    };
  };
  const generateUUID = () => {
    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };
  const getProtocolDef = (data: OrderModel) => {
    const uuid = generateUUID();
    setContractId(uuid);
    data.ContractId = uuid;
    if (
      (data.PaymentMethodId === 1 || data.PaymentMethodId === 2) &&
      data.DeliveryMethod === 1
    ) {
      return getSaasProtocol(data);
    } else if (data.PaymentMethodId === 3 && data.DeliveryMethod === 1) {
      return getSaasInstalmentsProtocol(data);
    } else if (data.DeliveryMethod === 10) {
      return geAiInstalmentsProtocol(data);
    } else if (data.PaymentMethodId === 3) {
      return getInstalmentsProtocol(data);
    } else {
      return getDefalutProtocol(data);
    }
  };
  const dowloadClick = () => {
    let data: any = {
      OrderItems: [
        {
          SolutionName: 'Test',
        },
      ],
    };
    let result = getProtocolDef(data);
    createAndDownload(result, 'test.pdf');
  };

  const modalHandleShow = (type: number, customOrderNumber: any) => {
    setProtocolType(type);
    setIsModalOpen(true);
    setCustomOrderNumber(customOrderNumber);
    if (type === 1) {
      setLoading(true);
      getOrderDetail(customOrderNumber).then((res) => {
        let result = getProtocolDef(res);
        getPdfPreviewUrl(result).then((fileData: any) => {
          setLoading(false);
          setPdfUrl(fileData);
        });
      });
    } else {
      setLoading(true);
      getOrderContract(customOrderNumber).then((res) => {
        setDownloadUrl(res.contractUrl);
        setLoading(false);
        setPdfUrl(res.contractUrl);
      });

      // getPDFData(data).then(res=>{
      //     setIsModalOpen(true);
      //     setLoading(false);
      //     setPdfUrl(data)
      // })
    }
  };
  const modalHandleOk = () => {
    setConfirmLoading(true);
    const blob = dataToBlob(pdfUrl);
    putOrderContract(customOrderNumber, contractId, {
      file: blob,
    }).then((res) => {
      setIsModalOpen(false);
      setConfirmLoading(false);
      if (confirmCallBack) {
        confirmCallBack(customOrderNumber);
      }
    });
  };
  const modalHandleCancel = () => {
    setIsModalOpen(false);
    if (confirmCallBack) {
      confirmCallBack(customOrderNumber);
    }
  };
  const modalFonter = () => (
    <div
      style={{
        borderTop: '1px solid rgba(229, 229, 229, 1)',
        lineHeight: '44px',
        height: '44px',
        paddingLeft: 32,
        paddingRight: 32,
      }}
    >
      {protocolType === 1 && (
        <Row>
          <Col span={12} style={{ textAlign: 'left' }}>
            *签署方式一经确认不可更改
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              loading={confirmLoading}
              onClick={modalHandleOk}
            >
              确认
            </Button>
          </Col>
        </Row>
      )}
      {protocolType === 2 && (
        <Button
          target="_blank"
          type="link"
          href={downloadUrl}
          onClick={dowloadClick}
        >
          下载电子合同
        </Button>
      )}
    </div>
  );

  return (
    <Modal
      destroyOnHidden={false}
      title={protocolType === 1 ? '生成电子订单' : '查看电子订单'}
      open={isModalOpen}
      width={'50%'}
      footer={modalFonter}
      onCancel={modalHandleCancel}
      onOk={modalHandleOk}
      loading={loading}
      styles={{
        content: {
          padding: 0,
        },
        header: {
          padding: 16,
        },
      }}
    >
      <div style={{ height: '60vh', overflow: 'auto', width: '100%' }}>
        {pdfUrl && (
          <Worker workerUrl={workerUrl}>
            <div style={{ width: '100%' }}>
              <Viewer fileUrl={pdfUrl} />
            </div>
          </Worker>
        )}
      </div>
    </Modal>
  );
});

export default Protocol;
