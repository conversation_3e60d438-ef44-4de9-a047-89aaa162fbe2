import { activationsUser, getActivationsUser } from '@/services/user';
import { history, useLocation } from '@umijs/max';
import { Form, Input, notification, Radio } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  container: css`
    width: 100%;
    min-height: 100vh;
    position: relative;
  `,
  title: css`
    font-size: 24px;
    color: rgba(56, 56, 56, 1);
    font-weight: 500;
    line-height: 50px;
  `,
  bgImg: css`
    width: 100%;
    height: 60vh;
    opacity: 1;
    background-image: url(${require('@/assets/inviteBg.png')});
    background-repeat: no-repeat;
    background-size: cover;
  `,
  bgGradient: css`
    width: 100%;
    opacity: 0.1;
    height: 60vh;
    background: linear-gradient(
      180deg,
      rgb(247, 249, 252) 0%,
      rgb(247, 249, 252) 100%
    );
  `,
  bgBottom: css`
    width: 100%;
    height: 40vh;
    background: linear-gradient(
      180deg,
      rgb(237, 244, 255) 0%,
      rgb(255, 255, 255) 100%
    );
  `,
  centerBox: css`
    position: absolute;
    left: 50%;
    top: 120px;
    padding: 32px 28px 28px 28px;
    box-shadow: 0px 2px 2px rgba(255, 255, 255, 0.25),
      0px 2px 8px rgba(36, 109, 227, 0.1), 0px 4px 16px rgba(36, 109, 227, 0.15);
    background: #ffffff;
    border-radius: 10px;
    transform: translateX(-50%);
    width: 460px;
    height: 590px;
    text-align: left;
  `,
  confirmText: css`
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
  `,
  confirmLink: css`
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    color: rgba(17, 61, 237, 100%);
  `,
  confirmButton: css`
    width: 100%;
    height: 46px;
    opacity: 1;
    border-radius: 8px;
    background: linear-gradient(
      132.34deg,
      rgba(17, 75, 237, 1) 0%,
      rgba(54, 152, 217, 1) 69.77%,
      rgba(117, 225, 255, 1) 100%
    );
    display: inline-block;
    color: #fff;
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    &:hover {
      color: #fff !important;
      background: linear-gradient(
        224.07deg,
        rgb(68, 134, 205) 0%,
        rgb(13, 5, 247) 80%
      ) !important;
    }
  `,
}));
export default () => {
  const [api, contextHolder] = notification.useNotification();
  const [form] = Form.useForm();
  const { styles } = useStyles();
  const [token, setToken] = useState<any>();
  const [confirm, setConfirm] = useState<any>();
  const location = useLocation();
  useEffect(() => {
    const params = new URLSearchParams(window.location.search); // 获取查询字符串
    const temToken = params.get('token');
    const temEmail = params.get('email');
    setToken(temToken);
    getActivationsUser({ token: temToken, email: temEmail }).then((res) => {
      form.setFieldsValue(res);
    });
  }, []);
  const onFinish = async (values: any) => {
    const { Confirm, Password, ConfirmPassword } = values;
    console.log('Confirm', Confirm);
    if (!Confirm) {
      form.setFields([{ name: ['Confirm'], errors: ['请勾选用户协议'] }]);
      return;
    }
    if (Password !== ConfirmPassword) {
      form.setFields([{ name: ['ConfirmPassword'], errors: ['密码不一致'] }]);
      return;
    }
    values.token = token;
    activationsUser(values).then(
      (res: any) => {
        api['success']({
          message: '用户激活成功',
          duration: 2,
        });
        history.push('/login');
      },
      () => {
        api['error']({
          message: '用户激活失败',
          duration: 2,
        });
      },
    );
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className={styles.container}>
      {contextHolder}
      <div className={styles.bgGradient}>
        <div className={styles.bgImg}></div>
      </div>
      <div className={styles.bgBottom}></div>

      <div className={styles.centerBox}>
        <div className={styles.title}>邀请用户</div>

        <Form
          form={form}
          name="basic"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            name="Company"
            style={{ marginTop: 16, marginBottom: 16 }}
            rules={[{ required: false, message: '请输入公司' }]}
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            style={{ marginTop: 16, marginBottom: 16 }}
            name="Email"
            rules={[{ required: false, message: '请输入邮箱' }]}
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            style={{ marginTop: 16, marginBottom: 16 }}
            name="Name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          <Form.Item
            style={{ marginTop: 16, marginBottom: 16 }}
            name="Phone"
            rules={[
              { required: true, message: '请输入正确的电话' },
              { pattern: /^([-()+ 0-9]+)$/, message: '请输入正确的电话' },
            ]}
          >
            <Input placeholder="请输入电话" />
          </Form.Item>
          <Form.Item
            style={{ marginTop: 16, marginBottom: 16 }}
            name="Password"
            rules={[
              { required: true, message: '请输入密码' },
              {
                min: 6,
                message: '密码长度不能小于6位',
              },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*$/,
                message: '密码必须包含大小写字母和数字',
              },
            ]}
          >
            <Input.Password placeholder="请输入密码，必须包含大小写字母和数字，长度不小于六位" />
          </Form.Item>

          <Form.Item
            style={{ marginTop: 16, marginBottom: 16 }}
            name="ConfirmPassword"
            rules={[{ required: true, message: '请输入确认密码' }]}
          >
            <Input.Password placeholder="确认密码" />
          </Form.Item>

          <Form.Item
            name={'Confirm'}
            valuePropName="checked"
            initialValue={false}
            rules={[{ required: false, message: '请勾选同意' }]}
          >
            <Radio>
              <span className={styles.confirmText}>请您确认阅读并同意</span>
              <a
                target="_blank"
                className={styles.confirmLink}
                href="/reg/privacyStatement"
              >
                《个人信息使用协议》
              </a>
              <a
                target="_blank"
                className={styles.confirmLink}
                href="/reg/privacyStatement"
              >
                《合作伙伴网络协议》
              </a>
            </Radio>
          </Form.Item>
          <Form.Item>
            <div
              onClick={() => {
                form.submit();
              }}
              className={styles.confirmButton}
            >
              注册
            </div>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};
