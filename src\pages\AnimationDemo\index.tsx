import AnimatedSection from '@/components/AnimatedSection';
import FeatureCards, { sampleFeatures } from '@/components/FeatureCards';
import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';
import { <PERSON><PERSON>, Card, Col, Divider, Row, Space, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const { Title, Paragraph } = Typography;

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  `,
  hero: css`
    padding: 120px 20px 80px;
    text-align: center;
    color: white;
    
    h1 {
      color: white !important;
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 24px;
    }
    
    p {
      color: rgba(255, 255, 255, 0.9) !important;
      font-size: 20px;
      max-width: 600px;
      margin: 0 auto 40px;
    }
  `,
  content: css`
    background: white;
    min-height: 100vh;
  `,
  section: css`
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
  `,
  demoCard: css`
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 12px;
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  `,
  codeBlock: css`
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    overflow-x: auto;
  `,
}));

/**
 * 动画演示页面 - 展示 useScrollAnimation Hook 的各种用法
 */
const AnimationDemo: React.FC = () => {
  const { styles } = useStyles();

  // 英雄区域动画
  const heroRef = useScrollAnimation({
    selector: '.hero-item',
    initialY: 80,
    initialOpacity: 0,
    duration: 1.2,
    stagger: 0.3,
    ease: 'power3.out',
    start: 'top 90%',
  });

  // 代码示例动画
  const codeRef = useScrollAnimation(animationPresets.fadeInLeft);

  // 自定义3D动画
  const customRef = useScrollAnimation({
    selector: '.custom-demo',
    customAnimation: {
      from: { 
        rotationX: -90, 
        opacity: 0,
        transformOrigin: 'center bottom',
        z: -100
      },
      to: { 
        rotationX: 0, 
        opacity: 1, 
        z: 0,
        duration: 1.5,
        stagger: 0.2,
        ease: 'power2.out'
      },
    },
  });

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className={styles.container}>
      {/* 英雄区域 */}
      <div className={styles.hero} ref={heroRef}>
        <Title level={1} className="hero-item">
          useScrollAnimation Hook
        </Title>
        <Paragraph className="hero-item">
          强大而灵活的滚动动画解决方案，让您的页面更加生动有趣
        </Paragraph>
        <Space className="hero-item">
          <Button type="primary" size="large" onClick={scrollToTop}>
            开始体验
          </Button>
          <Button size="large" ghost>
            查看文档
          </Button>
        </Space>
      </div>

      <div className={styles.content}>
        {/* 基础用法演示 */}
        <div className={styles.section}>
          <Title level={2}>基础用法</Title>
          <Paragraph>
            最简单的使用方式，只需要一行代码即可为元素添加滚动动画效果。
          </Paragraph>
          
          <div ref={codeRef}>
            <div className={`${styles.codeBlock} animate-item`}>
              {`const containerRef = useScrollAnimation();

return (
  <div ref={containerRef}>
    <div className="animate-item">这个元素会有动画</div>
  </div>
);`}
            </div>
          </div>
        </div>

        <Divider />

        {/* 预设动画演示 */}
        <FeatureCards
          title="预设动画效果"
          subtitle="提供多种常用的动画预设，开箱即用"
          features={sampleFeatures.slice(0, 3)}
          animationType="scale"
        />

        <Divider />

        {/* 自定义动画演示 */}
        <div className={styles.section}>
          <Title level={2}>自定义3D动画</Title>
          <Paragraph>
            支持完全自定义的动画效果，包括3D变换、复杂的缓动函数等。
          </Paragraph>
          
          <Row gutter={[24, 24]} ref={customRef}>
            {[1, 2, 3, 4].map((item) => (
              <Col span={6} key={item}>
                <div className={`${styles.demoCard} custom-demo`}>
                  3D 卡片 {item}
                </div>
              </Col>
            ))}
          </Row>
        </div>

        <Divider />

        {/* 完整示例 */}
        <AnimatedSection />

        {/* 更多特性 */}
        <FeatureCards
          title="更多特性"
          subtitle="探索更多强大的功能和特性"
          features={sampleFeatures.slice(3)}
          animationType="left"
        />
      </div>
    </div>
  );
};

export default AnimationDemo;
