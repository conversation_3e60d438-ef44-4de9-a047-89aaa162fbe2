import { COMPANY_STATUS } from '@/constants';
import { submitCompanyInfo } from '@/services/enterprise';
import useUserStore, { getCompany } from '@/store/user';
import DeleteOutlined from '@ant-design/icons/lib/icons/DeleteOutlined';
import EyeOutlined from '@ant-design/icons/lib/icons/EyeOutlined';
import {
  Button,
  Card,
  Col,
  Flex,
  Form,
  Input,
  message,
  Row,
  Space,
  Spin,
  Tag,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';
import './fix.less';
import HistoryList from './history';
import style from './style.less';
import upload_img from './upload.png';
const { TextArea } = Input;
const Info = () => {
  const { user, company } = useUserStore();

  const [editBillRecipient, setEditBillRecipient] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(true);
  const [isCreated, setIsCreated] = useState<boolean>(false);
  const [requesting, setRequesting] = useState<boolean>(false);

  const [License, setLicense] = useState<any[]>([]);
  const [Resource, setResource] = useState<any[]>([]);

  const handleDeleteLicense = (targetId: string) => {
    setLicense((prev: any[]) =>
      prev.filter((item: { GlobalId: string }) => item.GlobalId !== targetId),
    ); // ‌:ml-citation{ref="3,5" data="citationList"}
  };
  const handleDeleteResource = (targetId: string) => {
    setResource((prev: any[]) =>
      prev.filter((item: { GlobalId: string }) => item.GlobalId !== targetId),
    ); // ‌:ml-citation{ref="3,5" data="citationList"}
  };

  const [companyLicenseFileList, setCompanyLicenseFileList] = useState<
    UploadFile[]
  >([]);
  const [companyResourceFileList, setCompanyResourceFileList] = useState<
    UploadFile[]
  >([]);

  const fileToBase64 = (file: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const companyLicenseFileListProps: UploadProps = {
    maxCount: 1,
    onRemove: (file) => {
      const index = companyLicenseFileList.indexOf(file);
      const newFileList = companyLicenseFileList.slice();
      newFileList.splice(index, 1);
      setCompanyLicenseFileList(newFileList);
    },
    beforeUpload: async (file: any) => {
      setCompanyLicenseFileList([file]);
      try {
        const base64 = await fileToBase64(file);
        file.thumbUrl = base64; // 直接注入缩略图
        form.setFields([{ name: 'CompanyLicense', errors: [] }]); // 清除错误信息
      } catch {
        message.error('文件读取失败');
        return Upload.LIST_IGNORE;
      }
      return false;
    },
    fileList: companyLicenseFileList,
  };

  const companyResourceFileListProps: UploadProps = {
    onRemove: (file) => {
      const index = companyResourceFileList.indexOf(file);
      const newFileList = companyResourceFileList.slice();
      newFileList.splice(index, 1);
      setCompanyResourceFileList(newFileList);
    },
    beforeUpload: async (file: any) => {
      setCompanyResourceFileList([...companyResourceFileList, file]);
      try {
        const base64 = await fileToBase64(file);
        file.thumbUrl = base64; // 直接注入缩略图
      } catch {
        message.error('文件读取失败');
        return Upload.LIST_IGNORE;
      }
      return false;
    },
    fileList: companyResourceFileList,
  };

  const [form] = Form.useForm();

  const setFields = () => {
    // setDisabled(false);
    setDisabled(company?.Disabled as boolean);
    setIsCreated(company?.IsCreated as boolean); // if IsCreated === true    use update API else use create API
    form.setFieldValue('Name', company?.Name);
    form.setFieldValue('Address', company?.Address);
    form.setFieldValue('USCI', company?.USCI);
    form.setFieldValue('Contact', company?.Contact);
    form.setFieldValue('Phone', company?.Phone);
    form.setFieldValue('Email', company?.Email);
    form.setFieldValue('Comment', company?.Comment);

    setCompanyLicenseFileList([]);
    setCompanyResourceFileList([]);

    //处理二次提交中的 文件
    setLicense(JSON.parse(JSON.stringify(company?.License ?? [])));
    setResource(JSON.parse(JSON.stringify(company?.Resource ?? [])));

    setRequesting(false);
  };

  useEffect(() => {
    setFields();
  }, [company?.Status]);
  const openwindow = (base64Str: string) => {
    const cleanBase64 = base64Str.replace(/[\r\n]/g, '');
    const imgWindow: any = window.open('', '_blank');
    imgWindow.document.write(
      `<embed type="application/pdf" style="width:100%;height:100%" src="${cleanBase64}" />`,
    );
  };

  const onFinish = async (values: any) => {
    //{ Name, Address, Contact, Phone, Email, CompanyLicense, CompanyResource }
    if (companyLicenseFileList.length === 0 && License.length === 0) {
      form.setFields([{ name: 'CompanyLicense', errors: ['请上传营业执照'] }]);
      return;
    }
    setRequesting(true);
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      // formData.append(key, values[key] == null ? "" : values[key]); // 将所有值添加到 formData 中
      formData.append(key, values[key]); // 将所有值添加到 formData 中
    });
    formData.delete('CompanyLicense');
    formData.delete('CompanyResource');
    companyLicenseFileList.forEach((file) => {
      formData.append('CompanyLicense', file as any);
    });
    companyResourceFileList.forEach((file) => {
      formData.append('CompanyResource', file as any);
    });

    if (isCreated) {
      License.forEach(({ GlobalId }: any) => {
        formData.append('ExistedLicense', GlobalId);
      });
      Resource.forEach(({ GlobalId }: any) => {
        formData.append('ExistedResource', GlobalId);
      });
    }

    try {
      const res = await submitCompanyInfo(isCreated, formData);
      console.log(res);
      message.success('提交成功');
      setRequesting(false);
      getCompany();
    } catch (error) {
      // message.error('提交失败');
      setRequesting(false);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };
  const isPngImage = (fileName: string) => {
    return fileName.toLowerCase().endsWith('.png');
  };

  const showAggrement = useMemo(
    () =>
      company?.BlueCloudAgreementStartDate &&
      company?.BlueCloudAgreementEndDate &&
      company?.BlueCloudAgreementNumber,
    [
      company?.BlueCloudAgreementStartDate,
      company?.BlueCloudAgreementEndDate,
      company?.BlueCloudAgreementNumber,
    ],
  );

  const showUploadAlert = useMemo(
    () => company?.StatusEnum !== 10 && company?.StatusEnum !== 40,
    [company?.StatusEnum],
  );
  return (
    <div className="enterprise">
      <Spin spinning={requesting}>
        <Card
          title="企业信息"
          variant="borderless"
          styles={{
            body: {
              display: 'flex',
              flexDirection: 'column',
              gap: 20,
              padding: '0 20px',
            },
          }}
        >
          <Flex vertical gap={16}>
            <p className={style.titlewapper}>
              <span className={style.cardtitle}></span>登录信息
            </p>
            <Space size={8}>姓名：{user?.FullName}</Space>
            <Space size={8}>邮箱：{user?.Email}</Space>
            <Space size={8}>电话：{user?.Phone}</Space>
            <Space size={8}>角色：{user?.UserRoleNames}</Space>
            <Space size={8}>
              账号状态：
              <Tag className={style.tag} color={user?.Active ? 'green' : 'red'}>
                {user?.Active ? '已激活' : '未激活'}
              </Tag>
            </Space>

            {/* {!editBillRecipient && (
              <Space size={8}>账单接收人: {user?.Email} <Button type='text' icon={<EditOutlined />} onClick={()=>{
                setEditBillRecipient(true);
             }}></Button></Space>
            )}    
            {editBillRecipient && (
              <Space size={8}>账单接收人: 
              <Input  type="email"
              pattern="[^\s@]+@[^\s@]+\.[^\s@]+"
              placeholder="请输入邮箱地址"
              title="请输入有效的邮箱地址">
              </Input><Button type='primary' onClick={()=>{
                setEditBillRecipient(false);
             }}>保存</Button></Space>
            )} */}
          </Flex>

          <div>
            <p className={style.titlewapper}>
              <span className={style.cardtitle}></span>认证信息{' '}
              {company?.StatusEnum !== 0 && !disabled && (
                <Tag
                  className={style.tag}
                  color={COMPANY_STATUS.get(company?.StatusEnum as number)}
                >
                  {company?.Status}
                </Tag>
              )}
            </p>
            <>
              <Form
                form={form}
                name="basic"
                layout="vertical"
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
                scrollToFirstError={{
                  behavior: 'instant',
                  block: 'end',
                  focus: true,
                }}
                style={{
                  width: '100%',
                }}
              >
                {!disabled && (
                  <Row gutter={24}>
                    <Col span={8}>
                      <Form.Item
                        label="公司名称"
                        name="Name"
                        rules={[{ required: true, message: '请输入公司名称' }]}
                      >
                        <Input disabled={disabled} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="公司地址"
                        name="Address"
                        rules={[{ required: true, message: '请输入公司地址' }]}
                      >
                        <Input disabled={disabled} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="统一社会信用代码"
                        name="USCI"
                        rules={[
                          { required: true, message: '请输入统一社会信用代码' },
                        ]}
                      >
                        <Input disabled={disabled} />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        label="姓名"
                        name="Contact"
                        rules={[{ required: true, message: '请输入公司名称' }]}
                      >
                        <Input disabled={disabled} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="电话"
                        name="Phone"
                        rules={[{ required: true, message: '请输入电话' }]}
                      >
                        <Input disabled={disabled} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="邮箱"
                        name="Email"
                        rules={[{ required: true, message: '请输入email' }]}
                      >
                        <Input type="email" disabled={disabled} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="账单接收人"
                        name="BillingNoticeEmail"
                        tooltip="该项用于接收云市场账单。"
                        rules={[{ required: true, message: '请输入email' }]}
                      >
                        <Input type="email" disabled={disabled} />
                      </Form.Item>
                    </Col>
                  </Row>
                )}
                {disabled && (
                  <Row gutter={24} className={style.companyInfo}>
                    <Col span={12}>
                      <div className={style.companyInfoTitle}>注册公司</div>
                      <div>
                        公司名称：
                        <span style={{ marginLeft: 8 }}>{company?.Name}</span>
                      </div>
                      <div>
                        公司地址：
                        <span style={{ marginLeft: 8 }}>
                          {company?.Address}
                        </span>
                      </div>
                      <div>
                        社会统一信用代码：
                        <span style={{ marginLeft: 8 }}>{company?.USCI}</span>
                      </div>
                      {showAggrement && (
                        <div>
                          协议编号：
                          <span style={{ marginLeft: 8 }}>
                            {company?.BlueCloudAgreementNumber}
                          </span>
                        </div>
                      )}
                      {showAggrement && (
                        <div>
                          协议有效日期：
                          <span style={{ marginLeft: 8 }}>
                            <Space split="至">
                              {company?.BlueCloudAgreementStartDate}
                              {company?.BlueCloudAgreementEndDate}
                            </Space>
                          </span>
                        </div>
                      )}
                      {disabled && (
                        <div>
                          账单接收人:
                          <span style={{ marginLeft: 8 }}>
                            {company?.BillingNoticeEmail}
                          </span>
                        </div>
                      )}
                      <div>
                        认证状态：
                        <span style={{ marginLeft: 8 }}>
                          <Tag
                            className={style.tag}
                            color={COMPANY_STATUS.get(
                              company?.StatusEnum as number,
                            )}
                          >
                            {company?.Status}
                          </Tag>
                        </span>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className={style.companyInfoTitle}>联系人</div>
                      <div>
                        姓名：
                        <span style={{ marginLeft: 8 }}>
                          {company?.Contact}
                        </span>
                      </div>
                      <div>
                        邮箱：
                        <span style={{ marginLeft: 8 }}>{company?.Email}</span>
                      </div>
                      <div>
                        电话：
                        <span style={{ marginLeft: 8 }}>{company?.Phone}</span>
                      </div>
                      <div>
                        蓝云销售：
                        <span style={{ marginLeft: 8 }}>
                          {company?.AssistanceManager}
                        </span>
                      </div>
                      <div>
                        蓝云销售邮箱：
                        <span style={{ marginLeft: 8 }}>
                          {company?.AssistanceEmail}
                        </span>
                      </div>
                    </Col>
                  </Row>
                )}

                <Form.Item
                  label={
                    <div className={style.titlewapper}>
                      <span className={style.cardtitle}></span>营业执照
                    </div>
                  }
                  name={'CompanyLicense'}
                >
                  <div className={style.flex}>
                    {License.map((item: any) => {
                      return (
                        <div key={'License-' + item.GlobalId}>
                          <div className={style.thumbBox}>
                            <div
                              className={
                                item.IsImage
                                  ? isPngImage(item.FileName)
                                    ? style.imagePng
                                    : style.imageJpe
                                  : style.pdf
                              }
                            ></div>
                            {item.FileName}
                            <Button
                              onClick={() => {
                                window.open(item.SasBlobUri);
                              }}
                              ghost
                              type="primary"
                              shape="circle"
                              className={style.viewbtn}
                            >
                              <EyeOutlined />
                            </Button>
                            {!disabled && (
                              <Button
                                disabled={disabled}
                                onClick={() => {
                                  handleDeleteLicense(item.GlobalId);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.closebtn}
                              >
                                <DeleteOutlined />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                    <Upload
                      className={style.uploadWapper}
                      {...companyLicenseFileListProps}
                      accept=".png,.jpg,.jpeg,.pdf"
                      disabled={disabled}
                      listType="picture"
                      itemRender={(originNode, file) => {
                        return (
                          <div key={file.uid}>
                            <div className={style.thumbBox}>
                              {file.type === 'application/pdf' ? (
                                <>
                                  <div className={style.pdf}></div>
                                  <span>{file.name}</span>
                                </>
                              ) : (
                                <>
                                  <div
                                    className={
                                      isPngImage(file.name)
                                        ? style.imagePng
                                        : style.imageJpe
                                    }
                                  ></div>
                                </>
                              )}
                              <Button
                                onClick={() => {
                                  // window.open(file.thumbUrl);
                                  openwindow(file.thumbUrl as string);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.viewbtn}
                              >
                                <EyeOutlined />
                              </Button>
                              <Button
                                onClick={() => {
                                  console.log(file.uid);
                                  const index =
                                    companyLicenseFileList.indexOf(file);
                                  const newFileList =
                                    companyLicenseFileList.slice();
                                  newFileList.splice(index, 1);
                                  setCompanyLicenseFileList(newFileList);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.closebtn}
                              >
                                <DeleteOutlined />
                              </Button>
                            </div>
                            <div
                              style={{
                                textAlign: 'center',
                                marginTop: 4,
                                fontSize: 16,
                              }}
                            >
                              {file.name}
                            </div>
                          </div>
                        );
                      }}
                    >
                      {License.length === 0 &&
                        companyLicenseFileList.length === 0 &&
                        !disabled && (
                          <div className={style.upload}>
                            <img src={upload_img} />
                            <p>点击上传</p>
                          </div>
                        )}
                    </Upload>
                  </div>
                  {showUploadAlert && (
                    <div className={style.uploadAlert}>
                      支持的文件类型：*.png、*.jpg、*.jpeg、*.pdf
                    </div>
                  )}
                </Form.Item>
                <Form.Item
                  label={
                    <div className={style.titlewapper}>
                      <span className={style.cardtitle}></span>资质文件
                    </div>
                  }
                  name={'CompanyResource'}
                >
                  <div className={style.flex}>
                    {Resource.map((item: any) => {
                      return (
                        <div key={'Resource-' + item.GlobalId}>
                          <div className={style.thumbBox}>
                            <div
                              className={
                                item.IsImage
                                  ? isPngImage(item.FileName)
                                    ? style.imagePng
                                    : style.imageJpe
                                  : style.pdf
                              }
                            ></div>
                            {item.FileName}
                            <Button
                              onClick={() => {
                                window.open(item.SasBlobUri);
                              }}
                              ghost
                              type="primary"
                              shape="circle"
                              className={style.viewbtn}
                            >
                              <EyeOutlined />
                            </Button>
                            {!disabled && (
                              <Button
                                disabled={disabled}
                                onClick={() => {
                                  handleDeleteResource(item.GlobalId);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.closebtn}
                              >
                                <DeleteOutlined />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                    <Upload
                      accept=".png,.jpg,.jpeg,.pdf"
                      className={style.uploadWapper}
                      disabled={disabled}
                      listType="picture"
                      {...companyResourceFileListProps}
                      maxCount={1}
                      itemRender={(originNode, file) => {
                        return (
                          <div key={file.uid}>
                            <div className={style.thumbBox}>
                              {file.type === 'application/pdf' ? (
                                <>
                                  <div className={style.pdf}></div>
                                  <span>{file.name}</span>
                                </>
                              ) : (
                                <div
                                  className={
                                    isPngImage(file.name)
                                      ? style.imagePng
                                      : style.imageJpe
                                  }
                                ></div>
                              )}
                              <Button
                                onClick={() => {
                                  openwindow(file.thumbUrl as string);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.viewbtn}
                              >
                                <EyeOutlined />
                              </Button>
                              <Button
                                onClick={() => {
                                  console.log(file.uid);
                                  const index =
                                    companyResourceFileList.indexOf(file);
                                  const newFileList =
                                    companyResourceFileList.slice();
                                  newFileList.splice(index, 1);
                                  setCompanyResourceFileList(newFileList);
                                }}
                                ghost
                                type="primary"
                                shape="circle"
                                className={style.closebtn}
                              >
                                <DeleteOutlined />
                                {/* <img
                                src={del_img}
                                alt="icon"
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover', // 避免图片变形
                                }}
                              /> */}
                              </Button>
                            </div>
                            <div
                              style={{
                                textAlign: 'center',
                                marginTop: 4,
                                fontSize: 16,
                              }}
                            >
                              {file.name}
                            </div>
                          </div>
                        );
                      }}
                    >
                      {Resource.length + companyResourceFileList.length < 5 &&
                        !disabled && (
                          <div className={style.upload}>
                            <img src={upload_img} />
                            <p>点击上传</p>
                          </div>
                        )}
                    </Upload>
                  </div>
                  {showUploadAlert && (
                    <div className={style.uploadAlert}>
                      <div>支持的文件类型：*.png、*.jpg、*.jpeg、*.pdf</div>
                      <div>最多可上传5个文件</div>
                    </div>
                  )}
                </Form.Item>
                <Row>
                  <Col span={24}>
                    <Form.Item
                      name={'Comment'}
                      label={
                        <div className={style.titlewapper}>
                          <span className={style.cardtitle}></span>备注
                        </div>
                      }
                    >
                      <TextArea
                        disabled={disabled}
                        maxLength={100}
                        rows={6}
                        showCount={{
                          formatter: ({ count, maxLength }: any) => (
                            <span
                              style={{
                                color: count > maxLength ? 'red' : 'inherit',
                              }}
                            >
                              {count}/{maxLength}字
                            </span>
                          ),
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                {!disabled && (
                  <Form.Item
                    style={{ display: 'flex', justifyContent: 'center' }}
                  >
                    <Button
                      className={style.submit}
                      disabled={disabled}
                      type="primary"
                      htmlType="submit"
                    >
                      提交
                    </Button>
                  </Form.Item>
                )}
              </Form>
            </>
          </div>
          <HistoryList />
        </Card>
      </Spin>
    </div>
  );
};
export default Info;
