import PageWrapper from '@/components/PageWrapper';
import PriceStar from '@/components/pricestar';
import ProductList from '@/components/Product/List';
import { useCategoriesStore } from '@/store/categroies';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { Flex, Pagination, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import SolutionHeader from './components/SolutionHeader';
import useCategories from './hooks/useCategories';

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding-top: 29px;
    padding-bottom: 60px;
  `,
  list: css`
    padding: 24px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 1);
    box-shadow: inset 0px 2px 2px rgba(255, 255, 255, 0.4),
      inset 0px 2px 8px rgba(255, 255, 255, 0.4);
  `,
  pagination: css`
    align-self: flex-end;
  `,
}));
const Solution = () => {
  const { styles } = useStyles();

  const {
    levelOneMenu,
    levelTwoMenu,
    solutions,
    menuLoading,
    solutionsLoading,
    page,
    setPage,
  } = useCategories();

  const categoriesStore = useCategoriesStore();

  const setSelectedOne = (key: string) => {
    categoriesStore.solutionSelectedOneId = key;
  };

  const setSelectedTwo = (key: string) => {
    categoriesStore.solutionSelectedTwoId = key;
  };

  return (
    <div className={styles.container}>
      <PageWrapper style={{ background: 'transparent', padding: 0 }}>
        <Flex vertical gap={24}>
          <Skeleton loading={menuLoading}>
            <SolutionHeader
              levelOneMenu={levelOneMenu}
              levelTwoMenu={levelTwoMenu}
              selectedOne={categoriesStore.solutionSelectedOneId}
              selectedTwo={categoriesStore.solutionSelectedTwoId}
              setSelectedOne={setSelectedOne}
              setSelectedTwo={setSelectedTwo}
            />
          </Skeleton>
          <Flex vertical gap={24}>
            <Skeleton loading={solutionsLoading}>
              <div className={styles.list}>
                <ProductList
                  tag="online"
                  type="solution"
                  list={
                    solutions?.Data.map((item) => ({
                      id: item.Id,
                      globalId: item.GlobalId,
                      name: item.Name,
                      description: item.ShortDescription,
                      image: item.LogoUrl,
                      price: item.LimitPurchase ? (
                        <PriceStar />
                      ) : (
                        formatWithThousandSeparator(item.ShowedPrice)
                      ),
                      currency: item.Currency,
                      supplier: '',
                    })) || []
                  }
                />
              </div>
            </Skeleton>
            <Pagination
              className={styles.pagination}
              hideOnSinglePage={true}
              total={solutions?.Total || 0}
              current={page}
              pageSize={12}
              onChange={setPage}
            />
          </Flex>
        </Flex>
      </PageWrapper>
    </div>
  );
};
export default Solution;
