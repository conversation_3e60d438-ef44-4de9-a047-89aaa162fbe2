import { Col, Form, Input, Row } from 'antd';
interface IParameterExpandedRowRender {
  record: any;
  changeForm: (id: any, form: any) => void;
  changeFormRefs: (id: any, form: any) => void;
}

const ParameterExpandedRowRender: React.FC<IParameterExpandedRowRender> = (
  props,
) => {
  const { record, changeForm, changeFormRefs } = props;
  const [solutionForm] = Form.useForm();
  changeForm(record.Id, solutionForm);
  changeFormRefs(record.Id, solutionForm);

  return (
    <Row style={{ lineHeight: '32px' }}>
      <Col span={6}>购买参数</Col>
      <Col span={8}>
        <Form form={solutionForm} initialValues={{ Id: record.Id }}>
          <Form.Item
            label="邮箱"
            name="Email"
            style={{ marginBottom: '0px' }}
            rules={[
              { required: true, message: '邮箱不能为空' },
              {
                pattern:
                  /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                message: '邮箱格式不正确',
              },
            ]}
          >
            <Input placeholder="请填写开通订阅的邮箱作为账号"></Input>
          </Form.Item>
        </Form>
      </Col>
    </Row>
  );
};

export default ParameterExpandedRowRender;
