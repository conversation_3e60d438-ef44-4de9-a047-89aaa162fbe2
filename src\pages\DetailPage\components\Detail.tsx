import RichTextViewer from '@/components/RichTextViewer';
import { useUserDeriveState } from '@/store/user';
import { getFileNameFromPath } from '@/utils/fileUtils';
import { useCreation, useSafeState } from 'ahooks';
import { Anchor, Flex, Typography } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ProductDetail } from '../services';
import { isValidUrl } from '../utils';
import ExpandDiv from './ExpandDiv';

const useStyles = createStyles(({ css, prefixCls, token }) => ({
  common: css`
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  `,
  anchor: css`
    &.${prefixCls}-anchor-wrapper-horizontal {
      .${prefixCls}-anchor span.${prefixCls}-anchor-ink {
        background-color: #2d8cf0;
      }
    }
  `,
  detailHeader: css`
    position: sticky;
    top: 47px;
    background: #fff;
    border-radius: 8px;
    padding-right: 20px;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    .ant-anchor-link .ant-anchor-link-title {
      padding: 15px 30px;
    }
  `,
  detailContent: css`
    margin-bottom: 30px;
    color: rgba(56, 56, 56, 1);
  `,
  detailTitle: css`
    position: relative;
    &.${prefixCls}-typography {
      display: inline-block;
      font-size: 22px;
      font-weight: 500;
      color: #2d8cf0;
    }
  `,
  fileName: css`
    margin: 0 0 0 -20px;
    a {
      color: #2d8cf0;
      font-size: 14px;
    }
    span {
      color: #2d8cf0;
    }
  `,
  supportContent: css`
    color: #2d8cf0;
  `,
}));

const Detail: React.FC<{ detail: ProductDetail; extra?: React.ReactNode }> = ({
  detail,
  extra,
}) => {
  const { styles } = useStyles();

  const { isLogin, isRegistEntity } = useUserDeriveState();
  const detailHeaderRef = useRef<HTMLDivElement | null>(null);
  const [showExtra, setShowExtra] = useSafeState(false);
  useEffect(() => {
    const handleScroll = () => {
      if (detailHeaderRef?.current) {
        const rect = detailHeaderRef.current.getBoundingClientRect();
        const isSticky = rect.top === 47; // Assuming 47px is the sticky top value
        setShowExtra(isSticky);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [detailHeaderRef]);

  const [activeAncherKey, setActiveAncherKey] = useState('');

  const notLoginAndnotRegistEntity = useMemo(
    () => !isLogin || !isRegistEntity,
    [isLogin, isRegistEntity],
  );

  const anchorTitle = useCreation(
    () => [
      {
        key: 'detail',
        href: '#detail',
        title: '方案详情',
      },
      ...(detail?.Success && detail.Success.trim()
        ? [
            {
              key: 'success',
              href: '#success',
              title: '成功案例',
            },
          ]
        : []),
      ...(detail?.Resources && detail.Resources.trim()
        ? [
            {
              key: 'resource',
              href: '#resource',
              title: '产品声誉',
            },
          ]
        : []),
      {
        key: 'support',
        href: '#support',
        title: '技术支持',
      },
      {
        key: 'agreement',
        href: '#agreement',
        title: '用户协议',
      },
    ],
    [detail, detail?.Success, detail?.Resources],
  );

  const anchorTitleCalc = useCreation(() => {
    const updatedAnchorTitle = anchorTitle.slice(0, -1);
    return notLoginAndnotRegistEntity ? updatedAnchorTitle : anchorTitle;
  }, [notLoginAndnotRegistEntity, anchorTitle]);

  const getCurrentAnchor = () => activeAncherKey;

  return (
    <>
      <div className={styles.detailHeader} ref={detailHeaderRef}>
        <Flex justify="space-between" align="center">
          <Anchor
            className={styles.anchor}
            direction="horizontal"
            bounds={10}
            targetOffset={124}
            replace
            onChange={(key) => {
              setActiveAncherKey(key);
            }}
            getCurrentAnchor={getCurrentAnchor}
            items={anchorTitleCalc}
          />
          {showExtra ? extra : null}
        </Flex>
      </div>
      <Flex vertical gap={30} className={styles.detailContent}>
        <div id="detail" className={styles.common}>
          <div>
            <Typography.Text className={styles.detailTitle}>
              方案详情
            </Typography.Text>
          </div>
          <ExpandDiv>
            <RichTextViewer htmlContent={detail?.FullDescription || ''} />
          </ExpandDiv>
        </div>
        {detail?.Success && detail.Success.trim() && (
          <div id="success" className={styles.common}>
            <div>
              <Typography.Text className={styles.detailTitle}>
                成功案例
              </Typography.Text>
            </div>
            <ExpandDiv>
              <RichTextViewer htmlContent={detail?.Success || ''} />
            </ExpandDiv>
          </div>
        )}
        {detail?.Resources && detail.Resources.trim() && (
          <div id="resource" className={styles.common}>
            <div>
              <Typography.Text className={styles.detailTitle}>
                产品声誉
              </Typography.Text>
            </div>
            <ExpandDiv>
              <RichTextViewer htmlContent={detail?.Resources || ''} />
            </ExpandDiv>
          </div>
        )}
        <div id="support" className={styles.common}>
          <div>
            <Typography.Text
              className={styles.detailTitle}
              style={{ marginBottom: 12 }}
            >
              技术支持
            </Typography.Text>
          </div>
          <Flex vertical gap={10}>
            {detail?.SolutionSupports?.map((item) => {
              return (
                <Flex gap={10} key={item.Name}>
                  <span>{item.Name}:</span>
                  <span className={styles.supportContent}>{item.ValueRaw}</span>
                </Flex>
              );
            })}
          </Flex>
        </div>
        {!notLoginAndnotRegistEntity && (
          <div id="agreement" className={styles.common}>
            <div>
              <Typography.Text
                className={styles.detailTitle}
                style={{ marginBottom: 12 }}
              >
                用户协议
              </Typography.Text>
            </div>
            <Flex vertical gap={10}>
              <Typography.Text>
                购买此产品即表示您同意以下协议条款：
              </Typography.Text>
              {detail?.UserAgreement?.map((item) => {
                return (
                  <ul key={item.FileName} className={styles.fileName}>
                    <li>
                      {item.AgreementType === 30 &&
                        (isValidUrl(item.Uri) ? (
                          <a href={item.Uri} target="_blank" rel="noreferrer">
                            {getFileNameFromPath(item.FileName)}
                          </a>
                        ) : (
                          <span>{getFileNameFromPath(item.FileName)}</span>
                        ))}
                      {item.AgreementType === 40 && (
                        <a href={item.Uri} target="_blank" rel="noreferrer">
                          {getFileNameFromPath(item.Uri)}
                        </a>
                      )}
                    </li>
                  </ul>
                );
              })}
              <Typography.Text>
                请在使用产品前仔细阅读以上协议内容。使用本产品表示您已接受所有条款。如有问题，请联系我们的支持团队。
              </Typography.Text>
            </Flex>
          </div>
        )}
      </Flex>
    </>
  );
};

export default Detail;
