import Footer from '@/components/Footer';
import PrivacyHeader from '@/components/Header/PrivacyHeader';
import { Outlet, useLocation } from '@umijs/max';
import { FloatButton } from 'antd';
import { useEffect } from 'react';

const Index = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // 监听路由变化，滚动到顶部
    window.scrollTo(0, 0);
  }, [pathname]);
  return (
    <>
      <PrivacyHeader />
      <Outlet />
      <Footer />
      <FloatButton.BackTop />
    </>
  );
};

export default Index;
