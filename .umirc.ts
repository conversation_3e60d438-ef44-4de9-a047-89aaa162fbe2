import { defineConfig } from '@umijs/max';
const path = require('path');
export default defineConfig({
  title: '世纪互联云市场',
  request: {
    dataField: 'data',
  },
  antd: {
    configProvider: {},
  },
  alias: {
    '@/assets': path.resolve(__dirname, 'src/assets'),
  },
  model: {},
  svgo: {},
  esbuildMinifyIIFE: true,
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      key: 'home',
      name: '首页',
      path: '/home',
      component: './Home',
    },
    {
      key: 'solution',
      name: '解决方案',
      path: '/solution',
      component: './Solution',
    },
    {
      key: 'yellow-pages-products',
      name: '展示区',
      path: '/yellow-pages-products',
      component: './YellowPagesProducts',
    },
    {
      name: '展示区产品详情',
      key: 'yellow-pages-products-detail',
      path: '/yellow-pages-products/detail/:globalId',
      component: './YellowPageDetail',
    },
    {
      key: 'vnetzone',
      name: '世纪互联专区',
      path: '/vnetzone',
      component: './VnetZone',
      routes: [
        {
          path: '/vnetzone',
          redirect: '/vnetzone/consultation',
        },
        {
          name: '在线购买',
          key: 'online',
          path: '/vnetzone/online',
          component: './VnetZone/Online',
        },
        {
          name: '咨询专属',
          key: 'consultation',
          path: '/vnetzone/consultation',
          component: './VnetZone/Consult',
        },
      ],
    },
    {
      name: '供应商落地页',
      key: 'supplier',
      path: '/supplier/:id',
      component: './SupplierLandingPage',
      routes: [
        {
          path: '/supplier/:id',
          redirect: '/supplier/:id/online',
        },
        {
          name: '在线购买',
          key: 'online',
          path: '/supplier/:id/online',
          component: './SupplierLandingPage/Online',
        },
        {
          name: '咨询专属',
          key: 'consultation',
          path: '/supplier/:id/consultation',
          component: './SupplierLandingPage/Consult',
        },
      ],
    },
    {
      key: 'search',
      path: '/search',
      component: './SearchPage',
      routes: [
        {
          path: '/search',
          redirect: '/search/online',
        },
        {
          name: '在线购买',
          key: 'online',
          path: '/search/online',
          component: './SearchPage/Online',
        },
        {
          name: '咨询专属',
          key: 'consultation',
          path: '/search/consultation',
          component: './SearchPage/Consult',
        },
      ],
    },
    {
      key: 'brand',
      name: '品牌专区',
      path: '/brand/:id',
      component: './Brand',
    },
    {
      key: 'detail',
      name: '商品详情',
      path: '/:type/detail/:id',
      component: './DetailPage',
    },
    {
      key: 'login',
      name: '登录',
      path: '/login',
      component: './Login',
    },
    {
      key: 'register',
      name: '注册',
      path: '/register',
      component: './Reg',
    },
    {
      path: '/reg',
      component: '@/layouts/PrivacyLayout',
      layout: false,
      routes: [
        {
          path: '/reg',
          redirect: '/reg/networkProtocol',
        },
        {
          key: 'networkProtocol',
          name: '网络协议',
          path: '/reg/networkProtocol',
          component: './Reg/NetworkProtocol',
        },
        {
          key: 'privacyStatement',
          name: '隐私声明',
          path: '/reg/privacyStatement',
          component: './Reg/PrivacyStatement',
        },
      ],
    },
    {
      key: 'invite',
      name: '邀请用户',
      path: '/invite',
      component: './Invite',
    },
    {
      key: 'findPasswd',
      name: '找回密码',
      path: '/resetpassword',
      component: './FindPasswd',
    },
    {
      key: 'resetpwd',
      name: '重置密码',
      path: '/resetpwd',
      component: './ResetPasswd',
    },
    {
      key: 'help',
      name: '帮助',
      path: '/help',
      component: './Help',
      layout: true,
    },
    {
      key: 'cart',
      name: '购物车',
      path: '/cart',
      component: './Cart',
      wrappers: ['@/components/Authorize/AuthenticationPassed'],
    },
    {
      path: '/enterprise',
      key: 'enterprise',
      component: '@/layouts/EnterpriseLayout',
      routes: [
        {
          path: '/enterprise',
          redirect: '/enterprise/info',
        },
        {
          key: 'info',
          path: '/enterprise/info',
          component: './Enterprise/Info',
          defaultLayout: false,
        },
        {
          key: 'orders',
          path: '/enterprise/orders',
          component: './Enterprise/Orders',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'customers',
          path: '/enterprise/customers',
          component: './Enterprise/Customers',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'users',
          path: '/enterprise/users',
          component: './Enterprise/Users',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'invoiceHistory',
          path: '/enterprise/invoice/history',
          component: './Enterprise/Invoice/history',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'invoiceTitle',
          path: '/enterprise/invoice/title',
          component: './Enterprise/Invoice/title',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'invoiceAddress',
          path: '/enterprise/invoice/address',
          component: './Enterprise/Invoice/address',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'invoiceDetail',
          path: '/enterprise/invoice/detail',
          component: './Enterprise/Invoice/detail',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
        {
          key: 'bill',
          path: '/enterprise/bill',
          component: './Enterprise/Bill',
          wrappers: ['@/components/Authorize/AuthenticationPassed'],
        },
      ],
    },
    {
      key: 'purchase',
      name: '结算',
      path: '/purchase',
      component: './Purchase',
    },
    {
      key: 'payment',
      name: '支付',
      path: '/payment',
      component: './Payment',
    },
    {
      key: 'OrderDetail',
      name: '订单详情',
      path: '/order/detail',
      component: './OrderDetail',
    },

    { path: '/*', component: '@/pages/404' },
  ],
  proxy: {
    '/api': {
      target: 'http://*************:25114',
      // target: 'https://mplace-test-api.21vbluecloud.com', // uat
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
  },
  copy: [
    {
      from: 'web.config',
      to: 'dist/web.config',
    },
  ],
  npmClient: 'pnpm',
});
