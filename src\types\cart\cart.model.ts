export interface CartModel {
  CartOrderTotal: number;
  CartSubOrderTotal: number;
  Company: string;
  CustomProperties: any;
  DiscountOrder: number;
  DiscountTypeId: number;
  Email: string;
  Form: string;
  IsLeads: boolean;
  Items: CartItemModel[];
  Name: string;
  PaymentMethod: string;
  PhoneNumber: string;
}
export interface CartItemBrandZoneModel {
  BrandName: string;
  Id: number;
}
export interface CartItemModel {
  DeliveryMethod: number;
  BrandZone: CartItemBrandZoneModel;
  SolutionId: number;
  SolutionGlobalId: string;
  EditionId: number;
  Editions: EditionModel[];
  SolutionName: string;
  UnitPrice: number;
  SubTotal: number;
  Quantity: number;
  AttributeInfo: string;
  Discount: number;
  DiscountName: string;
  Users: number;
  Name: string;
  PictureUrl: string;
  Sku: string;
  PeriodValue: number;
  PeriodUnit: string;
  IsPurchaseParameter: boolean;
  SolutionPurchaseParameter: any;
  IsActive: boolean;
  Id: number;
}

export interface AddCartItemModel {
  SolutionGlobalId: string;
  SolutionId: number;
  SolutionEiditionId: number;
  Quantity: number;
}

export interface EditionModel {
  Id: number;
  Name: string;
  PurchaseLimit: number;
}
