import { useCategoriesStore } from '@/store/categroies';
import { usePrevious } from '@react-pdf-viewer/core';
import { useSearchParams } from '@umijs/max';
import { useCreation, useRequest, useSafeState } from 'ahooks';
import { sortBy } from 'lodash-es';
import { useEffect } from 'react';
import { DeliveryMethod, getCategories, getSolutionsById } from '../services';

const useCategories = () => {
  const categoriesStore = useCategoriesStore();

  const PAGE_SIZE = 20;
  const [page, setPage] = useSafeState(0);

  const [searchParams] = useSearchParams();

  const delivery = searchParams.get('t') as keyof typeof DeliveryMethod;

  const prevDelivery = usePrevious(delivery);

  useEffect(() => {
    if (delivery !== prevDelivery) {
      categoriesStore.solutionSelectedOneId = 'all';
      categoriesStore.solutionSelectedTwoId = 'all';
    }
  }, [delivery]);

  // 使用useRequest获取分类数据
  const { data: categories, loading: menuLoading } = useRequest(
    () => {
      if (delivery) return getCategories(DeliveryMethod[delivery]);

      return Promise.resolve(categoriesStore.rawData);
    },
    {
      refreshDeps: [delivery, categoriesStore.rawData],
    },
  );

  const sortCategories = sortBy(categories || [], 'Id'); // 按照Id排序

  const levelOneMenu = useCreation(() => {
    return sortCategories?.filter((item) => item.ParentId === 0) || [];
  }, [sortCategories]); // 一级菜单

  const levelTwoMenu = useCreation(
    () =>
      sortCategories?.filter(
        (item) =>
          item.ParentId === Number(categoriesStore.solutionSelectedOneId),
      ) || [],
    [sortCategories, categoriesStore.solutionSelectedOneId],
  ); // 二级菜单

  const selectedCategoryId = useCreation(() => {
    return categoriesStore.solutionSelectedTwoId === 'all'
      ? categoriesStore.solutionSelectedOneId
      : categoriesStore.solutionSelectedTwoId;
  }, [
    categoriesStore.solutionSelectedOneId,
    categoriesStore.solutionSelectedTwoId,
  ]);

  const currentCategoryId = useCreation(() => {
    return selectedCategoryId === 'all'
      ? undefined
      : Number(selectedCategoryId);
  }, [selectedCategoryId]);

  const { data: solutions, loading: solutionsLoading } = useRequest(
    () =>
      getSolutionsById(
        currentCategoryId,
        DeliveryMethod[searchParams.get('t') as keyof typeof DeliveryMethod],
        page,
        PAGE_SIZE,
      ),
    {
      refreshDeps: [selectedCategoryId, page, searchParams.get('t')],
    },
  );

  return {
    levelOneMenu,
    levelTwoMenu,
    solutions,
    solutionsLoading,
    menuLoading,
    page,
    setPage,
  };
};

export default useCategories;
