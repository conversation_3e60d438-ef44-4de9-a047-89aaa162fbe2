import useRouteKey from '@/hooks/useRouteKey';
import { OnlineListItem, SearchParams } from '@/pages/SearchPage/services';
import { createStyles } from 'antd-style';
import { FC, memo, ReactNode } from 'react';
import List from './List';

interface SearchPageProps {
  searchParams: SearchParams;
  updateSearchParams: (values: Partial<SearchParams>) => void;
  searchPanel: ReactNode;
  dataSource: OnlineListItem[];
  total: number;
  loading?: boolean;
  to: 'solution' | 'yellow-pages-products';
}

const useStyles = createStyles(({ prefixCls, css }) => ({
  list: css`
    width: 1200px;
    margin: 30px auto;
  `,
}));

const SearchPageContent: FC<SearchPageProps> = ({
  searchParams,
  updateSearchParams,
  searchPanel,
  dataSource,
  to,
  total = 0,
  loading = false,
}) => {
  const { styles } = useStyles();
  const routeKey = useRouteKey();

  return (
    <div>
      {searchPanel}
      <div className={styles.list}>
        <List
          to={to}
          tag={routeKey}
          data={dataSource || []}
          loading={loading}
          pagination={{
            page: searchParams.Page + 1,
            pageSize: searchParams.PageSize,
            setPage: (page: number) => {
              updateSearchParams({ Page: page });
            },
            total: total,
          }}
        />
      </div>
    </div>
  );
};

export default memo(SearchPageContent);
