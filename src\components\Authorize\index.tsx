import { useUserDeriveState } from '@/store/user';
import { FC, PropsWithChildren, ReactNode } from 'react';

interface AuthorizeProps {
  error?: ReactNode;
}
const Authorize: FC<AuthorizeProps & PropsWithChildren> = ({
  error = null,
  children,
}) => {
  const { isLogin } = useUserDeriveState();
  if (!isLogin) {
    return <>{error}</>;
  }
  return <>{children}</>;
};

export default Authorize;
