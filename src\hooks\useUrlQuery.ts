import { useLocation } from '@umijs/max';
import { useEffect, useState } from 'react';

const useQuery = () => {
  const [query, setQuery] = useState<Record<string, string>>({});
  const location = useLocation();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const params: Record<string, string> = {};

    for (let [key, value] of urlParams.entries()) {
      params[key] = value;
    }

    setQuery(params);
  }, []);

  return query;
};

export default useQuery;
