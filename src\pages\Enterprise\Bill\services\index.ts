import { request } from '@umijs/max';
export interface BillModel {
  id: number;
  billNumber: string;
  customerCompany: string;
  billDateUtc: string;
  billAmount: string;
  deadLinePayDateUtc: string;
  isOverDue: number;
  dueDays: number;
  billStatus: string;
  customOrderNumbers: string[];
  billStatusId: string;
}
export async function getBillssList(
  params: any,
): Promise<{ Data: BillModel[]; Total: number }> {
  return request(`/api/Orders/bills`, {
    method: 'GET',
    params: params,
  });
}

export async function getBillsSummary(): Promise<any> {
  return request('/api/Orders/billsSummary', {
    method: 'GET',
  });
}
