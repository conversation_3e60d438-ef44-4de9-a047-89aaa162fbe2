import { transformToTree } from '@/components/CategoriesTree';
import SearchPageContent from '@/components/SearchPageContent';
import { useSearchParams } from '@umijs/max';
import { useCreation } from 'ahooks';
import { memo } from 'react';
import { ListResponse, searchConsult } from '../SearchPage/services';
import { YellowPageSolutionData } from '../YellowPagesProducts/services';
import SearchCard from './components/SearchCard';
import useSearch from './hooks/useSearch';
import useVnetCategroies from './hooks/useVnetCategroies';
import { vnetCategroieDisplayed } from './services';

const Consult = () => {
  const [searchParams] = useSearchParams();

  const { categories } = useVnetCategroies(
    vnetCategroieDisplayed,
    searchParams.get('n') as string,
  );

  const categoriesTreeData = useCreation(() => {
    return transformToTree(categories);
  }, [categories]);

  const { data, loading, searchValues, updateState } = useSearch<
    ListResponse<YellowPageSolutionData>
  >(searchConsult, searchParams.get('n') as string);
  return (
    <SearchPageContent
      to="yellow-pages-products"
      searchPanel={
        <SearchCard
          categoriesTreeData={categoriesTreeData}
          searchValues={searchValues}
          updateSearchValues={updateState}
        />
      }
      searchParams={searchValues}
      updateSearchParams={updateState}
      dataSource={
        data?.Data?.map((item) => ({
          Id: item.Id,
          Name: item.SolutionName,
          ShortDescription: item.Description,
          LogoUrl: item.Logo,
          GlobalId: item.GlobalId,
          ShowedPrice: 0,
          Currency: '',
          LimitPurchase: false,
          Supplier: item.Supplier,
        })) || []
      }
      total={data?.Total || 0}
      loading={loading}
    />
  );
};

export default memo(Consult);
