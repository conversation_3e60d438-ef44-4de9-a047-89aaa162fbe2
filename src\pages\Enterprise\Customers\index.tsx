import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import useUserStore from '@/store/user';
import { IndustryList } from '@/types';
import { useCreation } from 'ahooks';
import {
  Button,
  Col,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Pagination,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  TableProps,
  Typography,
} from 'antd';
import { createStyles } from 'antd-style';
import useCustomer from './hooks/useCustomer';
import { Customer } from './services';
const pageSize = 10;
const useStyles = createStyles(({ css }) => ({
  input: css`
    border-radius: 28px;
    width: 249px;
    transition: all 0.3s ease-in-out;
  `,
}));
const Customers = () => {
  const { styles } = useStyles();

  const userStore = useUserStore();

  const [form] = Form.useForm();

  const watchId = Form.useWatch('id', form);

  const isEdit = useCreation(() => watchId !== 0, [watchId]);

  const {
    list,
    listLoading,
    addItem,
    addLoading,
    updateItem,
    updateLoading,
    deleteItem,
    deleteLoading,
    state,
    setFalse,
    setTrue,
    PageIndex,
    setPageIndex,
    searchText,
    setSearchText,
    selectedRowKeys,
    setSelectedRowKeys,
  } = useCustomer();

  const deleteBtnStatus = useCreation(
    () => selectedRowKeys.length > 0,
    [selectedRowKeys],
  );

  const onFinish = (values: any) => {
    if (isEdit) {
      updateItem({ ...values, competitor: 'no' });
    } else {
      addItem({ ...values, competitor: 'no' });
    }

    form.resetFields();
  };

  const handleDelete = () => {
    deleteItem();
  };

  const handleEdit = (data: Customer) => {
    // 编辑
    setTrue();
    form.setFieldsValue({
      id: data.Id,
      name: data.Name,
      company: data.Company,
      email: data.Email,
      phoneNumber: data.PhoneNumber,
      industry: data.Industry,
      address: data.Address,
      region: data.Region,
    });
  };

  const handleCancel = () => {
    setFalse();
    form.resetFields();
  };

  const columns: TableProps<Customer>['columns'] = [
    {
      title: '公司名称',
      dataIndex: 'Company',
    },
    {
      title: '姓名',
      dataIndex: 'Name',
    },
    {
      title: '邮箱',
      dataIndex: 'Email',
    },
    {
      title: '电话',
      dataIndex: 'PhoneNumber',
    },
    {
      title: '地址',
      dataIndex: 'Address',
    },
    {
      title: '操作',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex justify="space-between" style={{ width: '100%', marginBottom: 10 }}>
        <Typography.Title level={5}>客户管理</Typography.Title>
        <Space size={20}>
          <Input
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder="请输入姓名/公司/邮箱/电话"
            prefix={<SearchSVG />}
            className={styles.input}
          />
          <Button type="primary" onClick={setTrue}>
            添加客户
          </Button>
          <Popconfirm title="确定删除吗？" onConfirm={handleDelete}>
            <Button
              type="primary"
              loading={deleteLoading}
              ghost
              disabled={!deleteBtnStatus}
            >
              删除客户
            </Button>
          </Popconfirm>
        </Space>
      </Flex>

      <Table<Customer>
        rowKey="Id"
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
          },
        }}
        size="small"
        columns={columns}
        loading={listLoading}
        dataSource={list?.Data}
        pagination={false}
      />
      {(list?.Total ?? 0) > pageSize && (
        <Pagination
          style={{ marginTop: 16 }}
          align="end"
          current={PageIndex}
          pageSize={pageSize}
          onChange={(page, pageSize) => {
            setPageIndex(page);
          }}
          total={list?.Total ?? 0}
        />
      )}
      <Modal
        title={isEdit ? '编辑客户' : '添加客户'}
        open={state}
        onCancel={handleCancel}
        okButtonProps={{
          autoFocus: true,
          htmlType: 'submit',
          loading: addLoading || updateLoading,
        }}
        modalRender={(dom) => (
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
            initialValues={{ industry: 0, id: 0, region: 1 }}
          >
            {dom}
          </Form>
        )}
      >
        <Row gutter={12}>
          <Col span={12}>
            <Form.Item label="Id" name="id" hidden>
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="地区" name="region" hidden>
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="姓名"
              name="name"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="公司"
              name="company"
              rules={[{ required: true, message: '请输入公司' }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入正确的邮箱' },
              ]}
            >
              <Input type="email" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="电话"
              name="phoneNumber"
              rules={[
                { required: true, message: '请输入电话' },
                {
                  pattern: /(?:^1[3456789]|^9[28])\d{9}$/,
                  message: '请输入正确的手机号',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="行业"
              name="industry"
              rules={[{ required: true, message: '请选择行业' }]}
            >
              <Select
                options={IndustryList.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="地址"
              name="address"
              rules={[{ required: true, message: '请输入地址' }]}
            >
              <Input.TextArea rows={2} />
            </Form.Item>
          </Col>
        </Row>
        {!isEdit && userStore.company?.IsCredit && (
          <div
            style={{
              color: '#409eff',
              marginTop: 10,
              borderLeft: '4px solid #409eff',
              backgroundColor: '#ecf5ff',
              maxWidth: 'fit-content',
              padding: 10,
              fontSize: 14,
            }}
          >
            重要提示：为确保账单合并流程顺利进行，请您务必确认并提交最终客户名称信息。
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Customers;
