import { sortBy } from 'lodash-es';
import useBanner from '../hooks/useBanner';

import { Carousel, Skeleton } from 'antd';
import { createStyles, cx } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  prev: css`
    &.slick-prev {
      width: 32px;
      height: 32px;
      &:after {
        width: 24px;
        height: 24px;
      }
    }
  `,
  next: css`
    &.slick-next {
      width: 32px;
      height: 32px;
      &:after {
        width: 24px;
        height: 24px;
      }
    }
  `,
}));

function SamplePrevArrow(props: any) {
  const { className, style, onClick } = props;
  const { styles } = useStyles();

  return (
    <div
      className={cx(className, styles.prev)}
      style={style}
      onClick={onClick}
    />
  );
}

function SampleNextArrow(props: any) {
  const { className, style, onClick } = props;
  const { styles } = useStyles();

  return (
    <div
      className={cx(className, styles.next)}
      style={style}
      onClick={onClick}
    />
  );
}

const Banner = () => {
  const { bannerList = [], loading } = useBanner();

  return (
    <Skeleton loading={loading}>
      <Carousel
        autoplay
        arrows
        infinite
        speed={300}
        // autoplaySpeed={2000}
        prevArrow={<SamplePrevArrow />}
        nextArrow={<SampleNextArrow />}
      >
        {sortBy(bannerList, 'DisplayOrder').map((item, index) => {
          return (
            <div key={index}>
              <a href={item.Url} target="_blank" rel="noreferrer">
                <img
                  src={item.ImageUrl}
                  alt="banner"
                  loading="lazy"
                  style={{
                    objectFit: 'cover',
                    width: '100%',
                    height: '100%',
                  }}
                />
              </a>
            </div>
          );
        })}
      </Carousel>
    </Skeleton>
  );
};

export default Banner;
