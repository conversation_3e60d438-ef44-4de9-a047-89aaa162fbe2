/* eslint-disable no-param-reassign */

export function transform(code: string): string {
  switch (code) {
    case 'USD':
      return '$';
    case 'CNY':
      return '￥';
    case 'TWD':
      return 'NT$';
    case 'HKD':
      return 'HK$';
    case 'MOP':
      return 'MOP$';
    default:
      return '￥';
  }
}

export function digitUppercase(n: number): string {
  const fraction = ['角', '分'];
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const unit = [
    ['元', '万', '亿'],
    ['', '拾', '佰', '仟'],
  ];
  const head = n < 0 ? '欠' : '';
  n = Math.abs(n);
  let s = '';
  for (let i = 0; i < fraction.length; i++) {
    s += (
      digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]
    ).replace(/零./, '');
  }
  s = s || '整';
  n = Math.floor(n);
  for (let i = 0; i < unit[0].length && n > 0; i++) {
    let p = '';
    for (let j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p;
      n = Math.floor(n / 10);
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
  }
  return (
    head +
    s
      .replace(/(零.)*零元/, '元')
      .replace(/(零.)+/g, '零')
      .replace(/^整$/, '零元整')
  );
}

/**
 * 将数字格式化为千分位分隔符字符串
 * @param num 数字或数字字符串
 * @returns 带千分位分隔符的字符串
 */
export function formatWithThousandSeparator(num?: number | string): string {
  if (num === undefined || num === null || num === '') return '';
  const [integer, decimal] = num.toString().split('.');
  const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return decimal ? `${formattedInteger}.${decimal}` : formattedInteger;
}
