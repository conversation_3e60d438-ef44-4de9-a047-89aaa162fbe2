import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import {
  deleteInvoiceTitle,
  getInvoiceTitle,
  postInvoiceTitle,
  putInvoiceTitle,
} from '@/services/order/InvoiceController';
import useUserStore from '@/store/user';
import { useNavigate } from '@umijs/max';
import {
  Button,
  Flex,
  Form,
  Input,
  Modal,
  notification,
  Popconfirm,
  Space,
  Table,
  TableProps,
  Typography,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  search: css`
    border-radius: 16px;
  `,
}));
const InvoiceTitle = () => {
  interface DataType {
    FullName: string;
    Phone: string;
    UserRoleNames: string;
    CreatedOn: string;
    LastLoginDate: string;
    Active: boolean;
    Id: number;
    RoleId: number;
  }
  const { company } = useUserStore();
  console.log('company', company);
  const navigate = useNavigate();
  const [api, contextHolder] = notification.useNotification();
  const { styles } = useStyles();
  const [isAdd, setisAdd] = useState<boolean>(false);
  const [addConfirmLoading, setAddConfirmLoading] = useState<boolean>(false);
  const [updateConfirmLoading, setUpdateConfirmLoading] =
    useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [data, setData] = useState<DataType[]>([]);
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [searchForm] = Form.useForm();
  const columns: TableProps<DataType>['columns'] = [
    {
      title: '公司名称',
      dataIndex: 'CompanyName',
      key: 'CompanyName',
    },
    {
      title: '纳税人识别号',
      dataIndex: 'TaxNum',
      key: 'TaxNum',
    },
    {
      title: '开户行',
      dataIndex: 'Bank',
      key: 'Bank',
    },
    {
      title: '开户行账户',
      key: 'BankAccountnum',
      dataIndex: 'BankAccountnum',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              setIsEdit(true);
              editForm.setFieldsValue(record);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除吗？"
            onConfirm={() => {
              deleteClick(record.Id);
            }}
          >
            <Button type="link" size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  const deleteClick = (id: any) => {
    deleteInvoiceTitle([id]).then(
      (res) => {
        api['success']({
          message: '删除发票抬头成功',
          duration: 2,
        });
        getList();
      },
      () => {
        api['error']({
          message: '删除发票抬头失败',
          duration: 2,
        });
      },
    );
  };
  const getList = () => {
    let values = searchForm.getFieldsValue();
    setTableLoading(true);
    getInvoiceTitle(values).then((res) => {
      setData(res);
      setTableLoading(false);
    });
  };

  const onAddFinished = (values: any) => {
    setAddConfirmLoading(true);
    let par = { ...values };
    par.EntityId = 0;
    postInvoiceTitle(par).then(
      (res) => {
        api['success']({
          message: '添加抬头成功',
          duration: 2,
        });
        setAddConfirmLoading(false);
        setisAdd(false);
        addForm.resetFields();
        getList();
      },
      () => {
        setAddConfirmLoading(false);
      },
    );
  };

  const onEditFinished = (values: any) => {
    setUpdateConfirmLoading(true);
    putInvoiceTitle(values.Id, values).then(
      (res) => {
        api['success']({
          message: '编辑抬头成功',
          duration: 2,
        });
        setUpdateConfirmLoading(false);
        setIsEdit(false);
        getList();
      },
      () => {
        api['error']({
          message: '编辑抬头失败',
          duration: 2,
        });
      },
    );
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <div>
      {contextHolder}
      <Typography.Title level={5}>抬头管理</Typography.Title>
      <Flex
        justify="space-between"
        style={{ width: '100%', marginBottom: 20, paddingRight: 8 }}
      >
        <Form
          layout={'inline'}
          labelAlign="left"
          form={searchForm}
          style={{
            maxWidth: 'none',
            paddingTop: 16,
            paddingBottom: 16,
          }}
        >
          <Form.Item label="开户行" name="Bank">
            <Input placeholder="搜索开户行" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <Form.Item label="开户行账户" name="BankAccountnum">
            <Input placeholder="搜索开户行账户" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <div style={{ textAlign: 'left' }}>
            <Button
              type="primary"
              style={{ width: 80 }}
              onClick={() => {
                getList();
              }}
            >
              搜索
            </Button>
          </div>
        </Form>
        <Button
          type="primary"
          style={{ width: 80 }}
          onClick={(e) => {
            setisAdd(true);
          }}
        >
          新建
        </Button>
      </Flex>
      <Table<DataType>
        loading={tableLoading}
        pagination={false}
        rowKey={(record) => record.Id}
        columns={columns}
        dataSource={data}
      />

      <Modal
        title="添加抬头"
        open={isAdd}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="确认"
        onCancel={() => {
          setisAdd(false);
        }}
        confirmLoading={addConfirmLoading}
        modalRender={(dom) => (
          <Form
            form={addForm}
            name="basic"
            layout="vertical"
            initialValues={{
              CompanyName: company?.Name,
              TaxNum: company?.USCI,
            }}
            onFinish={onAddFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
      >
        <Form.Item
          label="公司名称"
          name="CompanyName"
          rules={[{ required: true, message: '请输入公司名称' }]}
        >
          <Input disabled={true} />
        </Form.Item>

        <Form.Item
          label="纳税人识别号"
          name="TaxNum"
          rules={[{ required: true, message: '纳税人识别号' }]}
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="联系电话"
          name="PhoneNumber"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^([-()+ 0-9]+)$/, message: '请输入正确的联系电话' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="地址"
          name="Address"
          rules={[
            { required: true, message: '请输入地址' },
            { pattern: /^\S+$/, message: '地址不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="开户行"
          name="Bank"
          rules={[
            { required: true, message: '请输入开户行' },
            { pattern: /^\S+$/, message: '开户行不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="开户行账号"
          name="BankAccountnum"
          rules={[
            { required: true, message: '请输入开户行账号' },
            { pattern: /^\d+$/, message: '开户行账号必须是数字' },
          ]}
        >
          <Input />
        </Form.Item>
      </Modal>
      <Modal
        title="编辑抬头"
        open={isEdit}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="保存"
        confirmLoading={updateConfirmLoading}
        onCancel={() => {
          setIsEdit(false);
        }}
        modalRender={(dom) => (
          <Form
            form={editForm}
            layout="vertical"
            onFinish={onEditFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
        destroyOnHidden
      >
        <Form.Item style={{ display: 'none' }} name="Id">
          <Input />
        </Form.Item>
        <Form.Item
          label="公司名称"
          name="CompanyName"
          rules={[{ required: true, message: '请输入公司名称' }]}
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="纳税人识别号"
          name="TaxNum"
          rules={[{ required: true, message: '纳税人识别号' }]}
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="联系电话"
          name="PhoneNumber"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^([-()+ 0-9]+)$/, message: '请输入正确的联系电话' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="地址"
          name="Address"
          rules={[
            { required: true, message: '请输入地址' },
            { pattern: /^\S+$/, message: '地址不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="开户行"
          name="Bank"
          rules={[
            { required: true, message: '请输入开户行' },
            { pattern: /^\S+$/, message: '开户行不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="开户行账号"
          name="BankAccountnum"
          rules={[
            { required: true, message: '请输入开户行账号' },
            { pattern: /^\d+$/, message: '开户行账号必须是数字' },
          ]}
        >
          <Input />
        </Form.Item>
      </Modal>
    </div>
  );
};

export default InvoiceTitle;
