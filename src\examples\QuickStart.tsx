import { useFadeInAnimation, useCardAnimation } from '@/hooks/useFadeInAnimation';
import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';
import { Card, Col, Row, Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

/**
 * 快速开始示例 - 展示最常用的几种使用方式
 */

// 示例1: 最简单的使用方式
export const SimpleExample: React.FC = () => {
  const containerRef = useFadeInAnimation('.simple-item');

  return (
    <div ref={containerRef}>
      <div className="simple-item">
        <Title level={3}>简单示例</Title>
        <Paragraph>这个元素会从下方淡入</Paragraph>
      </div>
      <div className="simple-item">
        <Paragraph>这个元素会稍后淡入</Paragraph>
      </div>
    </div>
  );
};

// 示例2: 卡片列表动画
export const CardListExample: React.FC = () => {
  const containerRef = useCardAnimation();

  const cards = [
    { title: '卡片 1', content: '这是第一个卡片的内容' },
    { title: '卡片 2', content: '这是第二个卡片的内容' },
    { title: '卡片 3', content: '这是第三个卡片的内容' },
  ];

  return (
    <Row gutter={[16, 16]} ref={containerRef}>
      {cards.map((card, index) => (
        <Col span={8} key={index}>
          <Card className="card-item" title={card.title}>
            <p>{card.content}</p>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

// 示例3: 使用预设动画
export const PresetExample: React.FC = () => {
  const containerRef = useScrollAnimation(animationPresets.fadeInScale);

  return (
    <div ref={containerRef}>
      <Card className="animate-item" style={{ textAlign: 'center', padding: '40px' }}>
        <Title level={2}>🎉</Title>
        <Title level={3}>缩放淡入效果</Title>
        <Paragraph>这个卡片会以缩放的方式淡入</Paragraph>
      </Card>
    </div>
  );
};

// 示例4: 自定义动画参数
export const CustomExample: React.FC = () => {
  const containerRef = useScrollAnimation({
    selector: '.custom-item',
    initialY: 100,
    duration: 1.2,
    stagger: 0.3,
    ease: 'back.out(1.7)',
  });

  return (
    <div ref={containerRef}>
      <div className="custom-item">
        <Title level={4}>自定义动画 1</Title>
        <Paragraph>更大的初始偏移和弹性效果</Paragraph>
      </div>
      <div className="custom-item">
        <Title level={4}>自定义动画 2</Title>
        <Paragraph>带有回弹效果的动画</Paragraph>
      </div>
    </div>
  );
};

// 示例5: 完全自定义的3D动画
export const Advanced3DExample: React.FC = () => {
  const containerRef = useScrollAnimation({
    selector: '.3d-item',
    customAnimation: {
      from: { 
        rotationY: -90, 
        opacity: 0,
        transformOrigin: 'center center',
        z: -200
      },
      to: { 
        rotationY: 0, 
        opacity: 1, 
        z: 0,
        duration: 1.5,
        stagger: 0.2,
        ease: 'power2.out'
      },
    },
  });

  return (
    <Row gutter={[24, 24]} ref={containerRef}>
      {[1, 2, 3].map((item) => (
        <Col span={8} key={item}>
          <Card 
            className="3d-item" 
            style={{ 
              height: '200px', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ color: 'white', margin: 0 }}>
                3D 卡片 {item}
              </Title>
              <Paragraph style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0' }}>
                3D旋转效果
              </Paragraph>
            </div>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

// 组合示例 - 在一个页面中使用多种动画
export const CombinedExample: React.FC = () => {
  return (
    <div style={{ padding: '40px 20px' }}>
      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          滚动动画示例
        </Title>
        <SimpleExample />
      </div>

      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          卡片动画示例
        </Title>
        <CardListExample />
      </div>

      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          预设动画示例
        </Title>
        <PresetExample />
      </div>

      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          自定义参数示例
        </Title>
        <CustomExample />
      </div>

      <div style={{ marginBottom: '80px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
          3D动画示例
        </Title>
        <Advanced3DExample />
      </div>
    </div>
  );
};

export default CombinedExample;
