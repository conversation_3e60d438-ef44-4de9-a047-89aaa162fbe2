import useCsrfToken from '@/hooks/useCsrfToken';
import {
  checkEmailExistance,
  getEmailcode,
  getVerifyCode,
  regNewUser,
} from '@/services/user';
import { encryptAES } from '@/utils/crypto';
import { history } from '@umijs/max';
import { useMount } from 'ahooks';
import { Avatar, Button, Form, Input, Radio, Space, Spin, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import style from './index.less';
import qrcode_img from './qrcode.png';
import success_img from './success.png';

export default () => {
  const [form] = Form.useForm();
  const [isregsuccess, setIsregsuccess] = useState<boolean>(false);

  const [code, setCode] = useState<string>('');
  const [src, setSrc] = useState<string>('');
  const [loadingVerifyCode, setLoadingVerifyCode] = useState<boolean>(false);
  const [registerLoading, setRegisterLoading] = useState<boolean>(false);

  useMount(() => {
    onGetVerifyCode();
  });

  const { runAsync: getCsrfToken } = useCsrfToken();

  const onGetVerifyCode = async () => {
    setLoadingVerifyCode(true);
    const { vId: verifyCode, img: base64 } = await getVerifyCode();
    setCode(verifyCode);
    setSrc(base64);
    setLoadingVerifyCode(false);
  };

  const onFinish = async (values: any) => {
    const {
      iagree,
      name,
      email,
      password,
      verifyPassword,
      verifyCode,
      captchaCode,
    } = values;

    if (!iagree) {
      form.setFields([{ name: ['iagree'], errors: ['请勾选同意！'] }]);
    } else {
      if (verifyPassword !== password) {
        form.setFields([{ name: ['verifyPassword'], errors: ['密码不一致'] }]);
      } else {
        //post
        try {
          setRegisterLoading(true);
          const { Token: csrfToken } = await getCsrfToken();
          const { status } = await regNewUser(
            {
              name,
              email,
              password: encryptAES(password),
              verifyPassword: encryptAES(verifyPassword),
              regionIds: [1],
              verifyCode,
              captchaCode,
              captchaId: code,
            },
            csrfToken,
          );
          if (status) {
            setIsregsuccess(true);
          }
        } catch ({
          response: {
            data: { Error },
          },
        }: any) {
          console.log(Error);
          onGetVerifyCode();
          form.setFieldValue('captchaCode', '');
        } finally {
          setRegisterLoading(false);
        }
      }
    }
  };

  const [resend, setResend] = useState<number>(0);

  useEffect(() => {
    if (resend > 0) {
      setTimeout(() => {
        setResend(resend - 1);
      }, 1000);
    }
  }, [resend]);

  return (
    <div className={style.container}>
      <div className={style.bg}></div>
      <div className={style.regpan}>
        {isregsuccess ? (
          <div className={style.regsuccess}>
            <Space size={20}>
              <Space direction="vertical" className={style.block}>
                <Avatar shape="square" size={107} src={qrcode_img} />
                <p>关注蓝云，遇见精彩</p>
              </Space>
              <div className={style.line}></div>
              <Space direction="vertical" className={style.block}>
                <Avatar shape="square" size={107} src={success_img} />
                <p>注册成功</p>
              </Space>
            </Space>
            <Button
              className={style.loginButton}
              type="primary"
              onClick={async () => {
                history.push('/login');
              }}
            >
              立即登录
            </Button>
          </div>
        ) : (
          <Form
            style={{
              width: '500px',
              height: '600px',
              padding: '40px 50px 40px 50px',
              borderRadius: '10px',
              background: 'rgba(255, 255, 255, 1)',
              boxShadow:
                '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
            }}
            form={form}
            name="basic"
            // wrapperCol={{ span: 16 }}
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item>
              <h2 className={style.title}>账号注册</h2>
            </Form.Item>

            <Form.Item
              name="name"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input style={{ height: '38px' }} placeholder="请输入您的姓名" />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { required: true, message: '内容不能为空' },
                {
                  pattern:
                    /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/, // 自定义正则 ‌:ml-citation{ref="1,3" data="citationList"}
                  message: '邮箱格式不正确',
                },
              ]}
            >
              <Input
                type="email"
                onChange={(e) =>
                  form.setFields([{ name: 'email', value: e.target.value }])
                }
                style={{ height: '38px' }}
                placeholder="请输入您的邮箱"
              />
            </Form.Item>

            <Form.Item
              name="verifyCode"
              rules={[{ required: true, message: '请输入验证码' }]}
            >
              <Input
                placeholder="请输入验证码"
                style={{ height: '38px', flex: 2 }}
                suffix={
                  resend === 0 ? (
                    <Button
                      type="primary"
                      size="small"
                      onClick={async () => {
                        try {
                          await form.validateFields(['email']); // 主动校验 email 字段 ‌:ml-citation{ref="6,7" data="citationList"}
                          // 校验通过后执行后续逻辑
                          const email = form.getFieldValue('email');
                          const { existance } = await checkEmailExistance(
                            email,
                          );
                          if (existance) {
                            form.setFields([
                              { name: ['email'], errors: ['邮箱已经存在'] },
                            ]);
                          } else {
                            setResend(60);
                            console.log(form.getFieldValue('email'));
                            const res = await getEmailcode(email);
                            console.log(res);
                          }
                        } catch (error) {
                          console.log('校验失败:', error);
                        }
                      }}
                    >
                      发送验证码
                    </Button>
                  ) : (
                    <Button size="small" disabled>
                      {resend}s后重试
                    </Button>
                  )
                }
              />
            </Form.Item>

            <Form.Item
              name="password"
              // rules={[{ required: true, message: '请输入密码' }]}
              rules={[
                {
                  validator(_, value) {
                    if (!value) return Promise.reject('请输入密码');
                    if (value.length < 8)
                      return Promise.reject('密码长度需≥8位');
                    if (!/[a-z]/.test(value))
                      return Promise.reject('缺少小写字母');
                    if (!/[A-Z]/.test(value))
                      return Promise.reject('缺少大写字母');
                    if (!/\d/.test(value)) return Promise.reject('缺少数字');
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input.Password
                style={{ height: '38px' }}
                placeholder="请输入密码，必须包含大小写字母和数字，长度不小于8位"
              />
            </Form.Item>

            <Form.Item
              name="verifyPassword"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password
                style={{ height: '38px' }}
                placeholder="确认密码"
              />
            </Form.Item>
            <Form.Item>
              <div className={style.space}>
                <Form.Item
                  name={'captchaCode'}
                  rules={[{ required: true, message: '请输入验证码' }]}
                  noStyle
                >
                  <Input
                    style={{ height: '38px', flex: 2 }}
                    placeholder="请输入验证码"
                  />
                </Form.Item>

                <Tooltip placement="top" title={'点击刷新'}>
                  <Spin spinning={loadingVerifyCode}>
                    <img
                      onClick={() => {
                        onGetVerifyCode();
                      }}
                      className={style.code}
                      src={`data:image/png;base64,${src}`}
                    />
                  </Spin>
                </Tooltip>
              </div>
            </Form.Item>
            <Form.Item
              name={'iagree'}
              valuePropName="checked"
              initialValue={false}
              rules={[{ required: false, message: '请勾选同意' }]}
            >
              <Radio>
                <span className={style.text}>请您确认阅读并同意</span>
                <a
                  className={style.link}
                  target="_blank"
                  href="/reg/privacyStatement"
                >
                  《个人信息使用协议》
                </a>
                <a
                  className={style.link}
                  target="_blank"
                  href="/reg/networkProtocol"
                >
                  《合作伙伴网络协议》
                </a>
              </Radio>
            </Form.Item>
            <Form.Item>
              <Button
                className={style.reg}
                type="primary"
                htmlType="submit"
                loading={registerLoading}
              >
                注册
              </Button>
            </Form.Item>
          </Form>
        )}
      </div>
    </div>
  );
};
