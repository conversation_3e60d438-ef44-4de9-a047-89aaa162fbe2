import { getOrderDetail } from '@/services/order/PurchaseController';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Modal } from 'antd';
import { createStyles } from 'antd-style';
import { forwardRef, useImperativeHandle, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  paymentOfflineInfo: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 36px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    vertical-align: top;
  `,
  paymentOfflineAlert: css`
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 20.27px;
    color: rgb(222, 50, 50);
    text-align: left;
    vertical-align: top;
    margin-top: 16px;
  `,
}));
interface IConfirmPeriod {
  confirmCallBack: (customerNumber: string) => void;
}
const ConfirmPeriod = forwardRef(
  (props: IConfirmPeriod, ref: React.Ref<any>) => {
    const { confirmCallBack } = props;
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [protocolType, setProtocolType] = useState<number>(1);
    const [data, setData] = useState<any>({});
    const [orderNumber, setOrderNumber] = useState<string>('');
    useImperativeHandle(ref, () => ({
      modalHandleShow,
    }));
    const modalHandleShow = (oNumber: string) => {
      //setProtocolType(type);
      setOrderNumber(oNumber);
      setIsModalOpen(true);
      setLoading(true);
      getOrderDetail(oNumber).then((res) => {
        setLoading(false);
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() + 5);
        res.BillingPeriodTime = currentDate;
        setData(res);
      });
    };
    const modalHandleOk = () => {
      setIsModalOpen(false);
      confirmCallBack(orderNumber);
    };
    const modalHandleCancel = () => {
      setIsModalOpen(false);
    };
    const modalFonter = () => (
      <>
        <div style={{ textAlign: 'center', marginTop: 32 }}>
          <Button
            loading={confirmLoading}
            type="primary"
            style={{ width: 120, marginLeft: 16 }}
            onClick={modalHandleOk}
          >
            确认电子合同
          </Button>
          <Button
            loading={confirmLoading}
            type="primary"
            style={{ width: 120, marginLeft: 16 }}
            onClick={modalHandleCancel}
          >
            取消
          </Button>
        </div>
      </>
    );

    return (
      <Modal
        destroyOnHidden={false}
        title={'账期付款确认'}
        open={isModalOpen}
        loading={loading}
        width={'50%'}
        footer={modalFonter}
        onCancel={modalHandleCancel}
      >
        <Divider />
        <div style={{ fontSize: 18 }}>
          感谢您选择与我们合作，本次交易已为您安排了
          <strong>【账期付款】</strong>服务。
        </div>
        <div
          style={{
            marginTop: 80,
            fontSize: 18,
            lineHeight: '40px',
            color: 'rgb(222, 50, 50)',
          }}
        >
          <div>
            请注意， <strong>【账期付款】</strong>
            是基于我们对贵公司的信任以及长期合作的基础上提供的特别服务,根据我们的协议，此次账期为
            {data.BillingPeriodDays}
            天，您需要按照合同上的蓝云账户信息完成线下付款。
          </div>
        </div>
        <div style={{ color: '#000', marginTop: 32, fontSize: 16 }}>
          如有任何疑问或需要帮助，请随时联系&nbsp;
          <span style={{ color: '#114BED', fontSize: 18 }}>
            {data.BlueCloudSalesEmail}
          </span>
        </div>
      </Modal>
    );
  },
);

export default ConfirmPeriod;
