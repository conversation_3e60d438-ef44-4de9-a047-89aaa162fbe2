import { useScrollAnimation, ScrollAnimationOptions } from './useScrollAnimation';

/**
 * 简化版的淡入动画Hook
 * 专门用于最常见的从下方淡入效果
 * 
 * @param selector 动画目标选择器，默认为 '.fade-in-item'
 * @param options 可选的动画配置
 * @returns 容器ref
 * 
 * @example
 * ```tsx
 * const containerRef = useFadeInAnimation('.my-card');
 * 
 * return (
 *   <div ref={containerRef}>
 *     <div className="my-card">卡片1</div>
 *     <div className="my-card">卡片2</div>
 *   </div>
 * );
 * ```
 */
export const useFadeInAnimation = (
  selector: string = '.fade-in-item',
  options: Partial<ScrollAnimationOptions> = {}
) => {
  return useScrollAnimation({
    selector,
    initialY: 50,
    initialOpacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: 'power2.out',
    start: 'top 85%',
    once: true,
    ...options,
  });
};

/**
 * 快速淡入动画Hook
 * 适用于需要快速响应的场景
 */
export const useFadeInFast = (selector: string = '.fade-in-item') => {
  return useFadeInAnimation(selector, {
    duration: 0.4,
    stagger: 0.1,
    initialY: 30,
  });
};

/**
 * 慢速淡入动画Hook
 * 适用于需要优雅展示的场景
 */
export const useFadeInSlow = (selector: string = '.fade-in-item') => {
  return useFadeInAnimation(selector, {
    duration: 1.2,
    stagger: 0.3,
    initialY: 80,
  });
};

/**
 * 卡片淡入动画Hook
 * 专门为卡片组件优化的动画效果
 */
export const useCardAnimation = () => {
  return useFadeInAnimation('.card-item', {
    initialY: 60,
    duration: 0.8,
    stagger: 0.15,
    ease: 'power2.out',
  });
};

/**
 * 列表项淡入动画Hook
 * 适用于列表项的动画效果
 */
export const useListAnimation = () => {
  return useFadeInAnimation('.list-item', {
    initialY: 30,
    duration: 0.6,
    stagger: 0.1,
    ease: 'power2.out',
  });
};

export default useFadeInAnimation;
