import { getVerifyCode } from '@/services/user';
import { userLogin } from '@/store/user';
import { KeyOutlined, MailOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Avatar, Button, Form, Input, Space, Spin, Tooltip } from 'antd';
// import { encode } from 'js-base64';
import { encryptAES } from '@/utils/crypto';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useState } from 'react';
import style from './index.less';

export default () => {
  const [form] = Form.useForm();
  const [code, setCode] = useState<string>('');
  const [src, setSrc] = useState<string>('');
  const [loadingVerifyCode, setLoadingVerifyCode] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const onFinish = useMemoizedFn(({ email, password, verifyCode }: any) => {
    setLoading(true);
    userLogin({
      email: email,
      password: encryptAES(password),
      captchaId: code,
      captchaCode: verifyCode,
    }).then((res) => {
      setLoading(false);
      if (res === true) {
        history.push('/');
      } else {
        form.setFieldValue('verifyCode', '');
        onGetVerifyCode();
      }
    });
  });

  const onGetVerifyCode = async () => {
    setLoadingVerifyCode(true);
    const { vId: verifyCode, img: base64 } = await getVerifyCode();
    setCode(verifyCode);
    setSrc(base64);
    setLoadingVerifyCode(false);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  useEffect(() => {
    onGetVerifyCode();
  }, []);

  return (
    <div className={style.container}>
      <div className={style.loginpan}>
        <Form
          style={{
            width: '500px',
            height: '600px',
            padding: '40px 50px 40px 50px',
            borderRadius: '10px',
            background: 'rgba(255, 255, 255, 1)',
            boxShadow:
              '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
          }}
          form={form}
          name="basic"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item>
            <h2 className={style.title}>登录</h2>
            <div className={style.subtitle}>欢迎登录云市场</div>
          </Form.Item>
          <Form.Item
            name="email"
            rules={[{ required: true, message: '请输入邮箱' }]}
          >
            <Input
              style={{ height: '38px' }}
              prefix={<MailOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
              placeholder="请输入邮箱"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              style={{ height: '38px' }}
              prefix={<KeyOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
              placeholder="请输入密码"
            />
          </Form.Item>

          <Form.Item>
            <div className={style.space}>
              <Form.Item
                noStyle
                name="verifyCode"
                rules={[{ required: true, message: '请输入验证码' }]}
              >
                <Input
                  style={{ height: '38px', flex: 2 }}
                  placeholder="请输入验证码"
                />
              </Form.Item>

              <Tooltip placement="top" title={'点击刷新'}>
                <Spin spinning={loadingVerifyCode}>
                  <img
                    onClick={() => {
                      onGetVerifyCode();
                    }}
                    className={style.code}
                    src={`data:image/png;base64,${src}`}
                  />
                </Spin>
              </Tooltip>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              className={style.login}
              loading={loading}
              type="primary"
              htmlType="submit"
            >
              登录
            </Button>
            <Button
              htmlType="button"
              className={style.reg}
              style={{ marginTop: '10px' }}
              ghost
              type="primary"
              onClick={() => {
                history.push('/register');
              }}
            >
              注册
            </Button>
          </Form.Item>
          <Form.Item>
            <a
              onClick={() => {
                history.push('/resetpassword');
              }}
              className={style.forgetpassword}
            >
              忘记密码？
            </a>
          </Form.Item>

          <Form.Item>
            <Space className={style.flex}>
              <span className={style.line} />
              <span
                style={{ color: 'rgba(166, 166, 166, 1)', fontSize: '12px' }}
              >
                其他方式登录
              </span>
              <span className={style.line} />
            </Space>
          </Form.Item>
          <Form.Item className={style.flex}>
            <Tooltip placement="top" title={'敬请期待'}>
              <Avatar size={34} />
            </Tooltip>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};
