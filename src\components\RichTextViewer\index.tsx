import DOMPurify from 'dompurify';
import React, { useEffect, useRef } from 'react';

const RichTextViewer: React.FC<{ htmlContent: string }> = ({ htmlContent }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && !containerRef.current.shadowRoot) {
      // 创建 Shadow DOM 容器
      const shadowRoot = containerRef.current.attachShadow({ mode: 'open' });

      // 创建 link 标签引入 CSS 文件
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/quill.bubble.css';
      shadowRoot.appendChild(link);

      // 创建内容容器并插入安全过滤后的 HTML
      const containerDiv = document.createElement('div');
      containerDiv.className = 'ql-container';
      const bubbleDiv = document.createElement('div');
      bubbleDiv.className = 'ql-bubble';
      const editorDom = document.createElement('div');
      editorDom.className = 'ql-editor';
      editorDom.innerHTML = DOMPurify.sanitize(htmlContent); // XSS 过滤
      bubbleDiv.appendChild(editorDom);
      containerDiv.appendChild(bubbleDiv);

      shadowRoot.appendChild(containerDiv);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''; // 清空内容以防止内存泄漏
      }
    };
  }, [htmlContent]);

  return <div ref={containerRef} />;
};

export default RichTextViewer;
