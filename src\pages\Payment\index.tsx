import { ReactComponent as PaymentFinishSvg } from '@/assets/paymentFinish.svg';
import OfflinePayment from '@/components/OfflinePayment';
import PageWrapper from '@/components/PageWrapper';
import PurchaseSteps from '@/components/PurchaseSteps';
import {
  getOrderDetail,
  putPayments,
} from '@/services/order/PurchaseController';
import { PaymentStatus } from '@/types';
import { formatWithThousandSeparator, transform } from '@/utils/currencyUtil';
import { formatDateTime } from '@/utils/format';
import { openUrl } from '@/utils/urlUtil';
import { useLocation, useNavigate } from '@umijs/max';
import type { TableProps } from 'antd';
import {
  Button,
  Col,
  Divider,
  Row,
  Space,
  Table,
  Typography,
  notification,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useRef, useState } from 'react';

const useStyles = createStyles(({ css }) => ({
  paymentTitle: css`
    font-size: 20px;
    font-weight: 500;
    line-height: 40px;
    color: rgba(56, 56, 56, 1);
    vertical-align: top;
  `,
  tablaBorder: css`
    border-radius: 8px;
    background: #fff;
    padding: 16px;
    border: 1px solid rgba(229, 229, 229, 100%);
    margin-top: 16px;
  `,
  tableTitle: css`
    font-size: 16px;
    font-weight: 500;
    line-height: 36px;
    color: rgba(56, 56, 56, 100%);
  `,
  payButton: css`
    width: 120px;
    height: 46px;
    opacity: 1;
    border-radius: 8px;
    background: linear-gradient(224.07deg, #2480e3ff 0%, #2b24e3ff 100%);
    display: inline-block;
    color: #fff;
    line-height: 46px;
    cursor: pointer;
    &:hover {
      color: #fff !important;
      background: linear-gradient(
        224.07deg,
        rgb(68, 134, 205) 0%,
        rgb(13, 5, 247) 80%
      ) !important;
    }
  `,
}));
const Payment = () => {
  const location = useLocation();
  const [api, contextHolder] = notification.useNotification();
  const { CustomOrderNumber } = location.state as any;
  useEffect(() => {
    getOrderDetailData();
  }, []);
  const navigate = useNavigate();
  const offlinePaymentRef = useRef<any>(null);
  const { styles } = useStyles();
  const [loading, setLoading] = useState<boolean>(true);
  const [tableData, setTableData] = useState<any>([]);
  const [orderDetail, setOrderDetail] = useState<any>({});
  const [isPayModalOpen, setIsPayModalOpen] = useState<boolean>(false);
  const columns: TableProps['columns'] = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      align: 'left',
      key: 'orderNumber',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      align: 'left',
      key: 'orderStatus',
    },
    {
      title: '付款状态',
      dataIndex: 'paymentStatus',
      align: 'left',
      key: 'paymentStatus',
    },
    {
      title: '订单总额',
      dataIndex: 'amount',
      align: 'left',
      key: 'amount',
      render: (value) => {
        return <span>{formatWithThousandSeparator(value)}</span>;
      },
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      align: 'left',
      key: 'createTime',
      render: (value) => {
        return formatDateTime(new Date(value), true);
      },
    },
  ];

  const getOrderDetailData = () => {
    setLoading(true);
    getOrderDetail(CustomOrderNumber).then((res) => {
      setLoading(false);
      setOrderDetail(res);
      setTableData([
        {
          orderNumber: CustomOrderNumber,
          paymentStatus: getPaymentStatusName(res.PaymentStatusId),
          orderStatus: res.OrderStatus,
          amount: transform(res.Currency) + res.OrderTotal,
          createTime: res.CreatedOnUtc,
        },
      ]);
    });
  };
  const getPaymentStatusName = (statusId: number) => {
    let displayValue = '';
    const filterList = PaymentStatus.filter((item) => item.id === statusId);
    displayValue =
      filterList && filterList.length > 0 ? filterList[0].value : '';
    return displayValue;
  };
  useEffect(() => {
    getOrderDetailData();
  }, []);

  const payClick = () => {
    if (orderDetail.PaymentMethodId === 1) {
      onlinePayment(orderDetail.PaymentNumber);
    } else {
      if (offlinePaymentRef.current) {
        offlinePaymentRef.current.modalHandleShow(
          'pay',
          {
            paymentNumber: orderDetail.PaymentNumber,
            customOrderNumber: orderDetail.CustomOrderNumber,
          },
          false,
        );
      }
    }
  };
  const onlinePayment = (paymentNumber: string) => {
    const returnUrl = window.location.href;
    putPayments(paymentNumber, {
      returnUrl: returnUrl,
    }).then(
      (res) => {
        openUrl(res.PaymentUrl);
      },
      () => {
        api['error']({
          message: '付款失败',
          duration: 2,
        });
      },
    );
  };

  const nativeToOrderDetail = () => {
    navigate('/order/detail', {
      state: { CustomOrderNumber: CustomOrderNumber, BackType: 2 },
    });
  };
  const nativeToHome = () => {
    navigate('/');
  };
  const nativeToOrderList = () => {
    navigate('/enterprise/orders');
  };
  return (
    <div style={{ paddingTop: 50 }}>
      {contextHolder}
      <PageWrapper>
        <div>
          <Row>
            <Col span={12}>
              <Typography.Title level={3}>支付页面</Typography.Title>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <PurchaseSteps stepValue={2} />
            </Col>
          </Row>
        </div>
        <div>
          <div style={{ textAlign: 'center', width: '100%' }}>
            <PaymentFinishSvg />
            <div className={styles.paymentTitle}>订单已生成</div>
            <Space style={{ lineHeight: '50px' }}>
              <Button type="link" onClick={nativeToOrderDetail}>
                查看详情
              </Button>
              <Divider type="vertical" style={{ borderColor: '#E5E5E5FF' }} />
              <Button type="link" onClick={nativeToOrderList}>
                订单管理
              </Button>
              <Divider type="vertical" style={{ borderColor: '#E5E5E5FF' }} />
              <Button type="link" onClick={nativeToHome}>
                返回首页
              </Button>
            </Space>
          </div>

          <div className={styles.tablaBorder}>
            <div className={styles.tableTitle}>订单信息</div>
            <Table
              pagination={false}
              columns={columns}
              dataSource={tableData}
            />
          </div>
          <div style={{ textAlign: 'center', marginTop: '50px' }}>
            {orderDetail.PaymentMethodId === 1 && (
              <div onClick={payClick} className={styles.payButton}>
                立即支付
              </div>
            )}
            {orderDetail.PaymentMethodId === 2 && !orderDetail.ChangePrice && (
              <div onClick={payClick} className={styles.payButton}>
                支付帮助
              </div>
            )}
          </div>
        </div>
        <OfflinePayment confirmCallBack={() => {}} ref={offlinePaymentRef} />
      </PageWrapper>
    </div>
  );
};

export default Payment;
