export function openUrl(url: string): boolean {
        const linkElement = document.createElement('a');
        try {
            linkElement.setAttribute('href', url);
            linkElement.setAttribute('target', '_blank');

            const clickEvent = new MouseEvent('click', {
                'view': window,
                'bubbles': true,
                'cancelable': false
            });
            linkElement.dispatchEvent(clickEvent);
            return true;
        } catch ( e ) {
            return false;
        }
    }