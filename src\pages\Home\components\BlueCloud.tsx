import { createStyles } from "antd-style";

const useStyles = createStyles(({ css }) => ({
    container: css`
        width: 1396px;
        margin: 28px auto 0;
        columns:4;
        column-gap:12px;
    `,
    cloud: css`
        height: 118px;
        border-radius: 10px;
        background-image: url(${require("@/assets/bluecloudBg.png")});
        transition: all 0.3s;
        transform: translateY(0);
        &:hover{
            background-image: url(${require("@/assets/bluecloudBg-hover.png")});
            background-size: cover;
            transform: translateY(-10px);
            transition: all 0.3s;
        )
    `,
}))

const BlueCloud = () => {
    const { styles } = useStyles();

    return (
        <div className={styles.container}>
            <div className={styles.cloud}></div>
            <div className={styles.cloud}></div>
            <div className={styles.cloud}></div>
            <div className={styles.cloud}></div>
        </div>
    )
}

export default BlueCloud;