<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="Api Proxy" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="^api/(.*)" />
                    <action type="Rewrite" url="https://mplace-test-api.21vbluecloud.com/{R:1}"
                        logRewrittenUrl="true" />
                </rule>
                <rule name="React Router" patternSyntax="Wildcard" stopProcessing="false">
                    <match url="*" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/index.html" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>