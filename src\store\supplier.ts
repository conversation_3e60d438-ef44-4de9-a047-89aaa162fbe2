import { getSuppliers, getSuppliersDisplayed } from '@/services/api';
import { uniq } from 'lodash-es';
import { persist } from 'valtio-persist';
import { useProxy } from 'valtio/utils';

interface SupplierState {
  list: string[];
  displayedList: string[];
}
const { store: supplierState } = await persist<SupplierState>(
  {
    list: [],
    displayedList: [],
  },
  'supplier',
);

const useSupplierStore = () => useProxy(supplierState);

const querySuppliers = async () => {
  try {
    const result = await getSuppliers();

    supplierState.list = uniq(result || []);
  } catch (e) {
    console.error(e);
  }
};

const querySuppliersDisplayed = async () => {
  try {
    const result = await getSuppliersDisplayed();

    supplierState.displayedList = uniq(result || []);
  } catch (e) {
    console.error(e);
  }
};

export { querySuppliers, querySuppliersDisplayed, useSupplierStore };
