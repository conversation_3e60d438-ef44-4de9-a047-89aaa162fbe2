import { useUpdateEffect } from 'ahooks';
import { Button, InputNumber, Space } from 'antd';
import { createStyles } from 'antd-style';
import useQuantity from '../hooks/useQuantity';

interface IInputNum {
  value: number;
  maxValue: number;
  disabled: boolean;
  onChange?: (value: number) => void;
}

const useStyles = createStyles(({ prefixCls, css }) => ({
  button: css`
    &.${prefixCls}-btn {
      width: 32px;
      height: 32px;
    }
  `,
  number: css`
    &.${prefixCls}-input-number {
      width: 47px;
    }
    &.${prefixCls}-input-number .${prefixCls}-input-number-input {
      height: 30px;
      text-align: center;
    }
  `,
}));
const InputNum: React.FC<IInputNum> = (props) => {
  const { value = 1, disabled, onChange, maxValue } = props;
  const { styles } = useStyles();

  const { debounceNumber, number, minus, plus, handleChange } =
    useQuantity(value);

  const handleChangeNumber = (value: number | null) => {
    if (onChange) {
      onChange(value as number);
    }
  };

  useUpdateEffect(() => {
    handleChangeNumber(debounceNumber);
  }, [debounceNumber]);

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Button
        type="default"
        onClick={minus}
        size="small"
        disabled={number <= 1 || disabled}
        className={styles.button}
      >
        -
      </Button>
      <InputNumber
        size="small"
        min={1}
        value={number}
        onChange={handleChange}
        disabled={disabled}
        controls={false}
        className={styles.number}
      />
      <Button
        type="default"
        onClick={plus}
        size="small"
        disabled={number >= maxValue || disabled}
        className={styles.button}
      >
        +
      </Button>
    </Space.Compact>
  );
};

export default InputNum;
