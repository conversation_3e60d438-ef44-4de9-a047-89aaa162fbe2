import { request } from '@umijs/max';

export type BrandZoneSolutionModel = {
  GlobalId: string;
  Name: string;
  ShortDescription: string;
  LogoUrl: string;
  IsHot: boolean;
  SalesTotal: number;
  CategoryId: number;
  Rating: number;
  LimitPurchase: boolean;
  Id: number;
  Currency: string;
  ShowedPrice: number;
};

export type BrandInfo = {
  BrandName: string;
  FocusPictureUrl: string;
  BrandDetailsDescription: string;
  BrandZoneSolutionsModels: BrandZoneSolutionModel[];
  Id: number;
};
export async function getBrand(id: number): Promise<BrandInfo> {
  return request(`/api/Brands/${id}`);
}
