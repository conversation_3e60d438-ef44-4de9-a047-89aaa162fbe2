import CategoriesTree, { treeData } from '@/components/CategoriesTree';
import StandardFormRow from '@/components/StandardFormRow';
import TagSelect, { TagSelectOption } from '@/components/TagSelect';
import useTags from '@/components/TagSelect/hooks/useTags.';
import { DeliveryMethod } from '@/constants';
import { SearchParams } from '@/pages/SearchPage/services';
import { Flex } from 'antd';
import { FC, memo, useEffect } from 'react';

interface SearchCardProps {
  searchValues: SearchParams;
  updateSearchValues: (values: Partial<SearchParams>) => void;
  categoriesTreeData: treeData[];
}

const SearchCard: FC<SearchCardProps> = ({
  searchValues,
  updateSearchValues,
  categoriesTreeData,
}) => {
  const { selectedKeys, onTagClick } = useTags({
    dataSource: DeliveryMethod,
    type: 'radio',
    defaultValue: ['all'],
  });

  useEffect(() => {
    updateSearchValues({
      DeliveryMethod: selectedKeys[0] === 'all' ? undefined : selectedKeys[0],
    });
  }, [JSON.stringify(selectedKeys)]);

  return (
    <div>
      <Flex justify="space-between" gap={16}>
        <div>
          <StandardFormRow title={null}>
            <TagSelect
              value={selectedKeys}
              items={DeliveryMethod}
              hideCheckAll
              renderItems={(item) => (
                <TagSelectOption
                  value={item.value}
                  checked={selectedKeys.includes(item.value)}
                  onChange={onTagClick}
                >
                  {item.label}
                </TagSelectOption>
              )}
            ></TagSelect>
          </StandardFormRow>
        </div>
        <div>
          <StandardFormRow title={null}>
            <CategoriesTree
              treeData={categoriesTreeData}
              value={searchValues.CategoryId}
              onChange={(value) => {
                updateSearchValues({ CategoryId: value });
              }}
            />
          </StandardFormRow>
        </div>
      </Flex>
    </div>
  );
};

export default memo(SearchCard);
