import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import { getInvoices } from '@/services/order/InvoiceController';
import { formatDateTime } from '@/utils/format';
import { useNavigate } from '@umijs/max';
import {
  Button,
  Flex,
  Form,
  Input,
  notification,
  Pagination,
  PaginationProps,
  Space,
  Table,
  TableProps,
  Tag,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
const InvoiceHistory = () => {
  interface DataType {
    TitleCompanyName: string;
    ReceiveAddress: string;
    CustomOrderNumber: string;
    TotalAmount: string;
    CreatedOnUtc: string;
    InvoiceStatus: string;
    Id: number;
  }
  const navigate = useNavigate();
  const [searchForm] = Form.useForm();
  const [api, contextHolder] = notification.useNotification();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  useState<boolean>(false);
  const [data, setData] = useState<DataType[]>([]);
  const pageSize = 10;
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [total, setTotal] = useState<number>(1);
  const [tableLoading, setTableLoading] = useState<boolean>(true);

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '申请日期',
      dataIndex: 'CreatedOnUtc',
      key: 'CreatedOnUtc',
      render: (value) => <>{formatDateTime(new Date(value), true)}</>,
    },
    {
      title: '发票抬头',
      dataIndex: 'TitleCompanyName',
      key: 'TitleCompanyName',
    },
    {
      title: '接收地址',
      dataIndex: 'ReceiveAddress',
      key: 'ReceiveAddress',
    },
    {
      title: '订单编号',
      key: 'CustomOrderNumber',
      dataIndex: 'CustomOrderNumber',
    },
    {
      title: '开票金额',
      key: 'TotalAmount',
      dataIndex: 'TotalAmount',
      render: (value) => {
        return '￥' + value;
      },
    },
    {
      title: '状态',
      key: 'InvoiceStatus',
      dataIndex: 'InvoiceStatus',
      render: (value) => {
        if (value === 1) {
          return <Tag color="processing">开票中</Tag>;
        } else if (value === 2) {
          return <Tag color="success">已开票</Tag>;
        } else if (value === 3) {
          return <Tag color="error">已拒绝</Tag>;
        } else {
          return <Tag color="processing">未开票</Tag>;
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              navigate(
                {
                  pathname: '/enterprise/invoice/detail',
                },
                {
                  state: {
                    CustomOrderNumber: record.CustomOrderNumber,
                    BackType: 0,
                  },
                },
              );
            }}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const getList = (page: number) => {
    let values = searchForm.getFieldsValue();
    setPageIndex(page);
    setTableLoading(true);
    getInvoices({ ...values, PageIndex: page, PageSize: pageSize }).then(
      (res) => {
        setTotal(res.Total);
        setData(res.Data);
        setTableLoading(false);
      },
    );
  };

  const onPageChange: PaginationProps['onChange'] = (page) => {
    getList(page);
  };
  useEffect(() => {
    getList(1);
  }, []);

  return (
    <div>
      {contextHolder}

      <Typography.Title level={5}>开票历史</Typography.Title>
      <Flex
        justify="space-between"
        style={{ width: '100%', marginBottom: 20, paddingRight: 8 }}
      >
        <Form
          layout={'inline'}
          labelCol={{ flex: '70px' }}
          wrapperCol={{ flex: 'auto' }}
          labelAlign="left"
          form={searchForm}
          style={{
            maxWidth: 'none',
            paddingTop: 16,
            paddingBottom: 16,
          }}
        >
          <Form.Item label="订单编号" name="CustomOrderNumber">
            <Input placeholder="搜索订单编号" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <Form.Item label="发票抬头" name="TitleCompanyName">
            <Input placeholder="搜索发票抬头" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <div style={{ textAlign: 'left' }}>
            <Button
              type="primary"
              style={{ width: 80 }}
              onClick={() => {
                getList(1);
              }}
              // icon={<SearchOutlined />}
            >
              搜索
            </Button>
          </div>
        </Form>
      </Flex>
      <Table<DataType>
        loading={tableLoading}
        pagination={false}
        rowKey={(record) => record.Id}
        columns={columns}
        dataSource={data}
      />
      {total > pageSize && (
        <Pagination
          style={{ marginTop: 16 }}
          align="end"
          current={pageIndex}
          pageSize={pageSize}
          onChange={onPageChange}
          total={total}
        />
      )}
    </div>
  );
};

export default InvoiceHistory;
