.helpCenter {
  overflow: hidden;
  margin-top: -48px;
  background-color: #fff;
}

.banner {
  width: 100vw;
  height: 300px;
  position: relative;
  background-image: url('@/assets/brandZoneBg.png');
}

.box {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
}

.head {
  width: 1200px;
}

.head .title {
  font-size: 36px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 52.13px;
  color: rgba(69, 69, 69, 100%);
  text-align: left;
  vertical-align: top;
  margin-top: 70px;
  margin-bottom: 16px;
}

.p {
  font-size: 18px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 26.06px;
  color: rgba(128, 128, 128, 100%);
  text-align: left;
  vertical-align: top;
  margin: 10px 0;
}

.page {
  width: 1200px;
  margin: 24px auto 0;
}

.antLstItem {
  gap: 24px;
  display: flex;
  flex-direction: column;
  align-items: start !important;
  margin-bottom: 22px;
}

.meta {
  width: 100%;
  line-height: 33px;
  font-size: 22px;
}

.avatar {
  width: 33px;
  height: 33px;
  border-radius: 0;
}

.ant-list-item-meta-content {
  width: 100% !important;
}
