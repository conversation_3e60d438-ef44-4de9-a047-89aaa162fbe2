import { useMemoizedFn } from 'ahooks';
import { Select } from 'antd';
import { FC, memo } from 'react';

interface SupplierSelectProps {
  options: { label: string; value: string }[];
  value: string | undefined;
  onChange: (value: string) => void;
}

const SupplierSelect: FC<SupplierSelectProps> = ({
  value,
  onChange,
  options,
}) => {
  const handleChange = useMemoizedFn((value: string) => {
    onChange(value);
  });
  return (
    <Select
      style={{ width: 230 }}
      options={options}
      placeholder="请选择供应商"
      allowClear
      showSearch
      onChange={handleChange}
      value={value}
      filterOption={(input, option) =>
        (option?.label ?? '')?.toLowerCase().includes(input.toLowerCase())
      }
    />
  );
};

export default memo(SupplierSelect);
