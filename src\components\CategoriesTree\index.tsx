import { CategoryItem } from '@/pages/Solution/services';
import { TreeSelect } from 'antd';
import { createStyles } from 'antd-style';
import { FC, memo, ReactNode } from 'react';

interface CategroitesTreeProps {
  value: string | undefined;
  onChange: (value: string) => void;
  treeData: treeData[];
}

const useStyle = createStyles(({ css }) => ({
  tree: css`
    .ant-select-tree-node-content-wrapper:not(
        .ant-select-tree-node-content-wrapper-normal
      )
      .ant-select-tree-title {
      font-weight: 600;
    }
  `,
}));

const CategoriesTree: FC<CategroitesTreeProps> = ({
  value,
  onChange,
  treeData,
}) => {
  const { styles } = useStyle();

  return (
    <TreeSelect
      classNames={{
        popup: {
          root: styles.tree,
        },
      }}
      placeholder="全部分类"
      style={{ width: 230 }}
      allowClear={true}
      onChange={onChange}
      treeData={treeData}
      value={value}
      styles={{
        popup: {
          root: {
            maxHeight: 400,
            overflow: 'auto',
          },
        },
      }}
    />
  );
};
export default memo(CategoriesTree);

export type treeData = {
  title: ReactNode;
  value: string;
  children?: treeData[];
  disabled?: boolean;
};
/**
 * 将扁平结构的CategoryItem数组转换为树形结构的NodeB数组
 * @param items 扁平数据
 * @param parentId 当前处理的父节点ID（默认0表示根节点）
 * @returns 树形结构数据
 */
export function transformToTree(
  items: CategoryItem[],
  parentId: number = 0,
): treeData[] {
  if (!items || Array.isArray(items) === false) return [];
  return items
    .filter((item) => item.ParentId === parentId)
    .map((item) => {
      const children = transformToTree(items, item.Id);
      const node: treeData = {
        title: item.Name,
        value: item.Id.toString(),
        disabled: !item.HasProducts,
      };
      if (children.length > 0) {
        node.children = children;
      }
      return node;
    });
}
