import ContactUs from '@/components/ContactUs';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import useBeforeUnload from '@/hooks/useBeforeUnload';
import {
  queryAllCategories,
  queryAllCategoriesDisplayed,
  useCategoriesStore,
} from '@/store/categroies';
import { querySuppliers, querySuppliersDisplayed } from '@/store/supplier';
import { resetState as resetUserStore } from '@/store/user';
import { Outlet, useLocation } from '@umijs/max';
import { useMount } from 'ahooks';
import { FloatButton } from 'antd';
import { useEffect } from 'react';

const Index = () => {
  const { pathname } = useLocation();
  const categoriesStore = useCategoriesStore();

  useMount(() => {
    queryAllCategories();
    queryAllCategoriesDisplayed();
    querySuppliers();
    querySuppliersDisplayed();
  });

  useBeforeUnload(() => {
    resetUserStore();
  });

  useEffect(() => {
    if (!pathname.includes('/solution')) {
      categoriesStore.solutionSelectedOneId = 'all';
      categoriesStore.solutionSelectedTwoId = 'all';
    }
  }, [pathname]);

  return (
    <>
      <Header />
      <Outlet />
      <Footer />
      <ContactUs />
      <FloatButton.BackTop />
    </>
  );
};

export default Index;
