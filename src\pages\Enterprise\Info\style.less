.upload {
  width: 415px;
  height: 260px;
  opacity: 1;
  border-radius: 8px;
  background: rgba(250, 251, 255, 100%);
  border: 1px dashed rgba(229, 229, 229, 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.uploadAlert {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.titlewapper {
  display: flex;
  align-items: center;
  justify-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.cardtitle {
  display: block;
  width: 4px;
  height: 18px;
  border-radius: 10px;
  background: linear-gradient(
    112.6deg,
    rgba(17, 75, 237, 10%) 0%,
    rgba(17, 75, 237, 100%) 100%
  );
}

.tag {
  border-radius: 8px !important;
  height: 32px;
  line-height: 32px;
  padding-left: 16px;
  padding-right: 16px;
  // background: rgba(217, 245, 229, 1) !important;
  // color: rgba(67, 207, 124, 1) !important;
  // border: 1px solid rgba(67, 207, 124, 1) !important;
}

.flex {
  display: flex;
  width: 100%;
  overflow-x: auto;
}

.thumbBox {
  width: 415px;
  height: 260px;
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  justify-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-right: 16px;
}

.thumbPic {
  width: 415px;
  height: 260px;
}

.image {
  width: 60px;
  height: 80px;
  background-repeat: no-repeat;
  background-size: cover;
}

.imagePng {
  width: 60px;
  height: 80px;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/PNG.png');
}

.imageJpe {
  width: 60px;
  height: 80px;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/JPG.png');
}

.pdf {
  width: 60px;
  height: 80px;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/PDF.png');
}

.uploadWapper {
  display: flex;
  flex-flow: row-reverse;
}

.closebtn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  border: 0;
}

.viewbtn {
  display: none;
}

.thumbBox:hover .viewbtn {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 32px;
  height: 32px;
  border: 0;
  display: block;
}

.submit {
  width: 120px;
  height: 32px;
  opacity: 1;
  border-radius: 8px;
  background: rgba(11, 72, 235, 100%);
}

.companyInfoTitle {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 48px;
  color: rgba(128, 128, 128, 100%);
  text-align: left;
  vertical-align: top;
}

.companyInfo {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 48px;
  color: rgba(56, 56, 56, 100%);
  text-align: left;
  vertical-align: top;
  padding: 8px;
}
