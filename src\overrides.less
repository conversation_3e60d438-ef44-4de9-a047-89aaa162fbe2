* {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

#root {
  background: linear-gradient(
    180deg,
    rgba(237, 244, 255, 100%) 0%,
    rgba(255, 255, 255, 78%) 100%
  );
  min-width: 1280px;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* 定义滑块（滚动条滑动部分） */
::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 hsla(0deg, 0%, 94.1%, 50%) !important;
  border-radius: 4px !important;
  background-color: #999 !important;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* 设置鼠标移入时的滑块颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #969696 !important;
}

.ant-select-dropdown-categories-tree
  .ant-select-tree-node-content-wrapper-close,
.ant-select-dropdown-categories-tree
  .ant-select-tree-node-content-wrapper-open {
  color: #000;
  font-weight: 600;
}
