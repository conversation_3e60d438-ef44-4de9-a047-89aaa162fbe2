const CryptoJS = require('crypto-js');
export function encryptAES(plainText: any) {
  const utf8Key = CryptoJS.enc.Utf8.parse('@#$^&%!$#21vbluecloudwwww12345AB');
  const iv = CryptoJS.enc.Utf8.parse('ABCDEFGH12345678');
  const encrypted = CryptoJS.AES.encrypt(plainText, utf8Key, {
    iv: iv,
    padding: CryptoJS.pad.Pkcs7, // 指定填充方式
    mode: CryptoJS.mode.CBC,
  });
  return encrypted.toString(); // 返回 Base64 密文
}
