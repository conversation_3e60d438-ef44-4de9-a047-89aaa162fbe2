import { PropsWithChildren } from 'react';

const ColorTag: React.FC<
  PropsWithChildren & {
    textColor: string;
    borderColor?: string;
    backgroundColor?: string;
    ghost?: boolean;
    style?: React.CSSProperties;
    shape?: 'round' | 'square';
  }
> = ({
  children,
  textColor,
  borderColor,
  backgroundColor,
  ghost = false,
  style = {},
  shape = 'square',
}) => {
  return (
    <span
      style={{
        display: 'inline-block',
        padding: '3px 12px',
        textAlign: 'center',
        fontSize: 12,
        backgroundColor: ghost ? 'transparent' : backgroundColor || '#f0f0f0',
        border: `1px solid ${!ghost ? 'transparent' : borderColor || '#ccc'}`,
        color: textColor || '#6b6b6b',
        borderRadius: shape === 'round' ? 20 : 6,
        ...style,
      }}
    >
      {children}
    </span>
  );
};

export default ColorTag;
