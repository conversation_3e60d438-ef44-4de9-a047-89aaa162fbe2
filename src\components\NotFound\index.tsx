import { useNavigate } from '@umijs/max';
import { Button } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const useStyles = createStyles(({ css }) => ({
  container: css`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 50px);
    background: #fff url(${require('@/assets/error.png')}) no-repeat center;
  `,
  button: css`
    align-self: flex-end;
    margin-bottom: 50px;
  `,
}));
const NotFound: React.FC<{ jumpType: 'home' | 'back' }> = ({ jumpType }) => {
  const navigate = useNavigate();
  const { styles } = useStyles();
  const goHome = () => {
    navigate('/');
  };

  const goBack = () => {
    navigate(-1);
  };
  return (
    <div className={styles.container}>
      {jumpType === 'home' ? (
        <Button type="primary" className={styles.button} onClick={goHome}>
          返回首页
        </Button>
      ) : (
        <Button type="primary" className={styles.button} onClick={goBack}>
          返回上一页
        </Button>
      )}
    </div>
  );
};

export default NotFound;
