import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';
import { Card, Col, Row, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const { Title, Paragraph } = Typography;

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
  `,
  sectionTitle: css`
    text-align: center;
    margin-bottom: 60px;
    
    h2 {
      color: ${token.colorTextHeading};
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    
    p {
      color: ${token.colorTextSecondary};
      font-size: 18px;
      max-width: 600px;
      margin: 0 auto;
    }
  `,
  featureCard: css`
    height: 100%;
    border-radius: 12px;
    border: 1px solid ${token.colorBorderSecondary};
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
      border-color: ${token.colorPrimary};
    }
    
    .ant-card-body {
      padding: 32px 24px;
      text-align: center;
    }
  `,
  icon: css`
    font-size: 48px;
    color: ${token.colorPrimary};
    margin-bottom: 24px;
    display: block;
  `,
  cardTitle: css`
    color: ${token.colorTextHeading};
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
  `,
  cardDescription: css`
    color: ${token.colorTextSecondary};
    font-size: 14px;
    line-height: 1.6;
  `,
}));

interface FeatureItem {
  id: string;
  icon: string;
  title: string;
  description: string;
}

interface FeatureCardsProps {
  title?: string;
  subtitle?: string;
  features: FeatureItem[];
  animationType?: 'default' | 'scale' | 'left' | 'right' | 'fast' | 'slow';
}

/**
 * 特性卡片组件 - 展示产品特性或服务优势
 * 支持多种动画效果
 */
const FeatureCards: React.FC<FeatureCardsProps> = ({
  title = '核心特性',
  subtitle = '为您提供全方位的解决方案',
  features,
  animationType = 'default',
}) => {
  const { styles } = useStyles();

  // 根据动画类型选择对应的配置
  const getAnimationConfig = () => {
    switch (animationType) {
      case 'scale':
        return animationPresets.fadeInScale;
      case 'left':
        return animationPresets.fadeInLeft;
      case 'right':
        return animationPresets.fadeInRight;
      case 'fast':
        return animationPresets.fadeInFast;
      case 'slow':
        return animationPresets.fadeInSlow;
      default:
        return {
          selector: '.feature-card',
          initialY: 60,
          initialOpacity: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
        };
    }
  };

  const containerRef = useScrollAnimation(getAnimationConfig());

  if (!features || features.length === 0) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.sectionTitle}>
        <Title level={2}>{title}</Title>
        <Paragraph>{subtitle}</Paragraph>
      </div>
      
      <Row gutter={[24, 24]} ref={containerRef}>
        {features.map((feature) => (
          <Col xs={24} sm={12} lg={8} key={feature.id}>
            <Card className={`${styles.featureCard} feature-card`}>
              <span className={styles.icon}>{feature.icon}</span>
              <Title level={4} className={styles.cardTitle}>
                {feature.title}
              </Title>
              <Paragraph className={styles.cardDescription}>
                {feature.description}
              </Paragraph>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default FeatureCards;

// 使用示例数据
export const sampleFeatures: FeatureItem[] = [
  {
    id: '1',
    icon: '🚀',
    title: '高性能',
    description: '基于现代化架构设计，提供卓越的性能表现，确保系统稳定运行。',
  },
  {
    id: '2',
    icon: '🔒',
    title: '安全可靠',
    description: '采用企业级安全标准，多重加密保护，确保数据安全无忧。',
  },
  {
    id: '3',
    icon: '⚡',
    title: '快速部署',
    description: '一键部署，快速上线，大幅缩短项目交付周期。',
  },
  {
    id: '4',
    icon: '🎯',
    title: '精准定位',
    description: '智能分析用户需求，提供个性化解决方案。',
  },
  {
    id: '5',
    icon: '📊',
    title: '数据洞察',
    description: '实时数据分析，帮助您做出更明智的业务决策。',
  },
  {
    id: '6',
    icon: '🌐',
    title: '全球服务',
    description: '覆盖全球的服务网络，为您提供24/7不间断支持。',
  },
];
