import { render } from '@testing-library/react';
import React from 'react';
import { useScrollAnimation, animationPresets } from '../useScrollAnimation';

// Mock GSAP
jest.mock('gsap', () => ({
  gsap: {
    registerPlugin: jest.fn(),
    set: jest.fn(),
    to: jest.fn(),
  },
  ScrollTrigger: {
    create: jest.fn(),
  },
}));

jest.mock('@gsap/react', () => ({
  useGSAP: jest.fn((callback, options) => {
    // 模拟 useGSAP 的行为
    callback();
  }),
}));

// 测试组件
const TestComponent: React.FC<{ options?: any }> = ({ options }) => {
  const containerRef = useScrollAnimation(options);
  
  return (
    <div ref={containerRef} data-testid="container">
      <div className="animate-item">Item 1</div>
      <div className="animate-item">Item 2</div>
    </div>
  );
};

describe('useScrollAnimation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { getByTestId } = render(<TestComponent />);
    expect(getByTestId('container')).toBeInTheDocument();
  });

  it('should work with default options', () => {
    render(<TestComponent />);
    // 验证 hook 能正常工作，不抛出错误
    expect(true).toBe(true);
  });

  it('should work with custom options', () => {
    const customOptions = {
      selector: '.custom-item',
      initialY: 100,
      duration: 1.5,
      stagger: 0.3,
    };
    
    render(<TestComponent options={customOptions} />);
    expect(true).toBe(true);
  });

  it('should work with animation presets', () => {
    render(<TestComponent options={animationPresets.fadeInScale} />);
    expect(true).toBe(true);
  });

  it('should export animation presets', () => {
    expect(animationPresets).toBeDefined();
    expect(animationPresets.fadeInUp).toBeDefined();
    expect(animationPresets.fadeInDown).toBeDefined();
    expect(animationPresets.fadeInLeft).toBeDefined();
    expect(animationPresets.fadeInRight).toBeDefined();
    expect(animationPresets.fadeInScale).toBeDefined();
    expect(animationPresets.fadeInFast).toBeDefined();
    expect(animationPresets.fadeInSlow).toBeDefined();
  });
});
