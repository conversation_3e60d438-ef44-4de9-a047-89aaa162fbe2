import { useDebounce } from 'ahooks';
import { useState } from 'react';

function useQuantity(quantity: number) {
  const [number, setNumber] = useState<number>(quantity);

  const debounceNumber = useDebounce(number, { wait: 300 });

  const minus = () => {
    setNumber(number - 1);
  };
  const plus = () => {
    setNumber(number + 1);
  };
  const handleChange = (value: number | null) => {
    if (!value) {
      setNumber(1);
    } else {
      setNumber(value as number);
    }
  };

  return {
    number,
    debounceNumber,
    minus,
    plus,
    handleChange,
  };
}

export default useQuantity;
