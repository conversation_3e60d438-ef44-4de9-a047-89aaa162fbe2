import { NavMenuItem } from '@/components/NavMenu';
import { CategoryItemDisplayed } from '@/services/api';
import { find, flatMapDeep } from 'lodash-es';
import { proxy, snapshot, subscribe } from 'valtio';

export function proxyWithPersist<V>(
  val: V,
  opts: {
    key: string;
  },
) {
  const local = sessionStorage.getItem(opts.key);
  const state = proxy(local ? JSON.parse(local) : val);
  subscribe(state, () => {
    sessionStorage.setItem(opts.key, JSON.stringify(snapshot(state)));
  });
  return state;
}

export function formatToNavMenuItems(
  data: CategoryItemDisplayed[],
): NavMenuItem[] {
  if (!data) return [];

  const buildTree = (parentId: number): NavMenuItem[] => {
    return data
      .filter((item) => item.ParentId === parentId)
      .map((item) => {
        const children = buildTree(item.Id);
        return {
          name: item.Name,
          key: String(item.Id),
          disabled: !item.HasProducts,
          ...(children.length > 0 && { children }),
        };
      });
  };

  return buildTree(0);
}

export function findItemById(
  menuItems: NavMenuItem[],
  id: string,
): NavMenuItem | undefined {
  return find(
    flatMapDeep(menuItems, (item) => [item, ...(item.children || [])]),
    { key: id },
  );
}
