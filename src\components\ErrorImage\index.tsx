import { FC, useEffect, useState } from 'react';

interface ImageProps {
  src: string;
  alt?: string;
  defaultSrc?: string;
}

const ErrorImage: FC<ImageProps> = ({
  src,
  alt = 'image',
  defaultSrc = require('@/assets/default.png'),
}) => {
  const [imageSrc, setImageSrc] = useState(defaultSrc);

  useEffect(() => {
    const img = new Image(); // 创建一个新的Image对象
    img.src = src;
    // 图片加载失败时设置默认图片
    img.onerror = () => {
      setImageSrc(defaultSrc);
    };

    // 图片加载成功时不进行任何操作，保持当前src
    img.onload = () => {
      // 可以在这里处理加载成功的逻辑，例如设置一个加载成功的标志
      setImageSrc(src);
    };

    // 组件卸载时清理
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src, defaultSrc]); // 依赖数组中包含src和defaultSrc

  return (
    <img
      style={{ maxWidth: '100%', maxHeight: '100%' }}
      src={imageSrc}
      alt={alt}
    />
  );
};

export default ErrorImage;
