import { useGSAP } from '@gsap/react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useRef } from 'react';

// 注册GSAP插件
gsap.registerPlugin(useGSAP, ScrollTrigger);

export interface ScrollAnimationOptions {
  /** 动画目标选择器，默认为 '.animate-item' */
  selector?: string;
  /** 触发位置，默认为 'top 85%' */
  start?: string;
  /** 初始Y轴偏移，默认为50 */
  initialY?: number;
  /** 初始透明度，默认为0 */
  initialOpacity?: number;
  /** 动画持续时间，默认为0.8秒 */
  duration?: number;
  /** 交错延迟，默认为0.2秒 */
  stagger?: number;
  /** 缓动函数，默认为 'power2.out' */
  ease?: string;
  /** 是否只触发一次，默认为true */
  once?: boolean;
  /** 自定义动画配置 */
  customAnimation?: {
    from?: gsap.TweenVars;
    to?: gsap.TweenVars;
  };
}

/**
 * 滚动进入动画Hook
 * 当容器进入视口时，对指定选择器的元素执行动画
 * 
 * @param options 动画配置选项
 * @returns 容器ref，需要绑定到触发动画的容器元素上
 * 
 * @example
 * ```tsx
 * const containerRef = useScrollAnimation({
 *   selector: '.product-card',
 *   initialY: 50,
 *   duration: 0.8,
 *   stagger: 0.2
 * });
 * 
 * return (
 *   <div ref={containerRef}>
 *     <div className="product-card">Item 1</div>
 *     <div className="product-card">Item 2</div>
 *   </div>
 * );
 * ```
 */
export const useScrollAnimation = (options: ScrollAnimationOptions = {}) => {
  const {
    selector = '.animate-item',
    start = 'top 85%',
    initialY = 50,
    initialOpacity = 0,
    duration = 0.8,
    stagger = 0.2,
    ease = 'power2.out',
    once = true,
    customAnimation,
  } = options;

  const container = useRef<HTMLDivElement>(null);

  useGSAP(
    () => {
      if (!container.current) return;

      // 使用自定义动画配置或默认配置
      const fromVars = customAnimation?.from || {
        y: initialY,
        opacity: initialOpacity,
      };

      const toVars = customAnimation?.to || {
        y: 0,
        opacity: 1,
        duration,
        stagger,
        ease,
      };

      // 设置初始状态
      gsap.set(selector, fromVars);

      // 创建滚动触发器
      ScrollTrigger.create({
        trigger: container.current,
        start,
        once,
        onEnter: () => gsap.to(selector, toVars),
      });
    },
    { scope: container } // 限制GSAP只查找container内部元素
  );

  return container;
};

/**
 * 预设的动画配置
 */
export const animationPresets = {
  /** 从下方淡入 */
  fadeInUp: {
    selector: '.animate-item',
    initialY: 50,
    initialOpacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: 'power2.out',
  },
  /** 从上方淡入 */
  fadeInDown: {
    selector: '.animate-item',
    initialY: -50,
    initialOpacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: 'power2.out',
  },
  /** 从左侧淡入 */
  fadeInLeft: {
    customAnimation: {
      from: { x: -50, opacity: 0 },
      to: { x: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'power2.out' },
    },
  },
  /** 从右侧淡入 */
  fadeInRight: {
    customAnimation: {
      from: { x: 50, opacity: 0 },
      to: { x: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'power2.out' },
    },
  },
  /** 缩放淡入 */
  fadeInScale: {
    customAnimation: {
      from: { scale: 0.8, opacity: 0 },
      to: { scale: 1, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'back.out(1.7)' },
    },
  },
  /** 快速淡入 */
  fadeInFast: {
    selector: '.animate-item',
    initialY: 30,
    initialOpacity: 0,
    duration: 0.4,
    stagger: 0.1,
    ease: 'power2.out',
  },
  /** 慢速淡入 */
  fadeInSlow: {
    selector: '.animate-item',
    initialY: 80,
    initialOpacity: 0,
    duration: 1.2,
    stagger: 0.3,
    ease: 'power2.out',
  },
} as const;

export default useScrollAnimation;
