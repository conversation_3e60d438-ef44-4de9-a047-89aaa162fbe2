import { Button, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { createStyles } from 'antd-style';
import React, { memo, useState } from 'react';
import useContact from '../hooks/useContact';
import { ContactInfo } from '../services';

const useStyles = createStyles(({ css, prefixCls }) => ({
  contactButton: css`
    padding: 12px 24px;
    border-radius: 4px;
    background: #4285f4;
    font-size: 16px;
    cursor: pointer;
    margin-right: 15px;
    border: none;
    color: white;
    transition: all 0.3s;
    font-weight: 500;
    &:hover {
      background: #3367d6;
    }
  `,
  input: css`
    &.${prefixCls}-input-outlined {
      background: #f9f9f9;
      border-radius: 4px;
    }
  `,
  select: css`
    &.${prefixCls}-select-outlined {
      .ant-select-selector {
        background: #f9f9f9;
        border-radius: 4px;
      }
    }
  `,
  button: css`
    &.${prefixCls}-btn-primary {
      background: #4285f4;
      border-radius: 4px;
    }
  `,
  title: css`
    text-align: center;
    font-weight: 600;
    color: #4285f4;
  `,
}));

const ContactForm: React.FC<{ solutionId: number }> = ({ solutionId }) => {
  const { styles } = useStyles();
  const [form] = Form.useForm<ContactInfo>();
  const [open, setOpen] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  const { runAsync, loading } = useContact();

  const onCreate = (values: ContactInfo) => {
    runAsync({ ...values, solutionId })
      .then(() => {
        messageApi.success('提交成功');
      })
      .catch((e) => {
        messageApi.error('提交失败');
      })
      .finally(() => {
        setOpen(false);
      });
  };

  return (
    <>
      <div className={styles.contactButton} onClick={() => setOpen(true)}>
        联系我们
      </div>
      <Modal
        open={open}
        title={<h2 className={styles.title}>联系我们</h2>}
        width={800}
        centered
        footer={null}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        onCancel={() => setOpen(false)}
        destroyOnHidden
        maskClosable={false}
        modalRender={(dom) => (
          <Form
            layout="vertical"
            form={form}
            name="form_in_modal"
            initialValues={{
              type: '1',
            }}
            clearOnDestroy
            onFinish={(values) => onCreate(values)}
          >
            {dom}
          </Form>
        )}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="姓名"
              name="name"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input size="large" className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="职位" name="job">
              <Input size="large" className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="公司名称"
              name="company"
              rules={[{ required: true, message: '请输入公司名称' }]}
            >
              <Input size="large" className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入正确的邮箱' },
              ]}
            >
              <Input size="large" className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="电话"
              name="phoneNumber"
              rules={[
                { required: true, message: '请输入电话' },
                {
                  pattern: /(?:^1[3456789]|^9[28])\d{9}$/,
                  message: '请输入正确的电话',
                },
              ]}
            >
              <Input size="large" className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="咨询类型" name="type">
              <Select
                size="large"
                className={styles.select}
                options={[
                  {
                    label: '产品和服务详情',
                    value: '1',
                  },
                  {
                    label: '报价咨询',
                    value: '2',
                  },
                  {
                    label: '其他',
                    value: '3',
                  },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="咨询内容" name="remark">
              <Input.TextArea rows={4} className={styles.input} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Button
              size="large"
              className={styles.button}
              block
              loading={loading}
              type="primary"
              htmlType="submit"
            >
              提交
            </Button>
          </Col>
        </Row>
      </Modal>
      {contextHolder}
    </>
  );
};

export default memo(ContactForm);
