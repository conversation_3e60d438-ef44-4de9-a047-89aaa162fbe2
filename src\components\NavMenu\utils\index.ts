import { NavMenuItem } from '../index';

export function findKeyPath(
  menuItems: NavMenuItem[],
  targetKey: string,
): string[] {
  // 使用栈结构实现非递归深度优先搜索
  const stack: { node: NavMenuItem; path: string[] }[] = menuItems.map(
    (node) => ({ node, path: [] }),
  );

  while (stack.length > 0) {
    const { node, path } = stack.pop()!;
    const currentPath = [...path, node.key];

    if (node.key === targetKey) {
      return currentPath;
    }

    // 子节点按先进后出顺序入栈保持DFS特性
    if (node.children) {
      stack.push(
        ...node.children
          .slice()
          .reverse()
          .map((child) => ({
            node: child,
            path: currentPath,
          })),
      );
    }
  }

  return [];
}
