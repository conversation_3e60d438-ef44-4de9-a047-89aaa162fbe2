import { createStyles } from 'antd-style';
import { memo, PropsWithChildren } from 'react';

interface StandardFormRowProps extends PropsWithChildren {
  title: string | null;
}

const useStyles = createStyles(({ css }) => ({
  container: css`
    border-bottom: 1px dashed #eee;
    padding-bottom: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    &:last-child {
      border: none;
      padding-bottom: 0;
      margin-bottom: 0;
    }
  `,
  label: css`
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    margin-right: 24px;
    flex: 0 0 auto;
    text-align: right;
    & > span {
      display: inline-block;
      height: 32px;
      line-height: 32px;
      &:after {
        content: '：';
      }
    }
  `,
  content: css``,
}));
const StandardFormRow: React.FC<StandardFormRowProps> = ({
  title,
  children,
}) => {
  const { styles } = useStyles();
  return (
    <div className={styles.container}>
      {title ? (
        <div className={styles.label}>
          <span>{title}</span>
        </div>
      ) : null}
      <div className={styles.content}>{children}</div>
    </div>
  );
};

export default memo(StandardFormRow);
