import useObjectState from '@/hooks/useObjectState';
import { SearchParams } from '@/pages/SearchPage/services';
import { useRequest } from 'ahooks';

const PAGE_SIZE = 20;

const useSearch = <T>(queryFnc: (params: SearchParams) => Promise<T>) => {
  const [searchValues, updateState] = useObjectState<SearchParams>({
    Keywords: undefined,
    Page: 0,
    PageSize: PAGE_SIZE,
    DeliveryMethod: undefined,
    CategoryId: undefined,
    Supplier: '',
  });
  const { run, data, loading } = useRequest(() => queryFnc(searchValues), {
    refreshDeps: [
      searchValues.Page,
      searchValues.PageSize,
      searchValues.DeliveryMethod,
      searchValues.CategoryId,
    ],
    debounceWait: 300,
  });

  return {
    searchValues,
    updateState,
    runSearch: run,
    data,
    loading,
  };
};

export default useSearch;
