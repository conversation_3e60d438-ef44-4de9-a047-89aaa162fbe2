import { forgetpassword } from '@/services/user';
import { PageContainer } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Form, Input } from 'antd';
import { useState } from 'react';
import style from './index.less';
import success_img from './success.png';

export default () => {
  const [form] = Form.useForm();

  const [isregsuccess, setIsregsuccess] = useState<boolean>(false);

  const onFinish = async (values: any) => {
    console.log('Success:', values);
    const { email } = values;
    try {
      const res = await forgetpassword(email);
      console.log(res);
      setIsregsuccess(true);
    } catch ({
      response: {
        data: { Error },
      },
    }: any) {
      // message.error(Error);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className={style.container}>
      <div className={style.bg}></div>
      <PageContainer ghost className={style.regpan}>
        {isregsuccess ? (
          <Form
            style={{
              padding: '30px',
              borderRadius: '10px',
              background: 'rgba(255, 255, 255, 1)',
              boxShadow:
                '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
            }}
            name="ok"
            // wrapperCol={{ span: 16 }}
            autoComplete="off"
          >
            <Form.Item className={style.center}>
              <img src={success_img} />
            </Form.Item>
            <Form.Item className={style.center}>邮件发送成功</Form.Item>
            <Form.Item>
              <Button
                className={style.reg}
                type="primary"
                htmlType="button"
                onClick={() => {
                  history.push('/');
                }}
              >
                返回首页
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <Form
            style={{
              padding: '30px',
              borderRadius: '10px',
              background: 'rgba(255, 255, 255, 1)',
              boxShadow:
                '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
            }}
            form={form}
            name="basic"
            // wrapperCol={{ span: 16 }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item>
              <h2>忘记密码</h2>
            </Form.Item>

            <Form.Item
              name="email"
              rules={[{ required: true, message: '请输入邮箱' }]}
            >
              <Input placeholder="请输入您的邮箱" />
            </Form.Item>

            <Form.Item>
              <Button className={style.reg} type="primary" htmlType="submit">
                邮件通知
              </Button>
            </Form.Item>
          </Form>
        )}
      </PageContainer>
    </div>
  );
};
