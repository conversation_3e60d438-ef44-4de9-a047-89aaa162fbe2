import { getCartCount } from '@/services/api';
import useUserStore from '@/store/user';
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
// 定义一个useCartCount函数，用于获取购物车数量
const useCartCount = () => {
  const { user, token } = useUserStore();
  const [cartRegion, setCartRegion] = useState<number>(user?.Regions?.[0] ?? 1);
  // 使用useRequest函数获取购物车数量，传入getCartCount函数和ready属性，判断是否需要刷新
  const { run, data, refresh } = useRequest(() => getCartCount(cartRegion), {
    manual: true,
    ready: !!token,
  });

  useEffect(() => {
    refresh();
  }, [cartRegion]);
  const refreshCartCount = () => {
    refresh();
  };
  // 返回购物车数量和刷新函数
  return {
    count: data?.Count || 0,
    refreshCartCount,
    setCartRegion,
    cartRegion,
    run,
  };
};

export default useCartCount;
