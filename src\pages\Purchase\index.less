.purchaaseTablaBorder {
  border-radius: 8px;
  background: #fff;
  padding: 16px;
  border: 1px solid rgba(229, 229, 229, 100%);
  margin-top: 16px;
}

.payAmountBox {
  align-items: flex-end; /* 底部对齐 */
  justify-content: center; /* 水平居中 */
  line-height: 80px;
  text-align: left;
}

.payAmount {
  font-size: 24px;
  font-weight: 700;
  color: rgba(247, 108, 89, 100%);
}

.payAmountUnit {
  font-size: 24px;
  font-weight: 500;
  color: rgba(247, 108, 89, 100%);
}

.payAmountTitle {
  font-size: 18px;
  color: rgba(107, 107, 107, 100%);
}

.accountInformation {
  font-size: 16px;
  font-weight: 400;
  line-height: 35px;
  color: rgba(110, 110, 110, 100%);
  text-align: left;
}

.payFrame {
  display: flex;
}

.purchaseTitle {
  font-size: 18px;
  font-weight: 600;
  color: rgba(56, 56, 56, 100%);
  text-align: left;
}

.purchaseTableTitle {
  font-size: 16px;
  font-weight: 500;
  line-height: 36px;
  color: rgba(56, 56, 56, 100%);
}

.purchaaseSquare {
  display: inline-block;
  background: #114bedff;
  border-left: 1px solid #114bedff;
  bottom: 0;
  height: 17px;
  position: absolute;
  right: 0;
  width: 17px;
  border-radius: 5px 0 0;
}

.purchaaseImg {
  width: 12px;
  height: 12px;
  position: relative;
  left: 0;
  top: -10px;
}

.paymentMethod {
  color: #808080;
  height: 38px;
  line-height: 38px;
  margin-right: 10px;
  overflow: hidden;
  position: relative;
  text-align: center;
  width: 120px;
  cursor: pointer;
  border-radius: 8px;
  border: 1px solid #808080;
  display: inline-block;
}

.purchaaseSelected {
  color: #114bed !important;
  border: 1px solid #114bed !important;
}

.paymentMethod:hover {
  color: #114bed;
  border: 1px solid #114bed;
}

.specialBox {
  padding-top: 32px;
}

.specialTitle {
  line-height: 30px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(56, 56, 56, 100%);
  margin-right: 16px;
}

.specialInfo {
  line-height: 30px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(128, 128, 128, 100%);
  margin-left: 16px;
}

.alertInfo {
  font-size: 14px;
  color: #409eff;
  margin-top: 12px;
  padding: 10px;
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  max-width: fit-content;
}

.submitButton {
  width: 120px;
  height: 46px;
  opacity: 1;
  border-radius: 8px;
  background: linear-gradient(224.07deg, #2480e3ff 0%, #2b24e3ff 100%);
  display: inline-block;
  color: #fff;
  line-height: 46px;
  cursor: pointer;

  &:hover {
    color: #fff !important;
    background: linear-gradient(
      224.07deg,
      rgb(68, 134, 205) 0%,
      rgb(13, 5, 247) 80%
    ) !important;
  }
}

.btbg {
  background: linear-gradient(224.07deg, #2480e3ff 0%, #2b24e3ff 100%);
}

.payBox {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 80px;
  background: #fff;
  line-height: 80px;
  padding-left: 32px;
  padding-right: 32px;
  border-top: 1px solid rgba(229, 229, 229, 100%);
}

.payInstallmentBox {
  position: absolute;
  padding-top: 16px;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 180px;
  background: #fff;
  line-height: 30px;
  padding-left: 32px;
  padding-right: 32px;
  border-top: 1px solid rgba(229, 229, 229, 100%);
}

.payInstallmentAmountBox {
  align-items: flex-end; /* 底部对齐 */
  justify-content: center; /* 水平居中 */
  line-height: 36px;
  text-align: left;
}

.payInstallAmount {
  font-size: 20px;
  font-weight: 700;
  color: rgba(247, 108, 89, 100%);
}

.disabledSubmitButton {
  text-align: center;
  width: 120px;
  height: 46px;
  opacity: 1;
  border-radius: 8px;
  background: linear-gradient(
    224.07deg,
    rgb(177, 181, 185) 0%,
    rgb(165, 164, 172) 100%
  );
  display: inline-block;
  color: #fff;
  line-height: 46px;
  font-size: 18px;
  cursor: pointer;

  &:hover {
    color: #fff !important;
    background: linear-gradient(
      224.07deg,
      rgb(68, 134, 205) 0%,
      rgb(13, 5, 247) 80%
    ) !important;
  }
}
