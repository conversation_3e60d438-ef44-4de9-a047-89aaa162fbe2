import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import { ReactComponent as UnpaidSVG } from '@/assets/unpaid.svg';
import {
  getOrderDetail,
  getOrders,
  putCancelOrder,
  putOrderSingleComplete,
  putPayments,
} from '@/services/order/PurchaseController';
import { DeliverStatus, OrderStatus, PaymentStatus } from '@/types';
import { formatWithThousandSeparator, transform } from '@/utils/currencyUtil';
import { formatDate, formatDateTime } from '@/utils/format';
import { openUrl } from '@/utils/urlUtil';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DownCircleOutlined,
  InfoCircleOutlined,
  UpCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from '@umijs/max';
import type { PaginationProps, TableProps } from 'antd';
import {
  But<PERSON>,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
  notification,
} from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
const Protocol = React.lazy(() => import('@/components/Protocol'));
const OfflinePayment = React.lazy(() => import('@/components/OfflinePayment'));
const RequestInvoice = React.lazy(() => import('@/components/RequestInvoice'));
const { RangePicker } = DatePicker;
const useStyle = createStyles(({ prefixCls, css, token }) => {
  return {
    customTable: css`
      ${prefixCls}-table {
        ${prefixCls}-table-container {
          ${prefixCls}-table-body,
          ${prefixCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `,
    specialtTag: css`
      margin-left: 6px;
      display: inline-block;
      width: 48px;
      height: 24px;
      opacity: 1;
      line-height: 22px;
      border-radius: 4px;
      border-radius: 4px;
      color: #ffffff;
      background: rgba(255, 128, 99, 1);
      border: 1px solid rgba(255, 128, 99, 1);
      text-align: center;
      font-size: 12px;
      font-weight: 400;
    `,
    specialtRejectTag: css`
      margin-left: 6px;
      display: inline-block;
      width: 72px;
      height: 24px;
      opacity: 1;
      line-height: 22px;
      border-radius: 4px;
      border-radius: 4px;
      color: #808080;
      background: #ffffff;
      border: 1px solid #808080;
      text-align: center;
      font-size: 12px;
      font-weight: 400;
    `,
    specialAmount: css`
      font-size: 14px;
      font-weight: 400;
      letter-spacing: 0px;
      text-decoration-line: line-through;
      color: rgba(166, 166, 166, 1);
      text-align: left;
    `,
    goDetail: css`
      margin-left: 4px;
      color: ${token.colorPrimary};
      cursor: pointer;
    `,
  };
});
const Orders = () => {
  const { styles } = useStyle();
  useEffect(() => {
    getOrderList(1);
  }, []);
  const navigate = useNavigate();
  const [searchForm] = Form.useForm();
  const pageSize = 10;
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [total, setTotal] = useState<number>(1);
  const protocolRef = useRef<any>(null);
  const offlinePaymentRef = useRef<any>(null);
  const requestInvoiceRef = useRef<any>(null);
  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [dataList, setDataList] = useState<any[]>([]);
  // const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [api, contextHolder] = notification.useNotification();
  const [selectOrder, setSelectOrder] = useState<any>(null);
  const [selectSolution, setSelectSolution] = useState<any>(null);
  const [selectType, setSelectType] = useState<any>(null);
  const [selectSolutionType, setSelectSolutionType] = useState<any>(null);
  useEffect(() => {
    if (selectType && selectOrder) {
      if (selectType === 'detail') {
        goOrderDetail(selectOrder.CustomOrderNumber);
      }
    }
  }, [selectOrder, selectType]);
  useEffect(() => {
    if (selectSolutionType && selectSolution) {
      if (selectSolutionType === 'detail') {
        goSolutionDetail(selectSolution.SolutionGlobalId);
      }
    }
  }, [selectSolution, selectSolutionType]);
  const orderItemColumns: TableProps['columns'] = [
    {
      title: '商品图片',
      dataIndex: 'ImageUrl',
      align: 'left',
      key: 'ImageUrl',
      ellipsis: true,
      width: 100,
      render: (item: any) => (
        <img
          loading="lazy"
          style={{ height: 48, width: 48, objectFit: 'contain' }}
          src={item}
        ></img>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'SolutionName',
      align: 'left',
      key: 'SolutionName',
      ellipsis: true,
      minWidth: 260,
    },
    {
      title: '单价',
      dataIndex: 'UnitPrice',
      align: 'left',
      key: 'UnitPrice',
      ellipsis: true,
      width: 100,
      render: (item: any, data: any) => (
        <>
          {transform(data.Currency) +
            formatWithThousandSeparator(data.UnitPrice)}
        </>
      ),
    },
    {
      title: '数量',
      dataIndex: 'Quantity',
      align: 'left',
      key: 'Quantity',
      ellipsis: true,
      width: 100,
    },
    {
      title: '小计',
      dataIndex: 'Total',
      align: 'left',
      key: 'Total',
      ellipsis: true,
      width: 100,
      render: (item: any, data: any) => (
        <>
          {transform(data.Currency) + formatWithThousandSeparator(data.Total)}
        </>
      ),
    },
    {
      title: '交付状态',
      dataIndex: 'DeliveryStatus',
      align: 'left',
      key: 'DeliveryStatus',
      ellipsis: true,
      width: 100,
      render: (value: any, data: any) => (
        <span style={getDeliveryStatusStyle(data.DeliveryStatusId)}>
          {value}
        </span>
      ),
    },
    {
      title: '操作',
      dataIndex: 'SolutionId',
      align: 'left',
      key: 'SolutionId',
      ellipsis: true,
      fixed: 'right',
      render: (status: any, data: any) => (
        <Space>
          {data.DeliveryStatusId === 3 && data.DeliveryMethodId !== 10 && (
            <Popconfirm
              title="是否确认交付?"
              onConfirm={() => deliveryOrderItemConfirm(data.OrderItemGlobalId)}
              okButtonProps={{
                size: 'small',
              }}
              cancelButtonProps={{
                size: 'small',
              }}
            >
              <Button
                color="primary"
                variant="outlined"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                确认交付
              </Button>
            </Popconfirm>
          )}
          &nbsp;
        </Space>
      ),
    },
  ];
  const getOrderPayDiableAlertMessage = (data: any) => {
    if (data.PaymentMethodId === 3) {
      if (data.PaymentStatusId === 0) {
        return '确认合同';
      }
      if (data.PaymentStatusId === 1) {
        return '订单已支付';
      }

      if (data.PaymentStatusId === 5) {
        return '账期付款审核中';
      }

      if (data.PaymentStatusId === 6) {
        return '订单已取消';
      }

      if (data.PaymentStatusId === 9 || data.PaymentStatusId === 11) {
        return '前往账单模块付款和查看付款状态';
      }

      if (data.PaymentStatusId === 10) {
        return '线下支付审核中';
      }
    }

    if (data.OrderStatusId === 4) {
      return '订单已取消';
    } else if (!data.HasContract) {
      return '未确认合同';
    } else if (!data.DisplayPaymentButton && data.OrderStatusId === 2) {
      return '订单交付中';
    } else if (!data.DisplayPaymentButton && data.OrderStatusId === 3) {
      return '订单已完成';
    } else if (
      (data.Locked || !data.DisplayPaymentButton) &&
      data.PaymentStatusId === 1
    ) {
      return '订单已支付';
    } else if (
      (data.Locked || !data.DisplayPaymentButton) &&
      data.PaymentStatusId === 2
    ) {
      return '线下支付审核中';
    } else if (
      (data.Locked || !data.DisplayPaymentButton) &&
      data.PaymentStatusId === 4
    ) {
      return '特价审核中';
    } else if (data.Locked) {
      return '订单处理中';
    }
    return null;
  };
  const requestInvoiceShow = (customOrderNumber: any) => {
    if (requestInvoiceRef.current) {
      setTableLoading(true);
      getOrderDetail(customOrderNumber).then((res) => {
        setTableLoading(false);
        requestInvoiceRef.current.modalHandleShow(res);
      });
    }
  };
  const getOrderProtocolDiableAlertMessage = (data: any) => {
    if (data.Locked && data.PaymentStatusId === 2) {
      return '线下支付审核中';
    } else if (data.Locked && data.PaymentStatusId === 4) {
      return '特价审核中';
    } else if (data.Locked && data.PaymentStatusId === 5) {
      return '账期付款审核中';
    } else if (data.Locked && data.PaymentStatusId === 10) {
      return '账期线下付款审核中';
    } else if (data.OrderStatusId === 4) {
      return '订单已取消';
    } else if (data.Locked) {
      return '订单处理中';
    }
    return null;
  };
  const getOrderCancelDiableAlertMessage = (data: any) => {
    if (data.OrderStatusId === 2) {
      return '订单交付中';
    } else if (data.OrderStatusId === 3) {
      return '订单已完成';
    } else if (data.OrderStatusId === 4) {
      return '订单已取消';
    } else if (data.PaymentStatusId === 1) {
      return '订单已支付';
    } else if (data.PaymentStatusId === 2) {
      return '线下支付审核中';
    } else if (data.PaymentStatusId === 4) {
      return '特价审核中';
    } else if (data.PaymentStatusId === 5) {
      return '账期付款审核中';
    } else if (data.PaymentStatusId === 9) {
      return '订单已支付';
    } else if (data.PaymentStatusId === 10) {
      return '账期线下付款审核中';
    } else if (data.PaymentStatusId === 12) {
      return '订单已支付';
    } else if (data.PaymentStatusId === 13) {
      return '订单已支付';
    } else if (data.PaymentStatusId === 14) {
      return '订单已支付';
    } else if (data.Locked) {
      return '订单处理中';
    }
    return null;
  };
  const columns: TableProps['columns'] = [
    {
      title: '订单编号',
      dataIndex: 'CustomOrderNumber',
      align: 'left',
      key: 'CustomOrderNumber',
      fixed: 'left',
      ellipsis: true,
      width: 160,
      render: (value: any, item: any) => (
        <Tooltip
          color="white"
          title={() => (
            <Button
              type="text"
              onClick={(e) => {
                e.stopPropagation();
                navigator.clipboard
                  .writeText(value)
                  .then(() => {
                    api['success']({
                      message: '复制成功',
                      duration: 2,
                    });
                  })
                  .catch((error) => {
                    console.error('Failed to copy text: ', error);
                  });
              }}
            >
              {' '}
              复制
            </Button>
          )}
        >
          <span>{value}</span>
          {item.ChangePrice && item.PaymentStatusId !== 8 && (
            <span className={styles.specialtTag}>特价</span>
          )}
          {item.ChangePrice && item.PaymentStatusId === 8 && (
            <span className={styles.specialtRejectTag}>特价拒绝</span>
          )}
          {item.PaymentMethodId === 3 && item.PaymentStatusId === 6 && (
            <span className={styles.specialtRejectTag}>账期拒绝</span>
          )}
        </Tooltip>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'CustomerCompany',
      align: 'left',
      key: 'CustomerCompany',
      width: 160,
      ellipsis: true,
    },
    {
      title: '下单人员',
      dataIndex: 'BuyUserName',
      align: 'left',
      key: 'BuyUserName',
      ellipsis: true,
      width: 150,
    },
    {
      title: '订单总额',
      dataIndex: 'OrderTotal',
      align: 'left',
      key: 'OrderTotal',
      width: 150,
      render: (item: any, data: any) => (
        <Flex vertical>
          {data.ChangePriceOldOrderTotal !== 0 && (
            <span className={styles.specialAmount} style={{ marginRight: 6 }}>
              {transform(data.Currency) +
                formatWithThousandSeparator(data.ChangePriceOldOrderTotal)}
            </span>
          )}

          <span>
            {transform(data.Currency) +
              formatWithThousandSeparator(data.OrderTotal)}
          </span>
          <span
            className={styles.goDetail}
            onClick={() => {
              goOrderDetail(data.CustomOrderNumber);
            }}
          >
            查看详情
          </span>
        </Flex>
      ),
    },
    {
      title: '创建日期',
      dataIndex: 'CreatedOnUtc',
      align: 'left',
      key: 'CreatedOnUtc',
      width: 160,
      render: (value) => {
        return value ? formatDateTime(new Date(value), true) : '';
      },
    },
    {
      title: '订单状态',
      align: 'left',
      dataIndex: 'Id',
      key: 'Id',
      width: 160,
      render: (item: any, data: any) => (
        <div style={getOrderStatusStyle(data.OrderStatusId)}>
          {getOrderStatusIcon(data.OrderStatusId)}&nbsp;
          {getOrderStatusName(data)}
        </div>
      ),
    },
    {
      title: '付款状态',
      align: 'left',
      dataIndex: 'Id',
      key: 'Id',
      width: 160,
      render: (item: any, data: any) => (
        <div style={getPaymentStatusStyle(data.PaymentStatusId)}>
          {getPaymentStatusIcon(data.PaymentStatusId)}&nbsp;
          {getPaymentStatusName(data)}
        </div>
      ),
    },
    {
      title: '操作',
      align: 'left',
      dataIndex: 'PaymentStatusId',
      key: 'PaymentStatusId',
      fixed: 'right',
      render: (status: any, data: any) => (
        <Space>
          {data.HasContract && (
            <Button
              color="primary"
              variant="outlined"
              style={{ width: 80 }}
              disabled={data.OrderStatusId === 4}
              onClick={(e) => {
                e.stopPropagation();
                protocolClick(e, data);
              }}
            >
              查看合同
            </Button>
          )}
          {!data.HasContract && (
            <Tooltip title={getOrderProtocolDiableAlertMessage(data)}>
              <Button
                color="primary"
                style={{ width: 80 }}
                variant="outlined"
                disabled={data.Locked || data.OrderStatusId === 4}
                onClick={(e) => {
                  e.stopPropagation();
                  protocolConfirmClick(e, data);
                }}
              >
                确认合同
              </Button>
            </Tooltip>
          )}
          <Tooltip title={getOrderPayDiableAlertMessage(data)}>
            <Button
              color="primary"
              variant="outlined"
              style={{ width: 80 }}
              disabled={
                !data.HasContract ||
                !data.DisplayPaymentButton ||
                data.Locked ||
                data.PaymentMethodId === 3
              }
              onClick={(e) => {
                e.stopPropagation();
                payClick(data);
              }}
            >
              {data.DeliveryMethodId === 10
                ? data.PaymentPlanId === 0
                  ? '确认付款'
                  : data?.Installments[0].InstallmentStatusId === 1
                  ? '确认尾款'
                  : '确认首款'
                : '确认付款'}
            </Button>
          </Tooltip>
          {(data.PaymentStatusId === 1 || data.PaymentStatusId === 9) &&
            (data.InvoiceStatus === 0 ||
              (data.InvoiceStatus === 3 && data.InvoiceRejectRepeat)) && (
              <Button
                color="primary"
                variant="outlined"
                style={{ width: 80 }}
                onClick={(e) => {
                  requestInvoiceShow(data.CustomOrderNumber);
                }}
              >
                申请开票
              </Button>
            )}
          {(data.PaymentStatusId === 1 || data.PaymentStatusId === 9) &&
            (data.InvoiceStatus === 1 ||
              data.InvoiceStatus === 2 ||
              (data.InvoiceStatus === 3 && !data.InvoiceRejectRepeat)) && (
              <Button
                color="primary"
                variant="outlined"
                style={{ width: 80 }}
                onClick={(e) => {
                  navigate(
                    {
                      pathname: '/enterprise/invoice/detail',
                    },
                    {
                      state: {
                        CustomOrderNumber: data.CustomOrderNumber,
                        BackType: 1,
                      },
                    },
                  );
                }}
              >
                发票详情
              </Button>
            )}
          {((data.PaymentStatusId !== 1 && data.PaymentStatusId !== 9) ||
            (data.InvoiceStatus !== 1 &&
              data.InvoiceStatus !== 2 &&
              data.InvoiceStatus !== 3 &&
              data.InvoiceStatus !== 0)) && (
            <Popconfirm
              title="确认取消订单吗?"
              onConfirm={() => orderCancelClick(data)}
              okButtonProps={{
                size: 'small',
              }}
              cancelButtonProps={{
                size: 'small',
              }}
            >
              <Tooltip title={getOrderCancelDiableAlertMessage(data)}>
                <Button
                  color="danger"
                  style={{ width: 80 }}
                  variant="outlined"
                  disabled={
                    data.PaymentStatusId === 1 ||
                    data.PaymentStatusId === 2 ||
                    data.PaymentStatusId === 9 ||
                    data.PaymentStatusId === 10 ||
                    data.PaymentStatusId === 12 ||
                    data.PaymentStatusId === 13 ||
                    data.PaymentStatusId === 14 ||
                    data.PaymentStatusId === 4 ||
                    data.PaymentStatusId === 5 ||
                    data.Locked ||
                    data.OrderStatusId === 4 ||
                    data.OrderStatusId === 3 ||
                    data.OrderStatusId === 2
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  取消
                </Button>
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];
  const deliveryOrderItemConfirm = (orderItemGlobalId: string) => {
    putOrderSingleComplete(orderItemGlobalId).then((res) => {
      getOrderList();
      api['success']({
        message: '确认交付成功',
        duration: 2,
      });
    });
  };
  const getOrderStatusIcon = (orderStatusId: number) => {
    switch (orderStatusId) {
      case 0:
        return <InfoCircleOutlined />;
      case 1:
        return <ClockCircleOutlined />;
      case 2:
        return <ClockCircleOutlined />;
      case 3:
        return <CheckCircleOutlined />;
      case 4:
        return <CloseCircleOutlined />;
    }
  };
  const getPaymentStatusIcon = (paymentStatusId: number) => {
    switch (paymentStatusId) {
      case 1:
        return <CheckCircleOutlined />;
      default:
        return (
          <UnpaidSVG
            viewBox="0 -6 16 22"
            style={{ width: '16', height: '16' }}
          />
        );
    }
  };
  const getPaymentStatusStyle = (paymentStatusId: number) => {
    switch (paymentStatusId) {
      case 0:
        return { color: '#FF8D1A' };
      case 1:
        return { color: '#43CF7C' };
      case 6:
        return { color: '#FF8D1A' };
      case 8:
        return { color: '#FF8D1A' };
      case 3:
        return { color: '#FF8D1A' };
      case 7:
        return { color: '#FF8D1A' };
      default:
        return { color: '#FF8D1A' };
    }
  };
  const getOrderStatusStyle = (orderStatusId: number) => {
    switch (orderStatusId) {
      case 0:
        return { color: '#FFC300' };
      case 1:
        return { color: '#FF8D1A' };
      case 2:
        return { color: '#FF8D1A' };
      case 3:
        return { color: '#43CF7C' };
      case 4:
        return { color: '#FF8063' };
      case 5:
        return { color: '#FF8063' };
    }
  };
  const getDeliveryStatusStyle = (deliveryStatusId: number) => {
    switch (deliveryStatusId) {
      case 0:
        return { color: '#808080' };
      case 1:
        return { color: '#808080' };
      case 2:
        return { color: '#43CF7C' };
      case 3:
        return { color: '#43CF7C' };
      case 4:
        return { color: '#43CF7C' };
      case 5:
        return { color: '#43CF7C' };
    }
  };
  const getOrderList = (page?: number) => {
    let values = searchForm.getFieldsValue();
    console.log('values', values);

    let searchPar = { ...values };
    if (searchPar.PaymentStatusId) {
      searchPar.PaymentStatusId = JSON.parse(searchPar.PaymentStatusId);
    }
    if (searchPar.OrderStatusId) {
      searchPar.OrderStatusId = JSON.parse(searchPar.OrderStatusId);
    }
    searchPar.RangePicker = undefined;
    if (values.RangePicker) {
      searchPar.StartDate = values.RangePicker[0].format('YYYY-MM-DD');
      searchPar.EndDate =
        values.RangePicker[1].format('YYYY-MM-DD') + ' 23:59:59.9999';
    }
    setTableLoading(true);
    setPageIndex(page ?? 1);
    getOrders({
      PageIndex: page ?? 1,
      PageSize: pageSize,
      ...searchPar,
    }).then((res) => {
      setTotal(res.Total);
      setDataList(res.Data);
      // setExpandedRowKeys(res.Data.map((item: any) => item.CustomOrderNumber));
      setTableLoading(false);
    });
  };
  const protocolClick = (e: any, item: any) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (protocolRef.current) {
      protocolRef.current.modalHandleShow(2, item.CustomOrderNumber);
    }
  };
  const protocolConfirmClick = (e: any, item: any) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (protocolRef.current) {
      protocolRef.current.modalHandleShow(1, item.CustomOrderNumber);
    }
  };
  const paymentClick = (paymentNumber: string, data: any) => {
    if (data.HasContract) {
      onlinePayment(paymentNumber);
    } else {
      if (protocolRef.current) {
        protocolRef.current.modalHandleShow(1, data.CustomOrderNumber);
      }
    }
  };
  const onlinePayment = (paymentNumber: string) => {
    const returnUrl = window.location.href;
    putPayments(paymentNumber, {
      returnUrl: returnUrl,
    }).then(
      (res) => {
        openUrl(res.PaymentUrl);
      },
      () => {
        api['error']({
          message: '付款失败',
          duration: 2,
        });
      },
    );
  };
  const payClick = (data: any) => {
    switch (data.PaymentMethodId) {
      case 1:
        paymentClick(data.PaymentNumber, data);
        break;
      case 2:
        offlinePaymentClick(data.PaymentNumber, data);
        break;
      case 3:
        offlinePaymentClick(data.PaymentNumber, data);
        break;
    }
  };

  const offlinePaymentClick = (paymentNumber: number, item: any) => {
    if (item.HasContract) {
      if (offlinePaymentRef.current) {
        let installmentNumber = '';
        if (item.DeliveryMethodId === 10 && item?.Installments?.length > 0) {
          if (item?.Installments[0]?.InstallmentStatusId === 1) {
            installmentNumber = item?.Installments[1]?.InstallmentNumber;
          } else {
            installmentNumber = item?.Installments[0]?.InstallmentNumber;
          }
        }
        offlinePaymentRef.current.modalHandleShow(
          'pay',
          {
            paymentNumber: paymentNumber,
            customOrderNumber: item.CustomOrderNumber,
            installmentNumber: installmentNumber,
          },
          true,
        );
      }
    } else {
      if (protocolRef.current) {
        protocolRef.current.modalHandleShow(1, item.CustomOrderNumber);
      }
    }
  };
  // const instalmentPaymentClick = (item: any) => {
  //   if (confirmPeriodRef.current) {
  //     confirmPeriodRef.current.modalHandleShow(item.CustomOrderNumber);
  //   }
  // };
  const orderCancelClick = (data: any) => {
    putCancelOrder(data.CustomOrderNumber).then(() => {
      getOrderList(1);
    });
  };
  const onPageChange: PaginationProps['onChange'] = (page) => {
    getOrderList(page);
  };
  const getPaymentStatusName = (data: any) => {
    let displayValue = '';
    const orders = PaymentStatus.filter(
      (item) => item.id === data.PaymentStatusId,
    );
    displayValue = orders && orders.length > 0 ? orders[0].value : '';
    return displayValue;
  };
  const getOrderStatusName = (data: any) => {
    let displayValue = '';
    const orders = OrderStatus.filter((item) => item.id === data.OrderStatusId);
    displayValue = orders && orders.length > 0 ? orders[0].value : '';

    return displayValue;
  };
  const expandedComponents = {
    header: {
      cell: (props: any) => {
        const { style, children, ...restProps } = props;
        return (
          <th
            {...restProps}
            style={{ height: 38, padding: '0 0 0 16px', ...style }}
          >
            {children}
          </th>
        );
      },
    },
    body: {
      cell: (props: any) => {
        const { style, children, ...restProps } = props;
        return (
          <td
            onClick={() => {
              if (!children || !children[1]) {
                return;
              }
              if (
                typeof children[1] === 'string' ||
                children[1].type === 'img'
              ) {
                setSelectSolutionType('detail');
              } else {
                setSelectSolutionType('dev');
              }
            }}
            {...restProps}
            style={{ cursor: 'pointer', ...style }}
          >
            {handleCell(children, '商品详情')}
          </td>
        );
      },
    },
  };
  const components = {
    header: {
      cell: (props: any) => {
        const { style, children, ...restProps } = props;
        return (
          <th
            {...restProps}
            style={{ height: 38, padding: '0 0 0 16px', ...style }}
          >
            {children}
          </th>
        );
      },
    },
    body: {
      cell: (props: any) => {
        const { style, children, ...restProps } = props;
        return (
          <td {...restProps} style={{ ...style }}>
            {children}
          </td>
        );
      },
    },
  };

  const handleCell = (value: any, tooltipTitle: string) => {
    if (!value || !value[1]) {
      return '';
    }
    if (typeof value[1] === 'string') {
      return (
        <Tooltip title={tooltipTitle}>
          <span>{value}</span>
        </Tooltip>
      );
    } else if (value[1].type === 'img') {
      return (
        <Tooltip title={tooltipTitle}>
          <span>{value}</span>
        </Tooltip>
      );
    } else {
      return <span>{value}</span>;
    }
  };
  const tableItemClick = (data: any) => {
    setSelectOrder(data);
    //goOrderDetail(data.CustomOrderNumber);
  };
  const goOrderDetail = (customOrderNumber: string) => {
    navigate(
      {
        pathname: '/order/detail',
      },
      {
        state: {
          CustomOrderNumber: customOrderNumber,
          BackType: 1,
        },
      },
    );
  };
  const goSolutionDetail = (solutionGlobalId: string) => {
    const baseUrl = window.location.origin;
    const path = `${baseUrl}/solution/detail/${solutionGlobalId}`;
    window.open(path, '_blank');
  };
  const orderItemExpandedRowRender = (orderList: any[]) => (
    <>
      {!tableLoading && (
        <Table
          style={{
            marginLeft: 160,
            borderRadius: '8px',
            border: '1px solid #d9d9d9',
          }}
          columns={orderItemColumns}
          dataSource={orderList}
          pagination={false}
          rowKey={'SolutionId'}
          components={expandedComponents}
          onRow={(data: any) => {
            return {
              onClick: (event) => {
                setSelectSolution(data);
              },
            };
          }}
        />
      )}
    </>
  );

  return (
    <div>
      {contextHolder}
      <Typography.Title level={5}>订单管理</Typography.Title>
      <Form
        layout={'inline'}
        labelCol={{ flex: '70px' }}
        wrapperCol={{ flex: 'auto' }}
        labelAlign="left"
        form={searchForm}
        style={{
          maxWidth: 'none',
          paddingTop: 16,
          paddingBottom: 16,
        }}
      >
        <Row gutter={[4, 20]}>
          <Col span={6}>
            <Form.Item label="订单编号" name="SearchOrderNumber">
              <Input placeholder="搜索订单编号" suffix={<SearchSVG />}></Input>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="客户名称" name="SearchCustomerCompany">
              <Input placeholder="搜索客户名称" suffix={<SearchSVG />}></Input>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="下单人员" name="BuyUserName">
              <Input placeholder="搜索下单人员" suffix={<SearchSVG />}></Input>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="日期区间" name="RangePicker">
              <RangePicker
                showTime={false}
                format="YYYY-MM-DD"
                maxDate={dayjs(formatDate(new Date()), 'YYYY-MM-DD')}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="订单状态" name="OrderStatusId">
              <Select
                placeholder="选择订单状态"
                allowClear
                options={[
                  { value: '[0]', label: '待处理' },
                  { value: '[1,2]', label: '处理中' },
                  { value: '[3]', label: '已完成' },
                  { value: '[4]', label: '已取消' },
                ]}
              ></Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="付款状态" name="PaymentStatusId">
              <Select
                placeholder="选择付款状态"
                allowClear
                options={[
                  { value: '[0,2,3,4,5,6,7,8]', label: '未付款' },
                  { value: '[1]', label: '已付款' },
                  { value: '[9,10,11]', label: '账期付款' },
                  { value: '[12,13,14]', label: '部分付款' },
                ]}
              ></Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="交付状态" name="DeliveryStatusId">
              <Select
                placeholder="选择交付状态"
                allowClear
                options={DeliverStatus.map((c) => {
                  return { value: c.id, label: c.value };
                })}
              ></Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'left' }}>
              <Button
                type="primary"
                style={{ width: 80 }}
                onClick={() => {
                  getOrderList(1);
                }}
                // icon={<SearchOutlined />}
              >
                搜索
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <Table
        className={styles.customTable}
        loading={tableLoading}
        style={{
          borderRadius: '8px',
          border: '1px solid #d9d9d9',
        }}
        columns={columns}
        dataSource={dataList}
        pagination={false}
        components={components}
        scroll={{ x: 'max-content', y: 65 * 10 }}
        rowKey={'CustomOrderNumber'}
        expandable={{
          expandedRowRender: (record) =>
            orderItemExpandedRowRender(record.OrderItems),
          defaultExpandedRowKeys: ['0'],
          // onExpand: (expanded, record) => {
          //   if (expanded) {
          //     expandedRowKeys.push(record.CustomOrderNumber);
          //     setExpandedRowKeys(expandedRowKeys);
          //   } else {
          //     setExpandedRowKeys(
          //       expandedRowKeys.filter((c) => c !== record.CustomOrderNumber),
          //     );
          //   }
          // },
          // expandedRowKeys: expandedRowKeys,
          expandIcon: ({ expanded, onExpand, record }) =>
            expanded ? (
              <UpCircleOutlined
                style={{ fontSize: 18, color: '#113DED' }}
                onClick={(e) => {
                  e.stopPropagation();
                  onExpand(record, e);
                }}
              />
            ) : (
              <DownCircleOutlined
                style={{ fontSize: 18, color: '#113DED' }}
                onClick={(e) => {
                  e.stopPropagation();
                  onExpand(record, e);
                }}
              />
            ),
          expandIconColumnIndex: 0,
          fixed: 'left',
        }}
        onRow={(data: any) => {
          return {
            onClick: (event) => {
              tableItemClick(data);
            },
          };
        }}
      />
      {total > pageSize && (
        <Pagination
          style={{ marginTop: 16 }}
          align="end"
          current={pageIndex}
          pageSize={pageSize}
          onChange={onPageChange}
          total={total}
          showSizeChanger={false}
        />
      )}
      <React.Suspense>
        <Protocol
          confirmCallBack={(customerNumber) => {
            getOrderList(1);
          }}
          ref={protocolRef}
        />
        <OfflinePayment
          confirmCallBack={getOrderList}
          ref={offlinePaymentRef}
        />
        <RequestInvoice
          confirmCallBack={() => {
            getOrderList();
          }}
          ref={requestInvoiceRef}
        />
      </React.Suspense>
      <Modal
        title="申请开票"
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="下一步"
        destroyOnHidden
      ></Modal>
    </div>
  );
};

export default Orders;
