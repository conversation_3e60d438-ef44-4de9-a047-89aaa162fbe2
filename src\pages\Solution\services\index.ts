import { request } from '@umijs/max';

// 定义分类项类型
export type CategoryItem = {
  Id: number;
  Name: string;
  ParentId: number;
  HasProducts: boolean;
};

export enum DeliveryMethod {
  'saas' = 1,
  'software' = 0,
  'ai-machine' = 10,
}

export type DeliveryMethodValues =
  (typeof DeliveryMethod)[keyof typeof DeliveryMethod];

// 定义分类列表类型
export type CategoryList = CategoryItem[];
// 异步获取分类列表
export async function getCategories(
  delivery: DeliveryMethodValues,
): Promise<CategoryList> {
  if (delivery === undefined)
    return request(`/api/Categories?SolutionAvailable=true`);
  return request(
    `/api/Categories?DeliveryMethod=${delivery}&SolutionAvailable=true`,
  );
}

// 定义产品项类型
export type ProductItem = {
  Id: number;
  Name: string;
  CategoryId: number;
  GlobalId: string;
  IsHot: boolean;
  LogoUrl: string;
  ShortDescription: string;
  SalesTotal: number;
  LimitPurchase: boolean;
  ShowedPrice: number;
  Currency: string;
};

// 定义产品列表类型
export type ProductList = ProductItem[];
export async function getSolutionsById(
  id: number | undefined,
  delivery: DeliveryMethodValues,
  page: number,
  pageSize: number,
): Promise<{ Data: ProductList; Total: number }> {
  return request(`/api/Solutions`, {
    method: 'GET',
    params: {
      CategoryId: id,
      DeliveryMethod: delivery,
      Page: page,
      PageSize: pageSize,
    },
  });
}
