import useObjectState from '@/hooks/useObjectState';
import { useSearchParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { useEffect } from 'react';
import { SearchParams } from '../services';

const PAGE_SIZE = 20;

const useSearch = <T>(queryFnc: (params: SearchParams) => Promise<T>) => {
  const [searchParams] = useSearchParams();

  const [searchValues, updateState] = useObjectState<SearchParams>({
    Keywords: undefined,
    Page: 0,
    PageSize: PAGE_SIZE,
    CategoryId: undefined,
    DeliveryMethod: undefined,
    Supplier: undefined,
  });

  useEffect(() => {
    const keywords = searchParams.get('k') || undefined;

    updateState({ Keywords: keywords, Page: 0 });
  }, [searchParams.get('k')]);

  const { data, loading } = useRequest(() => queryFnc(searchValues), {
    refreshDeps: [JSON.stringify(searchValues)],
    debounceWait: 300,
  });

  return {
    searchValues,
    data,
    loading,
    updateState,
  };
};

export default useSearch;
