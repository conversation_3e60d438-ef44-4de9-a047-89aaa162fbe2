import TagSelect, { TagSelectOption } from '@/components/TagSelect';
import useTags from '@/components/TagSelect/hooks/useTags.';
import { useCreation, useMemoizedFn } from 'ahooks';
import { Flex } from 'antd';
import { createStyles } from 'antd-style';
import { FC } from 'react';
import { CategoryItem } from '../services';

interface SolutionHeaderProps {
  levelOneMenu: CategoryItem[];
  levelTwoMenu: CategoryItem[];
  selectedOne: string;
  selectedTwo: string;
  setSelectedOne: (key: string) => void;
  setSelectedTwo: (key: string) => void;
}

const useStyles = createStyles(({ css }) => ({
  menuWrapper: css`
    position: relative;
    width: 1200px;
  `,
}));

const SolutionHeader: FC<SolutionHeaderProps> = ({
  levelOneMenu,
  levelTwoMenu,
  selectedOne,
  selectedTwo,
  setSelectedOne,
  setSelectedTwo,
}) => {
  const { styles } = useStyles();

  const levelOneMenuAll = useCreation(
    () =>
      [{ label: '全部分类', value: 'all' }].concat(
        ...levelOneMenu.map((item) => ({
          label: item.Name,
          value: `${item.Id}`,
        })),
      ),
    [levelOneMenu],
  );

  const levelTwoMenuAll = useCreation(
    () =>
      [{ label: '全部子类', value: 'all' }].concat(
        ...levelTwoMenu.map((item) => ({
          label: item.Name,
          value: `${item.Id}`,
        })),
      ),
    [levelTwoMenu],
  );

  const { selectedKeys: selectedOneKeys, onTagClick: onTagClickOne } = useTags({
    dataSource: levelOneMenuAll,
    type: 'radio',
    defaultValue: [selectedOne],
  });

  const { selectedKeys: selectedTwoKeys, onTagClick: onTagClickTwo } = useTags({
    dataSource: levelTwoMenuAll,
    type: 'radio',
    defaultValue: [selectedTwo],
  });

  const handleClickOne = useMemoizedFn((value: string, checked: boolean) => {
    onTagClickOne(value, checked);
    setSelectedOne(value);
    handleClickTwo('all', true);
  });

  const handleClickTwo = useMemoizedFn((value: string, checked: boolean) => {
    onTagClickTwo(value, checked);
    setSelectedTwo(value);
  });

  return (
    <Flex vertical gap={24}>
      <Flex justify="space-between">
        <TagSelect
          hideCheckAll
          items={levelOneMenuAll}
          renderItems={(item, selectedKeys) => (
            <TagSelectOption
              key={item.value}
              disabled={item.disabled}
              value={item.value}
              checked={selectedKeys.includes(item.value)}
              onChange={handleClickOne}
            >
              {item.label}
            </TagSelectOption>
          )}
          value={selectedOneKeys}
        />
      </Flex>
      <Flex justify="space-between" className={styles.menuWrapper}>
        {selectedOneKeys[0] !== 'all' && (
          <TagSelect
            hideCheckAll
            items={levelTwoMenuAll}
            renderItems={(item, selectedKeys) => (
              <TagSelectOption
                key={item.value}
                disabled={item.disabled}
                value={item.value}
                checked={selectedKeys.includes(item.value)}
                onChange={handleClickTwo}
              >
                {item.label}
              </TagSelectOption>
            )}
            value={selectedTwoKeys}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default SolutionHeader;
