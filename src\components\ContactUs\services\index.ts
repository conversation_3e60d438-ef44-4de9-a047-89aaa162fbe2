import { request } from '@umijs/max';

export type FeedbackParams = {
  CompanyName: string;
  CompanyType: number;
  Contact: string;
  Email: string;
  CategoryId: number;
  Title: string;
  Description: string;
};

export async function feedBack(params: FormData, token: string) {
  return request('/api/Feedback', {
    method: 'POST',
    headers: {
      'X-Csrf-Token': token,
    },
    data: params,
  });
}
