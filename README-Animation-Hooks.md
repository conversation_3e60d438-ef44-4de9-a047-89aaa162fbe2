# 滚动动画 Hooks 使用指南

本项目提供了一套完整的滚动动画解决方案，基于 GSAP 和 ScrollTrigger 构建，让您轻松为组件添加专业的滚动进入动画效果。

## 📦 包含的文件

### 核心 Hooks
- `src/hooks/useScrollAnimation.ts` - 主要的滚动动画Hook，功能最全面
- `src/hooks/useFadeInAnimation.ts` - 简化版Hook，专注于常用的淡入效果

### 示例组件
- `src/components/AnimatedSection/index.tsx` - 动画演示组件
- `src/components/FeatureCards/index.tsx` - 特性卡片组件
- `src/pages/AnimationDemo/index.tsx` - 完整的演示页面

### 文档和测试
- `src/hooks/useScrollAnimation.md` - 详细使用文档
- `src/hooks/__tests__/useScrollAnimation.test.tsx` - 单元测试

## 🚀 快速开始

### 1. 最简单的使用方式

```tsx
import { useFadeInAnimation } from '@/hooks/useFadeInAnimation';

const MyComponent = () => {
  const containerRef = useFadeInAnimation('.my-item');

  return (
    <div ref={containerRef}>
      <div className="my-item">这个元素会有淡入动画</div>
      <div className="my-item">这个元素也会有淡入动画</div>
    </div>
  );
};
```

### 2. 使用预设动画

```tsx
import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';

const MyComponent = () => {
  const containerRef = useScrollAnimation(animationPresets.fadeInScale);

  return (
    <div ref={containerRef}>
      <div className="animate-item">缩放淡入效果</div>
    </div>
  );
};
```

### 3. 自定义动画参数

```tsx
import { useScrollAnimation } from '@/hooks/useScrollAnimation';

const MyComponent = () => {
  const containerRef = useScrollAnimation({
    selector: '.product-card',
    initialY: 100,
    duration: 1.2,
    stagger: 0.3,
    ease: 'back.out(1.7)',
  });

  return (
    <div ref={containerRef}>
      <div className="product-card">产品卡片 1</div>
      <div className="product-card">产品卡片 2</div>
    </div>
  );
};
```

## 🎯 实际应用示例

### 更新现有的 Advantages 组件

原来的代码：
```tsx
// 原来需要手动写GSAP代码
useGSAP(() => {
  gsap.set('.product-card', { y: 50, opacity: 0 });
  ScrollTrigger.create({
    trigger: container.current,
    start: 'top 85%',
    once: true,
    onEnter: () => gsap.to('.product-card', {
      y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'power2.out'
    })
  });
}, { scope: container });
```

现在只需要：
```tsx
// 使用Hook，一行代码搞定
const container = useScrollAnimation({
  selector: '.product-card',
  initialY: 50,
  duration: 0.8,
  stagger: 0.2,
});
```

### 产品列表组件

```tsx
import { useCardAnimation } from '@/hooks/useFadeInAnimation';

const ProductList = ({ products }) => {
  const containerRef = useCardAnimation();

  return (
    <div ref={containerRef}>
      {products.map(product => (
        <div key={product.id} className="card-item">
          <img src={product.image} alt={product.name} />
          <h3>{product.name}</h3>
          <p>{product.description}</p>
        </div>
      ))}
    </div>
  );
};
```

### 特性介绍区块

```tsx
import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';

const Features = () => {
  const containerRef = useScrollAnimation(animationPresets.fadeInScale);

  return (
    <section ref={containerRef}>
      <div className="animate-item">
        <Icon type="speed" />
        <h3>高性能</h3>
      </div>
      <div className="animate-item">
        <Icon type="easy" />
        <h3>易使用</h3>
      </div>
    </section>
  );
};
```

## 🎨 可用的动画预设

| 预设名称 | 效果描述 | 适用场景 |
|----------|----------|----------|
| `fadeInUp` | 从下方淡入 | 通用场景，最常用 |
| `fadeInDown` | 从上方淡入 | 标题、导航等 |
| `fadeInLeft` | 从左侧淡入 | 文本内容 |
| `fadeInRight` | 从右侧淡入 | 图片、媒体内容 |
| `fadeInScale` | 缩放淡入 | 卡片、按钮等 |
| `fadeInFast` | 快速淡入 | 需要快速响应的元素 |
| `fadeInSlow` | 慢速淡入 | 需要优雅展示的内容 |

## 🛠️ 简化版 Hooks

为了更方便使用，提供了几个简化版的 Hooks：

```tsx
import { 
  useFadeInAnimation,  // 基础淡入
  useFadeInFast,       // 快速淡入
  useFadeInSlow,       // 慢速淡入
  useCardAnimation,    // 卡片动画
  useListAnimation     // 列表动画
} from '@/hooks/useFadeInAnimation';
```

## 📝 最佳实践

### 1. 选择合适的选择器
```tsx
// ✅ 推荐：使用语义化的类名
const containerRef = useScrollAnimation({ selector: '.feature-card' });

// ❌ 避免：使用过于通用的选择器
const containerRef = useScrollAnimation({ selector: 'div' });
```

### 2. 合理设置交错延迟
```tsx
// ✅ 推荐：根据元素数量调整交错延迟
const containerRef = useScrollAnimation({
  stagger: items.length > 6 ? 0.1 : 0.2  // 元素多时减少延迟
});
```

### 3. 考虑性能影响
```tsx
// ✅ 推荐：在移动设备上简化动画
const containerRef = useScrollAnimation({
  duration: isMobile ? 0.4 : 0.8,
  stagger: isMobile ? 0.1 : 0.2,
});
```

### 4. 提供动画开关
```tsx
// ✅ 推荐：允许用户关闭动画
const containerRef = useScrollAnimation(
  enableAnimation ? { selector: '.item' } : { duration: 0 }
);
```

## 🔧 自定义动画

对于复杂的动画需求，可以使用 `customAnimation` 参数：

```tsx
const containerRef = useScrollAnimation({
  selector: '.custom-element',
  customAnimation: {
    from: { 
      scale: 0, 
      rotation: 180, 
      opacity: 0 
    },
    to: { 
      scale: 1, 
      rotation: 0, 
      opacity: 1, 
      duration: 1.5,
      stagger: 0.2,
      ease: 'elastic.out(1, 0.3)'
    },
  },
});
```

## 🎯 迁移指南

如果您的项目中已经有使用 GSAP 的组件，可以按照以下步骤迁移：

1. **识别动画模式**：查看现有的 GSAP 代码，识别动画类型
2. **选择合适的 Hook**：根据动画效果选择对应的 Hook
3. **替换代码**：用 Hook 替换原有的 GSAP 代码
4. **测试效果**：确保动画效果符合预期
5. **优化参数**：根据需要调整动画参数

## 📚 更多资源

- [GSAP 官方文档](https://greensock.com/docs/)
- [ScrollTrigger 文档](https://greensock.com/docs/v3/Plugins/ScrollTrigger)
- [动画设计原则](https://material.io/design/motion/understanding-motion.html)

## 🤝 贡献

如果您有新的动画预设想法或发现了问题，欢迎提交 Issue 或 Pull Request！
