import { createStyles, cx } from 'antd-style';
import { FC, memo, useState } from 'react';

interface FlipCardProps {
  title: string;
  scenario: string;
  outcome: string;
}

const useStyles = createStyles(({ css }) => ({
  caseCard: css`
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    width: 100%;
    height: 180px;
    perspective: 1000px;
  `,
  caseCardInner: css`
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
  `,
  flipped: css`
    transform: rotateY(180deg);
  `,
  caseCardContent: css`
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    padding: 20px;
    border-radius: 8px;
    transition: background-color 0.3s;
  `,
  caseCardBack: css`
    background-color: #f0f6ff;
  `,
  caseCardFront: css`
    background-color: #fff;
    transform: rotateY(180deg);
    overflow-y: auto;
  `,
  caseTitle: css`
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
  `,
  caseDesc: css`
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    max-height: 120px;
    overflow-y: hidden;
    transition: all 0.3s;
    &:hover {
      overflow-y: auto;
    }
  `,
}));

const FlipCard: FC<FlipCardProps> = ({ title, scenario, outcome }) => {
  const { styles } = useStyles();
  const [isFlipped, setIsFlipped] = useState(true);

  const handleClick = () => {
    setIsFlipped(!isFlipped);
  };
  return (
    <div className={styles.caseCard} onClick={handleClick}>
      <div className={cx(styles.caseCardInner, isFlipped && styles.flipped)}>
        <div className={cx(styles.caseCardContent, styles.caseCardFront)}>
          <div className={styles.caseTitle}>{title}</div>
          <div className={styles.caseDesc}>{scenario}</div>
        </div>
        <div className={cx(styles.caseCardContent, styles.caseCardBack)}>
          <div className={styles.caseTitle}>{title}</div>
          <div className={styles.caseDesc}>{outcome}</div>
        </div>
      </div>
    </div>
  );
};

export default memo(FlipCard);
