import ErrorImage from '@/components/ErrorImage';
import NotFound from '@/components/NotFound';
import PageWrapper from '@/components/PageWrapper';
import PriceStar from '@/components/pricestar';
import { PRODUCT_TYPES } from '@/constants';
import useUserStore, { useUserDeriveState } from '@/store/user';
import { CartItemModel } from '@/types';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { useModel, useNavigate, useParams } from '@umijs/max';
import { useBoolean, useCreation } from 'ahooks';
import { Breadcrumb, Flex, message, Skeleton } from 'antd';
import { createStyles, cx } from 'antd-style';
import Big from 'big.js';
import { useCallback, useEffect, useMemo } from 'react';
import { getCart } from '../Cart/services';
import BuyButton from './components/BuyButton';
import Detail from './components/Detail';
import ProductInfo from './components/ProductInfo';
import ProductPlan from './components/ProductPlan';
import RelationProduct from './components/RelationProduct';
import useDetail from './hooks/useDetail';
import useOperation from './hooks/useOperation';
import useProductInfo from './hooks/useProductInfo';
import { addCart } from './services';
import { filterValidModels, getValidRegions } from './utils';

const useStyles = createStyles(({ prefixCls, css, token }) => ({
  common: css`
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  `,
  container: css`
    // padding-top: 50px;
  `,
  breadcrumb: css`
    padding: 15px 0;
    &.${prefixCls}-breadcrumb {
      .${prefixCls}-breadcrumb-link {
        cursor: pointer;
        &:hover {
          background: transparent;
        }
        :last-child {
          cursor: default;
        }
      }
    }
  `,
  content: css`
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    padding-top: 22px;
    .ant-tag {
      padding: 3px 16px;
    }
    .${prefixCls}-typography {
      color: #6b6b6b;
      font-weight: 400;
    }
  `,
  image: css`
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 280px;
    height: 220px;
    border: 1px solid rgba(229, 229, 229, 1);
    border-radius: 12px;
    overflow: hidden;
  `,
  soldOut: css`
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 20%;
    background: rgba(128, 128, 128, 1);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  `,
}));
const DetailPage = () => {
  const { type, id } = useParams();
  const navigate = useNavigate();
  const { styles } = useStyles();

  const [messageApi, contextHolder] = message.useMessage();
  const { isLogin, isRegistEntity } = useUserDeriveState();
  const { user } = useUserStore();

  const [state, { toggle }] = useBoolean(false);

  const { areaKey, planKey, plan, count, handleSelectedInfo } =
    useProductInfo();
  const { detail, loading, relateData, relateLoading } = useDetail(
    id as string,
  );
  const { setCart } = useModel('Cart.cartModel', (model) => ({
    setCart: model.setCart,
  }));

  const randomRelateData = useCreation(() => {
    if (!relateData?.length) return [];
    // 随机获取3个相关产品
    return relateData
      ?.sort(() => 0.5 - Math.random())
      ?.slice(0, 3)
      ?.map((item) => ({
        id: item.GlobalId,
        name: item.Name,
        supplier: item.Supplier,
        description: item.ShortDescription,
      }));
  }, [relateData]);

  const {
    addProductToCart,
    loading: cartLoading,
    notificationContextHolder,
    refreshCartCount,
    setCartRegion,
  } = useOperation();

  const areaList = useCreation(
    () => getValidRegions(detail?.SolutionEditionsAndPricing || []),
    [JSON.stringify(detail?.SolutionEditionsAndPricing)],
  );

  useEffect(() => {
    if (areaList.length > 0) {
      // 默认选中第一个
      handleSelectedInfo('area', areaList[0].Id);
    }
  }, [JSON.stringify(areaList)]);

  // 获取有效区域
  const planList = useCreation(
    () =>
      filterValidModels(
        detail?.SolutionEditionsAndPricing || [],
        areaKey as number,
      ),
    [JSON.stringify(detail?.SolutionEditionsAndPricing), areaKey],
  );

  useEffect(() => {
    if (planList.length > 0) {
      // 默认选中第一个
      handleSelectedInfo('plan', planList[0].Id, planList[0]);
    }
  }, [JSON.stringify(planList)]);

  const handleBuyNow = useCallback(async () => {
    if (!isLogin) {
      navigate('/login');
      return;
    }
    if (!user?.IsRegistEntity) {
      messageApi.error('请先认证企业账号');
      navigate('/enterprise/info');
      return;
    }
    toggle();
    setCartRegion(plan?.Region ?? 1);
    await addCart({
      SolutionGlobalId: detail?.GlobalId as string,
      SolutionId: plan?.SolutionId as number,
      SolutionEiditionId: plan?.Id as number,
      Quantity: count,
    }).then(() => {
      refreshCartCount();
    });

    const result = await getCart(plan?.Region ?? 1);

    const currentProduct: CartItemModel = result?.Items?.find(
      (item: CartItemModel) => item.EditionId === plan?.Id,
    );

    if (currentProduct) {
      Promise.resolve()
        .then(() => {
          toggle();
          setCart([
            {
              ...currentProduct,
              Quantity: count,
              SubTotal: new Big(currentProduct.UnitPrice)
                .times(count)
                .toNumber(),
            },
          ]);
        })
        .then(() => {
          setTimeout(() => {
            navigate(`/purchase`);
          }, 100);
        });
    }
  }, [detail?.GlobalId, plan?.Id, plan?.SolutionId, count, isLogin, user]);

  const handleAddToCart = useCallback(() => {
    if (!isLogin) {
      navigate('/login');
      return;
    }

    if (!user?.IsRegistEntity) {
      messageApi.error('请先认证企业账号');
      navigate('/enterprise/info');
      return;
    }

    if (!plan) {
      messageApi.error('请选择版本');
      return;
    }

    // 添加到购物车
    addProductToCart({
      SolutionGlobalId: detail?.GlobalId as string,
      SolutionId: plan?.SolutionId as number,
      SolutionEiditionId: plan?.Id as number,
      Quantity: count,
    });
  }, [detail?.GlobalId, plan?.Id, plan?.SolutionId, count, isLogin, user]);

  const disableSelected = useMemo(
    () =>
      !detail?.IsShelved ||
      detail?.LimitPurchase ||
      !isLogin ||
      !isRegistEntity,
    [detail?.IsShelved, detail?.LimitPurchase, isLogin, isRegistEntity],
  );

  const isLimitPurchase = useMemo(
    () => detail?.LimitPurchase,
    [detail?.LimitPurchase],
  );

  if (loading) {
    return (
      <Skeleton
        active
        style={{ minHeight: 'calc(100vh - 400px)', marginTop: 64 }}
        loading={loading}
      />
    );
  }

  if (!detail) {
    return <NotFound jumpType="back" />;
  }

  return (
    <div className={styles.container}>
      {notificationContextHolder}

      <PageWrapper style={{ background: 'transparent', padding: 0 }}>
        <Breadcrumb
          className={styles.breadcrumb}
          separator=">"
          items={[
            {
              title: '首页',
              href: '/',
            },
            {
              title: PRODUCT_TYPES[type as string],
              onClick: () => {
                history.back();
              },
            },
            {
              title: detail?.Name,
            },
          ]}
        ></Breadcrumb>
        <Flex vertical gap={24}>
          <div className={cx(styles.content, styles.common)}>
            <Flex vertical gap={30}>
              <div className={styles.image}>
                <ErrorImage src={detail?.LogoUrl} alt={detail?.Name} />
                {!detail.IsShelved && (
                  <div className={styles.soldOut}>
                    <div>产品已下架</div>
                  </div>
                )}
              </div>

              <Skeleton loading={relateLoading}>
                {relateData?.length ? (
                  <RelationProduct items={randomRelateData} />
                ) : null}
              </Skeleton>
            </Flex>
            <Flex vertical gap={30}>
              <ProductInfo
                supplierId={detail.SupplierId}
                title={detail?.Name}
                delivery={detail?.DeliveryMethod}
                supplier={detail?.Supplier}
                description={detail?.ShortDescription}
                price={
                  detail.LimitPurchase ? (
                    <PriceStar />
                  ) : (
                    `${formatWithThousandSeparator(
                      plan?.OverriddenPrice as number,
                    )}`
                  )
                }
                currency={plan?.Currency as string}
                categories={detail?.Categories}
              />
              <ProductPlan
                desciption={plan?.Description || ''}
                areaList={areaList}
                planList={planList}
                areaKey={areaKey}
                planKey={planKey}
                count={count}
                purchaseLimit={plan?.PurchaseLimit ?? 1}
                disableSelected={disableSelected}
                onSelectedInfo={handleSelectedInfo}
              />
              <BuyButton
                isLimitPurchase={isLimitPurchase}
                disableSelected={disableSelected}
                handleAddToCart={handleAddToCart}
                handleBuyNow={handleBuyNow}
                cartLoading={cartLoading}
                buyLoading={state}
              />
            </Flex>
          </div>
          <Detail
            detail={detail}
            extra={
              <BuyButton
                isLimitPurchase={isLimitPurchase}
                disableSelected={disableSelected}
                handleAddToCart={handleAddToCart}
                handleBuyNow={handleBuyNow}
                cartLoading={cartLoading}
                buyLoading={state}
              />
            }
          />
        </Flex>
        {contextHolder}
      </PageWrapper>
    </div>
  );
};

export default DetailPage;
