import ErrorImage from '@/components/ErrorImage';
import useRouteKey from '@/hooks/useRouteKey';
import { Outlet, useLocation, useNavigate, useParams } from '@umijs/max';
import { Flex, Skeleton, Tabs } from 'antd';
import { createStyles } from 'antd-style';
import useSupplier from './hooks/useSupplier';

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding-bottom: 50px;
    min-height: calc(100vh - 400px);
  `,
  infoBg: css`
    height: 400px;
    background: url(${require('@/assets/landingBg.png')}) no-repeat;
    background-size: cover;
  `,
  infoContent: css`
    width: 1200px;
    height: 100%;
    margin: 0 auto;
  `,
  logo: css`
    flex: 0 0 auto;
    width: 180px;
    height: 180px;
    padding: 10px;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
  `,
  title: css`
    font-size: 32px;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
  `,
  desc: css`
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: rgba(84, 84, 84, 1);
  `,
  companyInfo: css`
    transform: translateY(-30px);
  `,
  list: css`
    width: 1200px;
    margin: 0 auto;
  `,
}));

const VnetZone = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const routeKey = useRouteKey();
  const params = useParams();
  const location = useLocation();

  const { supplier, loading } = useSupplier(params.id as string);
  const handleChangeTabs = (key: string) => {
    navigate(`/supplier/${params.id}/${key}${location.search}`);
  };

  return (
    <div className={styles.container}>
      <Skeleton loading={loading}>
        <div className={styles.infoBg}>
          <Flex align="center" gap={30} className={styles.infoContent}>
            <Flex className={styles.logo} justify="center" align="center">
              <ErrorImage
                src={supplier?.SupplierLogo as string}
                alt={supplier?.SupplierName}
              />
            </Flex>
            <Flex vertical gap={10} className={styles.companyInfo}>
              <div className={styles.title}>{supplier?.SupplierName}</div>
              <div className={styles.desc}>{supplier?.Description}</div>
            </Flex>
          </Flex>
        </div>
      </Skeleton>
      <Flex vertical gap={50}>
        <div className={styles.list}>
          <Tabs
            centered
            activeKey={routeKey}
            items={[
              {
                label: '在线购买',
                key: 'online',
              },
              {
                label: '咨询专属',
                key: 'consultation',
              },
            ]}
            onChange={handleChangeTabs}
          />
          <div>
            <Outlet />
          </div>
        </div>
      </Flex>
    </div>
  );
};

export default VnetZone;
