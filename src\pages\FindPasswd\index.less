.container {
  width: 100%;
  margin-top: 50px;
  position: relative;
}

.bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 0.1;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: linear-gradient(
    180deg,
    rgba(247, 249, 252, 100%) 0%,
    rgba(247, 249, 252, 0%) 100%
  );
  background-image: url('@/assets/bg.png');
}

.regpan {
  width: 550px;
  margin: 0 auto;
}

.ant-form-item-label {
  display: none;
}

.ant-pro-page-container {
  position: relative;
  display: flex;
  align-items: center;
}

.ant-btn[type='submit'] {
  width: 440px;
}

.ant-radio {
  font-size: 12px;
}

.ant-form {
  width: 500px;
}

.reg {
  width: 100%;
}

.reg::before {
  content: '';
  background: linear-gradient(
    132.34deg,
    rgba(17, 75, 237, 100%) 0%,
    rgba(54, 152, 217, 100%) 69.77%,
    rgba(117, 225, 255, 100%) 100%
  );
  position: absolute;
  inset: -1px;
  opacity: 1;
  transition: all 0.3s;
  border-radius: inherit;
}

.reg > span {
  position: relative;
}

.text {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 17.38px;
}

.link {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 17.38px;
  color: rgba(17, 61, 237, 100%);
}

.regsuccess {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 500px;
  height: 400px;
  justify-content: center;
  border-radius: 10px;
  gap: 20px;
  background: rgba(255, 255, 255, 100%);
  box-shadow: 0 2px 2px rgba(255, 255, 255, 25%),
    0 2px 8px rgba(36, 109, 227, 10%), 0 4px 16px rgba(36, 109, 227, 15%);
}

.loginButton {
  width: 400px;
}

.line {
  width: 0;
  height: 240px;
  border-left: 1px solid rgba(229, 229, 229, 100%);
}

.block {
  width: 150px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20.27px;
  color: rgba(56, 56, 56, 100%);
}

.center {
  justify-content: center;
  display: flex;
}
