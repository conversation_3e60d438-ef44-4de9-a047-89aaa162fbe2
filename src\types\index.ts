export * from '../types/cart/cart.model';
export * from '../types/order/order.model';
export * from '../types/order/order.status';
export * from '../types/solution/solution.model';

type PascalToCamelCase<S extends string> =
  S extends `${infer P1}${infer P2}${infer Rest}`
    ? P1 extends Uppercase<P1>
      ? `${Lowercase<P1>}${P2}${PascalToCamelCase<Rest>}`
      : `${P1}${PascalToCamelCase<`${P2}${Rest}`>}`
    : S;

export type CamelCaseKeys<T> = {
  [K in keyof T as PascalToCamelCase<string & K>]: T[K];
};
