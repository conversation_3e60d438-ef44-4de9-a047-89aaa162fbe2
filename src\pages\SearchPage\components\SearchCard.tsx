import CategoriesTree, { treeData } from '@/components/CategoriesTree';
import StandardFormRow from '@/components/StandardFormRow';
import SupplierSelect from '@/components/SupplierSelect';
import TagSelect, { TagSelectOption } from '@/components/TagSelect';
import useTags from '@/components/TagSelect/hooks/useTags.';
import { DeliveryMethod } from '@/constants';
import { useMemoizedFn } from 'ahooks';
import { Flex } from 'antd';
import { FC, memo, useEffect } from 'react';
import { SearchParams } from '../services';

interface SearchCardProps {
  supplierOptions: { label: string; value: string }[];
  searchValues: SearchParams;
  updateSearchValues: (values: Partial<SearchParams>) => void;
  categoriesTreeData: treeData[];
}

const SearchCard: FC<SearchCardProps> = ({
  searchValues,
  updateSearchValues,
  supplierOptions,
  categoriesTreeData,
}) => {
  const { selectedKeys, onTagClick } = useTags({
    dataSource: DeliveryMethod,
    type: 'radio',
    defaultValue: ['all'],
  });

  useEffect(() => {
    updateSearchValues({
      DeliveryMethod: selectedKeys[0] === 'all' ? undefined : selectedKeys[0],
    });
  }, [JSON.stringify(selectedKeys)]);

  const onChangeSupplier = useMemoizedFn((value: string) => {
    updateSearchValues({ Supplier: value });
  });

  return (
    <div>
      <StandardFormRow title={null}>
        <TagSelect
          value={selectedKeys}
          items={DeliveryMethod}
          hideCheckAll
          renderItems={(item) => (
            <TagSelectOption
              value={item.value}
              checked={selectedKeys.includes(item.value)}
              onChange={onTagClick}
            >
              {item.label}
            </TagSelectOption>
          )}
        ></TagSelect>
      </StandardFormRow>
      <Flex justify="space-between" gap={16}>
        <div>
          <StandardFormRow title={null}>
            <SupplierSelect
              options={supplierOptions}
              value={searchValues.Supplier}
              onChange={onChangeSupplier}
            />
          </StandardFormRow>
        </div>
        <div>
          <StandardFormRow title={null}>
            <CategoriesTree
              treeData={categoriesTreeData}
              onChange={(value) => {
                updateSearchValues({ CategoryId: value });
              }}
              value={searchValues.CategoryId}
            />
          </StandardFormRow>
        </div>
      </Flex>
    </div>
  );
};
export default memo(SearchCard);
