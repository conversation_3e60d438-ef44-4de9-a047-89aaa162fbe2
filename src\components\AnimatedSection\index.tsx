import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation';
import { Card, Col, Row, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding: 60px 0;
    background: #f8f9fa;
  `,
  content: css`
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  `,
  title: css`
    text-align: center;
    margin-bottom: 50px;
    h2 {
      color: #333;
      font-size: 32px;
      font-weight: 600;
    }
    p {
      color: #666;
      font-size: 16px;
      margin-top: 10px;
    }
  `,
  card: css`
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
  `,
}));

/**
 * 动画示例组件 - 展示如何使用 useScrollAnimation hooks
 */
const AnimatedSection: React.FC = () => {
  const { styles } = useStyles();

  // 示例1: 使用默认配置
  const defaultContainer = useScrollAnimation({
    selector: '.default-item',
  });

  // 示例2: 使用预设动画
  const presetContainer = useScrollAnimation(animationPresets.fadeInScale);

  // 示例3: 使用自定义动画
  const customContainer = useScrollAnimation({
    selector: '.custom-item',
    customAnimation: {
      from: { 
        rotationY: 90, 
        opacity: 0,
        transformOrigin: 'left center'
      },
      to: { 
        rotationY: 0, 
        opacity: 1, 
        duration: 1,
        stagger: 0.15,
        ease: 'power2.out'
      },
    },
  });

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* 默认动画示例 */}
        <div className={styles.title}>
          <Typography.Title level={2}>默认淡入动画</Typography.Title>
          <Typography.Paragraph>从下方淡入的基础动画效果</Typography.Paragraph>
        </div>
        
        <Row gutter={[24, 24]} ref={defaultContainer} style={{ marginBottom: 80 }}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card className={`${styles.card} default-item`}>
                <Typography.Title level={4}>卡片 {item}</Typography.Title>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 预设动画示例 */}
        <div className={styles.title}>
          <Typography.Title level={2}>缩放淡入动画</Typography.Title>
          <Typography.Paragraph>使用预设的缩放淡入效果</Typography.Paragraph>
        </div>
        
        <Row gutter={[24, 24]} ref={presetContainer} style={{ marginBottom: 80 }}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card className={`${styles.card} animate-item`}>
                <Typography.Title level={4}>预设 {item}</Typography.Title>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 自定义动画示例 */}
        <div className={styles.title}>
          <Typography.Title level={2}>3D旋转动画</Typography.Title>
          <Typography.Paragraph>自定义的3D旋转淡入效果</Typography.Paragraph>
        </div>
        
        <Row gutter={[24, 24]} ref={customContainer}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card className={`${styles.card} custom-item`}>
                <Typography.Title level={4}>自定义 {item}</Typography.Title>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default AnimatedSection;
