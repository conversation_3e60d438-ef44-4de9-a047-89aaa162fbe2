import { useRequest } from 'ahooks';
import { getDetail, getRelatedSolutions } from '../services';

const useDetail = (globalId: string) => {
  const { data, loading } = useRequest(() => getDetail(globalId), {
    staleTime: 1000 * 60,
    refreshDeps: [globalId],
    onSuccess: (res) => {
      if (res.IsShelved) {
        run(res.Id);
      }
    },
  });

  const {
    run,
    data: relateData,
    loading: relateLoading,
  } = useRequest(getRelatedSolutions, {
    manual: true,
    staleTime: 1000 * 60,
    refreshDeps: [globalId],
  });

  return { detail: data, loading, relateData, relateLoading };
};

export default useDetail;
