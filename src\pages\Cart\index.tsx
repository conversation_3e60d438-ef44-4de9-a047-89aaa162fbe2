import PageWrapper from '@/components/PageWrapper';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { useModel, useNavigate } from '@umijs/max';
import { Button, Flex, Space, Typography, notification } from 'antd';
import { createStyles } from 'antd-style';
import CartTable from './CartTable';

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding-top: 50px;
  `,
  amountBox: css`
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 80px;
    padding: 18px 34px;
    background: #fff;
    border: 1px solid #e5e5e5;
  `,
  amountBtn: css`
      border-width: 0;
      width: 120px;
      height: 46px;
      color: #fff;
      background: linear-gradient(
          224.07deg,
          rgba(36, 128, 227, 1) 0%,
          rgba(43, 36, 227, 1) 100%
        );
      
      &:hover {
        color: #fff !important;
        background: linear-gradient(
          224.07deg,
          rgb(68, 134, 205) 0%,
          rgb(13, 5, 247) 80%
        ) !important;
      }
    }
  `,
}));

const Cart = () => {
  const navigate = useNavigate();
  const { styles } = useStyles();
  const { amount, cart } = useModel('Cart.cartModel');
  const gePurchase = () => {
    const allSameType = cart.reduce((acc, item, index, array) => {
      return (
        acc && (index === 0 || item.DeliveryMethod === array[0].DeliveryMethod)
      );
    }, true);
    if (!allSameType) {
      notification.open({
        message: '你所购买的产品属于不同的交付方式，请分别下单，感谢您的配合',
        duration: 2,
      });
      return;
    }
    const allSameBrandZoneId = cart.reduce((acc, item, index, array) => {
      return (
        acc && (index === 0 || item.BrandZone.Id === array[0].BrandZone.Id)
      );
    }, true);
    if (!allSameBrandZoneId) {
      notification.open({
        message: '你所购买的产品属于不同品牌，请分别下单，感谢您的配合',
        duration: 2,
      });
      return;
    }
    navigate('/purchase');
  };
  return (
    <div className={styles.container}>
      <PageWrapper>
        <Typography.Title level={3}>购物车</Typography.Title>
        <div style={{ paddingBottom: 80, minHeight: 'calc(100vh - 188px)' }}>
          <CartTable />
          <div className={styles.amountBox}>
            <Flex justify="space-between" align="center">
              <Space>
                商品总额：
                <Typography.Title level={3}>
                  ￥{formatWithThousandSeparator(amount)}
                </Typography.Title>
              </Space>
              <Button
                size="large"
                onClick={gePurchase}
                disabled={cart.length === 0}
                className={styles.amountBtn}
              >
                结算
              </Button>
            </Flex>
          </div>
        </div>
      </PageWrapper>
    </div>
  );
};

export default Cart;
