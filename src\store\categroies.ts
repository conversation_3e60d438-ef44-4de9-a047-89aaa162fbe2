import { NavMenuItem } from '@/components/NavMenu';
import { CategoryList } from '@/pages/Solution/services';
import { getAllCategroies, getAllCategroiesDisplayed } from '@/services/api';
import { derive } from 'derive-valtio';
import { persist } from 'valtio-persist';
import { devtools, useProxy } from 'valtio/utils';
import { findItemById, formatToNavMenuItems } from './util';

type categoriesStateT = {
  categroiesMenu: NavMenuItem[];
  currentLevelOneId: string;
  currentLevelTwoId: string;
  rawData: CategoryList;
  rawDataDisplayed: CategoryList;
  solutionSelectedOneId: string;
  solutionSelectedTwoId: string;
};

const { store: categoriesState } = await persist<categoriesStateT>(
  {
    categroiesMenu: [],
    rawData: [],
    rawDataDisplayed: [],
    currentLevelOneId: '',
    currentLevelTwoId: '',
    solutionSelectedOneId: 'all',
    solutionSelectedTwoId: 'all',
  },
  'categroies',
);

const setCurrentLevelId = (level: number, id: string) => {
  if (level === 1) {
    categoriesState.currentLevelOneId = id;
    categoriesState.currentLevelTwoId = '';
  }
  if (level === 2) {
    categoriesState.currentLevelTwoId = id;
  }
};

const useCategoriesStore = () => useProxy(categoriesState);

const categoriesDeriveState = derive({
  currentLevelOneTitle: (get) =>
    findItemById(
      get(categoriesState).categroiesMenu,
      get(categoriesState).currentLevelOneId,
    )?.name,
  currentLevelTwoMenu: (get) => {
    return get(categoriesState).categroiesMenu?.find(
      (item: any) => item.key === get(categoriesState).currentLevelOneId,
    )?.children;
  },
});

const useCategoriesDeriveState = () => useProxy(categoriesDeriveState);

const queryAllCategories = async () => {
  try {
    const result = await getAllCategroies();
    categoriesState.rawData = result;
    // categoriesState.categroies = formatToNavMenuItems(result);
  } catch (error) {
    console.error(error);
  }
};

const queryAllCategoriesDisplayed = async () => {
  try {
    const result = await getAllCategroiesDisplayed();
    categoriesState.rawDataDisplayed = result;
    categoriesState.categroiesMenu = formatToNavMenuItems(result);
  } catch (error) {
    console.error(error);
  }
};

const unsub = devtools(categoriesState, { name: 'state name', enabled: true });

export {
  categoriesState,
  queryAllCategories,
  queryAllCategoriesDisplayed,
  setCurrentLevelId,
  useCategoriesDeriveState,
  useCategoriesStore,
};
