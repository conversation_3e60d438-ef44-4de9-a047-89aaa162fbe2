import { request } from '@umijs/max';

export type YellowPagesListParams = {
  Keywords?: string;
  CategoryId: string | undefined;
  DeliveryMethod: string | undefined;
  Page: number;
  PageSize: number;
  Supplier: string | undefined;
};

export interface SuccessCase {
  Id: number;
  GlobalId: string;
  Title: string;
  Scenario: string;
  Outcome: string;
}

export interface YellowPageSolutionData {
  Id: number;
  GlobalId: string;
  CustomerSupport: string;
  MainImage: string;
  SolutionName: string;
  Description: string;
  Detail: string;
  DeliveryMethod: number;
  Supplier: string;
  SupplierId: number;
  Logo: string;
  Status: number;
  BcPersonEmail: string;
  SuccessCases: SuccessCase[];
}
export async function getYellowPagesList(
  params: YellowPagesListParams,
): Promise<{ Data: YellowPageSolutionData[]; Total: number }> {
  return request(`/api/Solutions/Displayed`, {
    method: 'GET',
    params: params,
  });
}
