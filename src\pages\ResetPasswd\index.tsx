import useQuery from '@/hooks/useUrlQuery';
import { resetpassword } from '@/services/user';
import { PageContainer } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Form, Input } from 'antd';
import { useState } from 'react';
import style from './index.less';
import success_img from './success.png';

export default () => {
  const [form] = Form.useForm();
  const [isregsuccess, setIsregsuccess] = useState<boolean>(false);
  const { token } = useQuery();

  const onFinish = async (values: any) => {
    const { password } = values;
    const res = await resetpassword({
      password,
      token,
    });
    console.log(res);
    setIsregsuccess(true);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className={style.container}>
      <div className={style.bg}></div>
      <PageContainer ghost className={style.regpan}>
        {isregsuccess ? (
          <Form
            style={{
              padding: '30px',
              borderRadius: '10px',
              background: 'rgba(255, 255, 255, 1)',
              boxShadow:
                '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
            }}
            name="ok"
            // wrapperCol={{ span: 16 }}
            autoComplete="off"
          >
            <Form.Item className={style.center}>
              <img src={success_img} />
            </Form.Item>
            <Form.Item className={style.center}>密码重置成功</Form.Item>
            <Form.Item>
              <Button
                className={style.reg}
                type="primary"
                htmlType="button"
                onClick={() => {
                  history.push('/login');
                }}
              >
                返回登录
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <Form
            style={{
              padding: '30px',
              borderRadius: '10px',
              background: 'rgba(255, 255, 255, 1)',
              boxShadow:
                '0px 2px 2px  rgba(255, 255, 255, 0.25), 0px 2px 8px  rgba(36, 109, 227, 0.1), 0px 4px 16px  rgba(36, 109, 227, 0.15)',
            }}
            form={form}
            name="basic"
            // wrapperCol={{ span: 16 }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item>
              <h2>输入新密码</h2>
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="请输入密码，必须包含大小写字母和数字，长度不小于六位" />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="确认密码" />
            </Form.Item>

            <Form.Item>
              <Button className={style.reg} type="primary" htmlType="submit">
                确认
              </Button>
            </Form.Item>
          </Form>
        )}
      </PageContainer>
    </div>
  );
};
