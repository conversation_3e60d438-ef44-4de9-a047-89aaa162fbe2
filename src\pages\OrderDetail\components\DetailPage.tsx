import OfflinePayment from '@/components/OfflinePayment';
import {
  putOrderComplete,
  putOrderSingleComplete,
} from '@/services/order/PurchaseController';
import { OrderStatus, PaymentStatus } from '@/types';
import { formatWithThousandSeparator, transform } from '@/utils/currencyUtil';
import { getFileNameFromPath } from '@/utils/fileUtils';
import { formatDateTime, formatDuring, formatUtcDate } from '@/utils/format';
import type { TableProps } from 'antd';
import {
  Button,
  Col,
  ConfigProvider,
  Flex,
  notification,
  Popconfirm,
  Row,
  Space,
  Table,
  Tooltip,
} from 'antd';
import { createStyles } from 'antd-style';
import React, { useRef } from 'react';
import { Regions } from '../../../types';
const Protocol = React.lazy(() => import('@/components/Protocol'));
const RequestInvoice = React.lazy(() => import('@/components/RequestInvoice'));
const useStyles = createStyles(({ css }) => ({
  titleIcon: css`
    width: 4px;
    height: 18px;
    opacity: 1;
    border-radius: 10px;
    display: inline-block;
    margin-right: 2px;
    background: linear-gradient(
      112.6deg,
      rgba(17, 75, 237, 0.1) 0%,
      rgba(17, 75, 237, 8) 100%
    );
  `,
  title: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 18px !import;
    color: rgba(56, 56, 56, 1);
    vertical-align: top;
  `,
  tableFooterText: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 25px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  `,
  tableHeaderText: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    text-align: center;
    margin: -8px !important;
    line-height: 36px;
    height: 36px;
    background: rgb(222, 227, 238);
  `,
  tableFooterNumber: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(17, 75, 237, 1);
    text-align: left;
  `,
  tableFooterAmount: css`
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 128, 99, 1);
    text-align: left;
  `,
  orderInfo: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 36px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    padding-top: 16px;
  `,
  orderInfoValue: css`
    margin-left: 10px;
  `,
}));
interface CheckTagProps {
  orderDetail: any;
  customOrderNumber: string;
  getData: () => void;
}
const DetailPage: React.FC<CheckTagProps> = ({
  orderDetail,
  customOrderNumber,
  getData,
}) => {
  const [api, contextHolder] = notification.useNotification();
  const { styles } = useStyles();
  const offlinePaymentRef = useRef<any>(null);
  const protocolRef = useRef<any>(null);
  const requestInvoiceRef = useRef<any>(null);
  const solutionTableData = () => {
    return orderDetail.OrderItems;
  };
  const addressTableData = () => {
    return [
      {
        ReceiveAddress: orderDetail.ReceiveAddress,
        ReceivePerson: orderDetail.ReceivePerson,
        ReceivePhoneNumber: orderDetail.ReceivePhoneNumber,
        ExpressNumber: orderDetail.ExpressNumber,
      },
    ];
  };
  const paymentTableData = () => {
    if (
      orderDetail.DeliveryMethod === 10 &&
      orderDetail?.Installments?.length > 0
    ) {
      return orderDetail?.Installments ?? [];
    } else {
      return [
        {
          Title: '',
          AmountDue: orderDetail.OrderTotal,
          PaymentType: orderDetail.PaymentType,
          InstallmentStatus: getPaymentStatusName(orderDetail.PaymentStatusId),
          PaidOnUtc: orderDetail.PaidOnUtc,
        },
      ];
    }
  };
  const paymentColumns: TableProps['columns'] = [
    {
      title: '付款阶段',
      dataIndex: 'Title',
      align: 'center',
      key: 'Title',
      render: (item: any, data: any, index) => (
        <>{index === 0 ? '首付款(50%)' : '尾款(50%)'}</>
      ),
    },
    {
      title: '金额',
      dataIndex: 'AmountDue',
      align: 'center',
      key: 'AmountDue',
      render: (value: any) => <>￥{formatWithThousandSeparator(value)}</>,
    },
    {
      title: '付款途径',
      dataIndex: 'PaymentType',
      align: 'center',
      key: 'PaymentType',
    },
    {
      title: '付款状态',
      dataIndex: 'InstallmentStatus',
      align: 'center',
      key: 'InstallmentStatus',
      render: (value: any, item: any) => (
        <>
          <div>{value} </div>
          <div style={{ color: 'rgb(245, 108, 108)' }}>
            {finalPaymentDueDateUtcName(
              item.FinalPaymentDueDateUtc,
              orderDetail.PaymentStatusId,
            )}
          </div>
        </>
      ),
    },
    {
      title: '付款时间',
      dataIndex: 'PaidOnUtc',
      align: 'center',
      key: 'PaidOnUtc',
      render: (value: any, data: any) => (
        <> {value ? formatUtcDate(new Date(value)) : ''}</>
      ),
    },
    {
      title: '操作',
      dataIndex: 'sum',
      align: 'center',
      key: 'sum',
      render: (item: any, data: any, index) => {
        if (index === 0) {
          return {
            children: (
              <Space>
                {orderDetail.DisplayPaymentButton &&
                  orderDetail.HasContract &&
                  orderDetail.PaymentMethodId !== 3 && (
                    <Button
                      color="primary"
                      variant="outlined"
                      onClick={() => {
                        paymentClick();
                      }}
                    >
                      {orderDetail.DeliveryMethod === 10
                        ? orderDetail.PaymentPlanId === 0
                          ? '确认付款'
                          : orderDetail?.Installments[0].InstallmentStatusId ===
                            1
                          ? '确认尾款'
                          : '确认首款'
                        : '确认付款'}
                    </Button>
                  )}
                {orderDetail.DisplayPaymentButton &&
                  !orderDetail.HasContract && (
                    <Button
                      color="primary"
                      variant="outlined"
                      onClick={() => {
                        protocolShow();
                      }}
                    >
                      确认合同
                    </Button>
                  )}
                {(orderDetail.PaymentStatusId === 1 ||
                  orderDetail.PaymentStatusId === 9) &&
                  orderDetail.InvoiceStatus === 0 && (
                    <Button
                      color="primary"
                      variant="outlined"
                      onClick={() => {
                        requestInvoiceShow();
                      }}
                    >
                      申请开票
                    </Button>
                  )}
              </Space>
            ),
            props: {
              rowSpan: 3,
            },
          };
        }
        return {
          children: <></>,
          props: {
            rowSpan: 0,
          },
        };
      },
    },
  ];
  const finalPaymentDueDateUtcName = (
    finalPaymentDueDateUtc: string,
    paymentStatusId: number,
  ) => {
    if (finalPaymentDueDateUtc) {
      if (paymentStatusId === 13 || paymentStatusId === 14) {
        return (
          '最晚付款时间：' + formatUtcDate(new Date(finalPaymentDueDateUtc))
        );
      } else {
        return '';
      }
    } else {
      return '';
    }
  };
  const addressColumns: TableProps['columns'] = [
    {
      title: '收货人',
      dataIndex: 'ReceivePerson',
      align: 'center',
      key: 'ReceivePerson',
    },
    {
      title: '收货地址',
      dataIndex: 'ReceiveAddress',
      align: 'center',
      key: 'ReceiveAddress',
    },
    {
      title: '联系电话',
      dataIndex: 'ReceivePhoneNumber',
      align: 'center',
      key: 'ReceivePhoneNumber',
    },
    {
      title: '物流单号',
      dataIndex: 'ExpressNumber',
      align: 'center',
      key: 'ExpressNumber',
      render: (item: any) => <>{item ? item.replace(',', '') : '-'}</>,
    },
  ];
  const getPaymentStatusName = (paymentStatusId: number) => {
    let displayValue = '';
    const orders = PaymentStatus.filter((item) => item.id === paymentStatusId);
    displayValue = orders && orders.length > 0 ? orders[0].value : '';
    return displayValue;
  };
  const getOrderStatusName = (statusId: number) => {
    let displayValue = '';
    const orders = OrderStatus.filter((item) => item.id === statusId);
    displayValue = orders && orders.length > 0 ? orders[0].value : '';
    return displayValue;
  };
  const requestInvoiceShow = () => {
    if (requestInvoiceRef.current) {
      requestInvoiceRef.current.modalHandleShow(orderDetail);
    }
  };
  const paymentClick = () => {
    if (orderDetail.PaymentMethodId === 1) {
      if (protocolRef.current) {
        protocolRef.current.modalHandleShow(1, orderDetail.CustomOrderNumber);
      }
    } else {
      if (offlinePaymentRef.current) {
        let installmentNumber = '';
        if (
          orderDetail.DeliveryMethod === 10 &&
          orderDetail?.Installments?.length > 0
        ) {
          if (orderDetail?.Installments[0].InstallmentStatusId === 1) {
            installmentNumber = orderDetail?.Installments[1]?.InstallmentNumber;
          } else {
            installmentNumber = orderDetail?.Installments[0]?.InstallmentNumber;
          }
        }
        offlinePaymentRef.current.modalHandleShow(
          'pay',
          {
            paymentNumber: orderDetail.PaymentNumber,
            customOrderNumber: orderDetail.CustomOrderNumber,
            installmentNumber: installmentNumber,
          },
          true,
        );
      }
    }
  };
  const protocolShow = () => {
    if (protocolRef.current) {
      protocolRef.current.modalHandleShow(1, orderDetail.CustomOrderNumber);
    }
  };
  const deliveryOrderItemConfirm = (orderItemGlobalId: string) => {
    putOrderSingleComplete(orderItemGlobalId).then((res) => {
      getData();
      api['success']({
        message: '确认交付成功',
        duration: 2,
      });
    });
  };
  const solutionColumns: TableProps['columns'] = [
    {
      title: '商品',
      dataIndex: 'SolutionName',
      align: 'center',
      key: 'SolutionName',
      render: (item: any, data: any) => (
        <>
          <div>{data.SolutionName}</div>
          <div>{data.SolutionAttributeCombinationName}</div>
        </>
      ),
    },
    {
      title: '购买参数',
      dataIndex: 'PurchaseParameter',
      align: 'center',
      key: 'PurchaseParameter',
      render: (item: any, data: any) => <>{JSON.parse(item)?.Email ?? ''}</>,
    },
    {
      title: '价格',
      dataIndex: 'UnitPrice',
      align: 'center',
      key: 'UnitPrice',
      render: (item: any, data: any) => (
        <>
          {regionCurrency(orderDetail.region) +
            formatWithThousandSeparator(data.UnitPrice)}
        </>
      ),
    },
    {
      title: '数量',
      dataIndex: 'Quantity',
      align: 'center',
      key: 'Quantity',
    },
    {
      title: '小计',
      dataIndex: 'sum',
      align: 'center',
      render: (item: any, data: any) => (
        <>
          {regionCurrency(orderDetail.region) +
            formatWithThousandSeparator(data.Total)}
        </>
      ),
    },
    {
      title: '交付状态',
      dataIndex: 'DeliveryStatus',
      align: 'center',
      key: 'DeliveryStatus',
    },
    {
      title: '交付内容',
      dataIndex: 'content',
      align: 'center',
      key: 'content',
      render: (item: any, data: any) => (
        <div>
          {data?.OrderItemDeliveries?.map((de: any, index: number) => (
            <div key={index}>
              <p
                style={{
                  textAlign: 'left',
                  maxWidth: '200px',
                  wordBreak: 'break-all',
                }}
              >
                {de.Content}
              </p>
              {de?.OrderItemDeliveryAttachments?.map(
                (item: any, ix: number) => (
                  <p key={index}>
                    {/* <span
                      style={{
                        display: 'block',
                        paddingLeft: 16,
                        maxWidth: 200,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {getFileNameFromPath(item.FileName)}
                    </span> */}
                    <span style={{ display: 'block', textAlign: 'left' }}>
                      <a target="_blank" rel="noreferrer" href={item.FileUrl}>
                        {getFileNameFromPath(item.FileName)}
                      </a>
                    </span>
                  </p>
                ),
              )}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'Status',
      align: 'center',
      key: 'Status',
      render: (item: any, data: any) => (
        <div>
          {data.DeliveryStatusId === 3 && orderDetail.DeliveryMethod !== 10 && (
            <>
              <Popconfirm
                title="是否确认交付?"
                onConfirm={() =>
                  deliveryOrderItemConfirm(data.OrderItemGlobalId)
                }
                okButtonProps={{
                  size: 'small',
                }}
                cancelButtonProps={{
                  size: 'small',
                }}
              >
                <Button
                  color="primary"
                  variant="outlined"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  确认交付
                </Button>
              </Popconfirm>
              <p style={{ marginTop: 16 }}>
                系统将于{' '}
                <span style={{ color: '#06A3D0' }}>
                  {autoCompleteDeadLine(data)}
                </span>{' '}
                后自动确认交付
              </p>
            </>
          )}
        </div>
      ),
    },
  ];

  const autoCompleteDeadLine = (item: any) => {
    const dateNow = new Date();
    const deadLineDate = new Date(item.AutoCompleteDeadLineUtc);
    deadLineDate.setHours(deadLineDate.getHours() + 8);
    const diffInterval = deadLineDate.getTime() - dateNow.getTime();
    return diffInterval >= 0 ? formatDuring(diffInterval) : '0s';
  };
  const deliveryOrderConfirm = () => {
    putOrderComplete(customOrderNumber).then((res) => {
      getData();
    });
  };
  const regionCurrency = (region: number) => {
    const filterRegions = Regions.filter((item) => item.value === region);
    return filterRegions.length > 0 ? filterRegions[0].currency : '¥';
  };
  const tableFooter = () => {
    return (
      <div className={styles.tableFooterText}>
        <Row>
          <Col span={12}>
            <span>
              共
              <span className={styles.tableFooterNumber}>
                {solutionTableData().length}
              </span>
              件商品，商品总金额
              <span className={styles.tableFooterNumber}>
                {transform(orderDetail.Currency)}
                {formatWithThousandSeparator(orderDetail.OrderTotal)}
              </span>
            </span>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <span>
              应付总额
              <span className={styles.tableFooterAmount}>
                {transform(orderDetail.Currency)}
                {formatWithThousandSeparator(orderDetail.OrderTotal)}
              </span>
            </span>
          </Col>
        </Row>
      </div>
    );
  };
  const paymentTableHeader = () => {
    return (
      <div className={styles.tableHeaderText}>
        <Row>
          <Col span={8}>
            <span style={{ fontWeight: 600, marginRight: 8, fontSize: 15 }}>
              订单总额：
            </span>
            ￥{formatWithThousandSeparator(orderDetail.OrderTotal)}
          </Col>
          <Col span={8}>
            <span style={{ fontWeight: 600, marginRight: 8, fontSize: 15 }}>
              付款方式：
            </span>
            {orderDetail.PaymentMethod}
          </Col>
          <Col span={8}>
            <span style={{ fontWeight: 600, marginRight: 8, fontSize: 15 }}>
              付款状态：
            </span>
            {getPaymentStatusName(orderDetail.PaymentStatusId)}
          </Col>
          {/* <Col span={9} style={{ textAlign: 'right' }}>
            <Space>
            {orderDetail.DisplayPaymentButton && orderDetail.HasContract && (
              
              <Button
                color="primary"
                variant="outlined"
                onClick={() => {
                  paymentClick();
                }}
              >
                {orderDetail.DeliveryMethod===10?(orderDetail.PaymentPlanId===0?"确认付款":(
                  orderDetail.Installments[0].IsInitialPayment?"确认首款":"确认尾款"
                )):"确认付款"}
                
              </Button>
            )}
            {orderDetail.DisplayPaymentButton && !orderDetail.HasContract && (
              <Button
                color="primary"
                variant="outlined"
                onClick={() => {
                  protocolShow();
                }}
              >
                确认合同
              </Button>
            )}
            {(orderDetail.PaymentStatusId === 1 ||
              orderDetail.PaymentStatusId === 9) &&
              orderDetail.InvoiceStatus === 0 && (
                <Button
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    requestInvoiceShow();
                  }}
                >
                  申请开票
                </Button>
              )}
            </Space>
          </Col> */}
        </Row>
      </div>
    );
  };
  return (
    <div>
      {contextHolder}
      <ConfigProvider
        theme={{
          components: {
            Table: {
              footerBg: '#fffff',
            },
          },
        }}
      >
        <div>
          <div>
            <Row>
              <Col span={8}>
                <div style={{ marginTop: 16 }}>
                  <div className={styles.titleIcon}></div>
                  <span className={styles.title}>基本信息</span>
                </div>
                <div className={styles.orderInfo}>
                  <div>
                    订单编号：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomOrderNumber}
                    </span>
                  </div>
                  <div>
                    订单状态：
                    <span className={styles.orderInfoValue}>
                      {getOrderStatusName(orderDetail.OrderStatusId)}
                    </span>
                  </div>
                  <div>
                    订单总额：
                    <span className={styles.orderInfoValue}>
                      {transform(orderDetail.Currency)}
                      {formatWithThousandSeparator(orderDetail.OrderTotal)}
                    </span>
                  </div>
                  <div>
                    付款方案：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.PaymentPlan}
                    </span>
                  </div>
                  <div>
                    创建时间：
                    <span className={styles.orderInfoValue}>
                      {orderDetail?.CreatedOnUtc
                        ? formatDateTime(
                            new Date(orderDetail?.CreatedOnUtc),
                            true,
                          )
                        : ''}
                    </span>
                  </div>
                  <div>
                    是否特价：
                    <Tooltip
                      title={
                        orderDetail.ChangePriceDueDateUtc
                          ? '特价有效期：' +
                            formatDateTime(
                              new Date(orderDetail.ChangePriceDueDateUtc),
                              true,
                            )
                          : null
                      }
                    >
                      <span className={styles.orderInfoValue}>
                        {orderDetail?.ChangePrice ? '是' : '否'}
                      </span>
                    </Tooltip>
                  </div>
                  <div>
                    是否账期：
                    <Space>
                      <span className={styles.orderInfoValue}>
                        {orderDetail?.BillingPeriod ? '是' : '否'}
                      </span>
                      {orderDetail?.MergeNCEI === true && '（已合并）'}
                      {orderDetail?.MergeNCEI === false && '（未合并）'}
                    </Space>
                  </div>
                  {/* <div>
                            合同类型：
                            <span className={styles.orderInfoValue}>
                                {contractName(orderDetail.ContractType)}
                            </span>
                            </div> */}
                  <div>
                    蓝云销售：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BlueCloudSalesEmail}
                    </span>
                  </div>
                  <div>
                    订单备注：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.Remark || '无'}
                    </span>
                  </div>
                </div>
              </Col>

              <Col span={8}>
                <div style={{ marginTop: 16 }}>
                  <div className={styles.titleIcon}></div>
                  <span className={styles.title}>收货人信息</span>
                </div>
                <div className={styles.orderInfo}>
                  <div>
                    客户公司：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomerCompany}
                    </span>
                  </div>
                  <div>
                    客户姓名：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomerName}
                    </span>
                  </div>
                  <div>
                    客户邮箱：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomerEmail}
                    </span>
                  </div>
                  <div>
                    客户电话：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomerPhone}
                    </span>
                  </div>
                  <div>
                    地址：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.CustomerAddress}
                    </span>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginTop: 16 }}>
                  <div className={styles.titleIcon}></div>
                  <span className={styles.title}>渠道商</span>
                </div>
                <div className={styles.orderInfo}>
                  <div>
                    公司名称：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserCompanyName}
                    </span>
                  </div>
                  {/* <div>
                            类型：
                            <span className={styles.orderInfoValue}>
                                {orderDetail.BuyUserCompanyType}
                            </span>
                            </div> */}
                  <div>
                    公司地址：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserAddress}
                    </span>
                  </div>
                  <div>
                    订单创建人：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserName}
                    </span>
                  </div>
                  <div>
                    邮箱：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserEmail}
                    </span>
                  </div>
                  <div>
                    电话：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserPhone}
                    </span>
                  </div>
                  <div>
                    角色：
                    <span className={styles.orderInfoValue}>
                      {orderDetail.BuyUserRoleNames}
                    </span>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
          <div style={{ marginTop: 16 }}>
            <div className={styles.titleIcon}></div>
            <span className={styles.title}>付款信息</span>
          </div>
          <div style={{ marginTop: 16 }}>
            <Table
              bordered={true}
              pagination={false}
              columns={
                orderDetail?.DeliveryMethod === 10 &&
                orderDetail?.PaymentPlanId === 1
                  ? paymentColumns
                  : paymentColumns.filter((c) => c.key !== 'Title')
              }
              dataSource={paymentTableData()}
              size="small"
              title={paymentTableHeader}
            />
          </div>
          <div style={{ marginTop: 32 }}>
            <Flex
              justify="space-between"
              style={{ width: '100%', marginBottom: 10 }}
            >
              <div>
                <div className={styles.titleIcon}></div>
                <span className={styles.title}>商品信息</span>
              </div>
              {orderDetail.DeliveryStatusId === 3 &&
                orderDetail.DeliveryMethod !== 10 && (
                  <Popconfirm
                    title="是否确认交付?"
                    onConfirm={() => deliveryOrderConfirm()}
                    okButtonProps={{
                      size: 'small',
                    }}
                    cancelButtonProps={{
                      size: 'small',
                    }}
                  >
                    <Button
                      color="primary"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      确认交付
                    </Button>
                  </Popconfirm>
                )}
            </Flex>
          </div>
          <div style={{ marginTop: 16 }}>
            <Table
              size="small"
              bordered={true}
              pagination={false}
              rowKey={'SolutionId'}
              columns={
                orderDetail?.DeliveryMethod === 1
                  ? solutionColumns
                  : solutionColumns.filter((c) => c.key !== 'PurchaseParameter')
              }
              dataSource={solutionTableData()}
              footer={tableFooter}
            />
          </div>
          {orderDetail?.DeliveryMethod === 10 && (
            <>
              <div style={{ marginTop: 16 }}>
                <div className={styles.titleIcon}></div>
                <span className={styles.title}>发货信息</span>
              </div>
              <div style={{ marginTop: 16 }}>
                <Table
                  size="small"
                  bordered={true}
                  pagination={false}
                  rowKey={'SolutionId'}
                  columns={addressColumns}
                  dataSource={addressTableData()}
                />
              </div>
            </>
          )}
        </div>
      </ConfigProvider>
      <React.Suspense>
        <Protocol
          confirmCallBack={(customerNumber) => {
            getData();
          }}
          ref={protocolRef}
        />
        <RequestInvoice
          confirmCallBack={() => {
            getData();
          }}
          ref={requestInvoiceRef}
        />
      </React.Suspense>

      <OfflinePayment confirmCallBack={getData} ref={offlinePaymentRef} />
    </div>
  );
};
export default DetailPage;
