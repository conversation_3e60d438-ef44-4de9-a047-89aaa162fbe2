import { ReactComponent as CartSV<PERSON> } from '@/assets/cart.svg';
import { ReactComponent as EnterpriseSVG } from '@/assets/enterprise.svg';
import { ReactComponent as LogoSVG } from '@/assets/logo.svg';
import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import useRouteKey from '@/hooks/useRouteKey';
import { setCurrentLevelId, useCategoriesStore } from '@/store/categroies';
import { useMenuStore } from '@/store/menu';
import useUserStore, {
  getCompany,
  getUser,
  userLogout,
  useUserDeriveState,
} from '@/store/user';
import {
  Link,
  useModel,
  useNavigate,
  useParams,
  useSearchParams,
} from '@umijs/max';
import { useCreation, useSafeState } from 'ahooks';
import { Avatar, Badge, Button, Divider, Flex, Popover, Space } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';
import Authorize from '../Authorize';
import Reminders from '../EnterpriseReminders';
import NavMenu, { NavMenuProps } from '../NavMenu';

const useStyles = createStyles(
  ({ css, token }, props: { isScroll: boolean }) => ({
    container: css`
      min-width: 1280px;
      position: sticky;
      top: 0;
      background: rgba(255, 255, 255, ${props.isScroll ? 1 : 0.5});
      width: 100%;
      height: 50px;
      padding: 0 20px;
      transition: all 0.3s ease-in-out;
      z-index: 999;
    `,
    header: css`
      width: 100%;
      height: 100%;
    `,
    input: css`
      cursor: pointer;
      border-radius: 28px;
      width: 160px;
      height: 30px;
      padding: 0 8px;
      transition: all 0.3s ease-in-out;
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      gap: 8px;
      background: #fff;
      border: 1px solid ${props.isScroll ? '#d9d9d9' : 'transparent'};
    `,
    menuContainer: css`
      flex: 1;
    `,
    menu: css`
      min-width: 400px;
      margin-left: 20px;
      background: transparent;
      a {
        color: #383838;
      }
      .ant-menu-submenu-selected a {
        color: ${token.colorPrimary};
      }
      .ant-menu-item,
      .ant-menu-submenu-title {
        font-weight: 400;
        color: #383838;
      }
    `,
    text: css`
      color: #383838;
      background: rgba(255, 255, 255, 0.3);
    `,
    vnetzone: css`
      background: linear-gradient(90deg, #8b5cf6, #3b82f6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 600;
    `,
  }),
);

const Header = () => {
  const [affixed, setAffixed] = useSafeState(false);

  const { styles } = useStyles({ isScroll: affixed });
  const navigate = useNavigate();
  const routeKey = useRouteKey(); // 获取当前路由
  const params = useParams();
  const menuStore = useMenuStore();

  const [searchParmas] = useSearchParams();

  const { isLogin, showReminder } = useUserDeriveState();

  const { brands } = useModel('brands', (model) => ({ brands: model.brands }));
  const { count, getCount } = useModel('cart', (model) => ({
    getCount: model.run,
    count: model.count,
  }));
  const { user, token } = useUserStore();
  const categoriesStore = useCategoriesStore();

  useEffect(() => {
    if (token) {
      getUser();
      getCount();
      getCompany();
    }
  }, [token]);

  const brandsMenu: NavMenuProps['items'] = useCreation(
    () =>
      brands?.length
        ? brands?.map((item) => ({
            key: `${item.Id}`,
            name: item.BrandName,
          }))
        : [],
    [JSON.stringify(brands)],
  );

  const handleSelect: NavMenuProps['onSelect'] = ({ keyPath }) => {
    menuStore.selectedKeys = keyPath;
    if (keyPath.length > 1) {
      if (keyPath[0] === 'yellow-pages-products') {
        if (keyPath[1]) {
          setCurrentLevelId(1, keyPath[1]);
        }
        if (keyPath[2]) {
          setCurrentLevelId(2, keyPath[2]);
        }
        navigate('/yellow-pages-products');
      }

      if (keyPath[0] === 'brand') {
        navigate(`/brand/${keyPath[1]}`);
      }
      if (keyPath[0] === 'solution') {
        navigate(`/solution?t=${keyPath[1]}`);
      }
    } else {
      navigate(keyPath[0]);
    }
  };

  useEffect(() => {
    if (routeKey === 'solution') {
      menuStore.selectedKeys = ['solution', searchParmas.get('t')!];
    } else if (routeKey === 'brand') {
      menuStore.selectedKeys = ['brand', params.id!];
    } else if (routeKey === 'yellow-pages-products') {
      menuStore.selectedKeys = [
        'yellow-pages-products',
        categoriesStore.currentLevelOneId,
        categoriesStore.currentLevelTwoId,
      ];
    } else if (routeKey === 'home') {
      menuStore.selectedKeys = [];
    }
  }, [
    routeKey,
    params.id,
    searchParmas.get('t'),
    categoriesStore.currentLevelOneId,
    categoriesStore.currentLevelTwoId,
  ]);

  const showReminders = useCreation(
    () => isLogin && showReminder && routeKey !== 'info' && <Reminders />,
    [isLogin, showReminder, routeKey],
  );

  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 0) {
        setAffixed(true);
      } else {
        setAffixed(false);
      }
    });
    return () => {
      window.removeEventListener('scroll', () => {});
    };
  }, []);

  return (
    <div className={styles.container}>
      {showReminders}
      <Flex justify="space-between" align="center" className={styles.header}>
        <Flex gap={5} align="center">
          <Link to="/">
            <LogoSVG
              viewBox="0 0 278 39"
              style={{ width: '139', height: '39' }}
            />
          </Link>
          <div className={styles.menuContainer}>
            <NavMenu
              selectedKeys={menuStore.selectedKeys || []}
              items={[
                {
                  key: 'solution',
                  name: <Link to={'/solution'}>解决方案</Link>,
                  children: [
                    { key: 'ai-machine', name: 'AI一体机' },
                    { key: 'software', name: '软件' },
                    { key: 'saas', name: 'SAAS' },
                  ],
                },
                {
                  key: 'brand',
                  name: '品牌专区',
                  disabled: true,
                  children: brandsMenu,
                },
                {
                  key: 'yellow-pages-products',
                  name: <Link to="/yellow-pages-products">展示区</Link>,
                  block: true,
                  children: categoriesStore.categroiesMenu,
                },
                {
                  key: 'vnetzone',
                  name: <span className={styles.vnetzone}>世纪互联专区</span>,
                },
              ]}
              onSelect={handleSelect}
            />
          </div>
        </Flex>
        <Flex justify="space-between" align="center" gap={30}>
          <div
            className={styles.input}
            onClick={(e) => {
              e.preventDefault();
              menuStore.selectedKeys = ['search'];
              navigate('/search');
            }}
          >
            <SearchSVG />
            搜索您需要的云产品
          </div>
          <div>
            <Button
              onClick={() => {
                menuStore.selectedKeys = ['help'];
                navigate('/help');
              }}
              type="text"
            >
              帮助中心
            </Button>
          </div>
          <div>
            <Authorize
              error={
                <Space size={'middle'}>
                  <Button
                    onClick={() => {
                      window.open(
                        'https://marketops.vnet.com/runtime/login?appId=app-igqlYxum07',
                      );
                    }}
                    type="primary"
                    ghost
                  >
                    提供商中心
                  </Button>
                  <Button
                    onClick={() => navigate('/register')}
                    type="primary"
                    ghost
                  >
                    注册
                  </Button>
                  <Button onClick={() => navigate('/login')} type="primary">
                    登录
                  </Button>
                </Space>
              }
            >
              <Flex gap={20} justify="space-around" align="center">
                <Button
                  size="small"
                  type="text"
                  onClick={() => navigate('/enterprise')}
                  icon={<EnterpriseSVG />}
                  className={styles.text}
                >
                  企业中心
                </Button>
                {user?.IsRegistEntity && (
                  <Badge count={count} size="small" offset={[-3, 3]}>
                    <Button
                      size="small"
                      type="text"
                      icon={<CartSVG />}
                      onClick={() => navigate('/cart')}
                      className={styles.text}
                    >
                      购物车
                    </Button>
                  </Badge>
                )}
                <Popover
                  placement="bottomRight"
                  title={null}
                  content={
                    <div>
                      <div>公司：{user?.CompanyName}</div>
                      <div>邮箱：{user?.Email}</div>
                      <div>姓名：{user?.FullName}</div>
                      <Divider style={{ margin: '8px 0' }} />
                      <Button type="link" onClick={userLogout} block>
                        注销
                      </Button>
                    </div>
                  }
                >
                  <Avatar
                    alt={user?.FullName}
                    style={{ backgroundColor: '#1064E3' }}
                  >
                    {user?.FullName?.slice(0, 1)}
                  </Avatar>
                </Popover>
              </Flex>
            </Authorize>
          </div>
        </Flex>
      </Flex>
    </div>
  );
};

export default Header;
