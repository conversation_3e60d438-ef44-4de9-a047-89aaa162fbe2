import { Col, Empty, Row } from 'antd';
import ProductItem, { ProductItemProps } from './Item';

interface ProductListProps {
  list: ProductItemProps['detail'][];
  type: 'solution' | 'brand' | 'yellow-pages-products';
  tag: 'online' | 'consultation';
}

const ProductList: React.FC<ProductListProps> = (props) => {
  const { list, type, tag } = props;
  if (list.length === 0) return <Empty />;
  return (
    <Row gutter={[20, 20]}>
      {list.map((item, index) => (
        <Col span={6} key={index}>
          <ProductItem detail={item} type={type} tag={tag} />
        </Col>
      ))}
    </Row>
  );
};

export default ProductList;
