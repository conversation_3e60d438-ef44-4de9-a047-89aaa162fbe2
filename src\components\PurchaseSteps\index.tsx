import {
  CheckOutlined
} from '@ant-design/icons';
import { ReactComponent as PaymentSvg } from '@/assets/payment.svg';
import { ReactComponent as PurchaseSvg } from '@/assets/purchase.svg';
import { Space, Steps } from 'antd';
interface IPurchaseSteps {
  stepValue: number;
}
const PurchaseSteps: React.FC<IPurchaseSteps> = (props) => {
  const { stepValue } = props;

  const finishIconStyle = {
    backgroundColor: '#F1F4FD',
    borderRadius: '50%',
    padding: '8px',
    color: '#114BED',
  };
  const processIconStyle = {
    backgroundColor: '#114BED',
    borderRadius: '50%',
    padding: '8px',
    color: '#fff',
  };
  const waitIconStyle = {
    backgroundColor: '#E5E5E5',
    borderRadius: '50%',
    padding: '8px',
    color: '#fff'
  };
  const getIconStyle = (step: number) => {
    if (step === stepValue) {
      return processIconStyle;
    } else if (step > stepValue) {
      return waitIconStyle;
    } else {
      return finishIconStyle;
    }
  };
  const getIcon =(step:number) =>{
    if (step === stepValue) {
      return processIconStyle;
    } else if (step > stepValue) {
      return waitIconStyle;
    } else {
      return finishIconStyle;
    }
  }
  const stepItem = [
    {
      icon: <CheckOutlined style={getIconStyle(0)} />,
      title: '选择商品',
    },
    {
      icon: stepValue ===2?<CheckOutlined style={getIconStyle(1)} />:<PurchaseSvg style={getIconStyle(1)} />,
      title: '确认订单',
    },
    {
      icon: <PaymentSvg style={getIconStyle(2)} />,
      title: '支付',
    },
  ];

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Steps current={stepValue} labelPlacement="vertical" items={stepItem} />
    </Space.Compact>
  );
};

export default PurchaseSteps;
