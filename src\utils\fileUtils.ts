export function dataToBlob(data: any): Blob {
  const [header, base64] = data.split(',');
  const mimeType = header.match(/:(.*?);/)[1];

  // Base64 解码为二进制数据
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);

  return new Blob([byteArray], { type: mimeType });
}

export function getFileNameFromPath(path: string): string {
  const decodeName = decodeURIComponent(decodeURI(path));

  return decodeName.substring(decodeName.lastIndexOf('/') + 1).split('_')[0];
}
