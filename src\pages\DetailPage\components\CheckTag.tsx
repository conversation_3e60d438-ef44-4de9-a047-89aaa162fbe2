// src/components/ColorfulTag.tsx
import { Col, Row } from 'antd';
import { createStyles, cx } from 'antd-style';
import React from 'react';
import { SolutionEditionAndPricing } from '../services';

interface CheckTagProps {
  onClick: (id: number, item: SolutionEditionAndPricing) => void;
  item: SolutionEditionAndPricing;
  isSelected: boolean;
  disabled?: boolean;
  align?: 'center' | 'left' | 'right';
}

const useStyles = createStyles(
  ({ css }, props: { isSelected: boolean; align: string }) => ({
    tag: css`
      position: relative;
      cursor: pointer;
      display: block;
      width: 100%;
      line-height: 38px;
      height: 38px;
      text-align: ${props.align};
      padding: 0 15px;
      box-shadow: ${props.isSelected
        ? '0 2px 6px rgba(45, 140, 240, 0.1)'
        : '0 1px 2px rgba(0, 0, 0, 0.05)'};
      border-radius: 4px;
      border: solid 1px ${props.isSelected ? '#2D8CF0' : '#e0e0e0'};
      background-color: ${props.isSelected ? '#f0f7ff' : '#fff'};
      font-size: 13px;
      color: ${props.isSelected ? '#2D8CF0' : '#333'};
      transition: all 0.3s ease-in-out;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.disabled {
        color: rgba(166, 166, 166, 1);
        border: 1px solid rgba(166, 166, 166, 1);
        background-color: rgba(166, 166, 166, 0.1);
        cursor: not-allowed;
      }
      :hover:not(.disabled) {
        border-color: #a3d0ff;
        background-color: #f5faff;
        z-index: 10;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
      }
      :after {
        content: '';
        position: absolute;
        right: -1px;
        bottom: -1px;
        width: 14px;
        height: 14px;
        border-radius: 5px 0px 5px 0px;
        background-color: #2d8cf0;
        background-image: url(${require('@/assets/checkedBg.svg').default});
        background-repeat: no-repeat;
        background-position: center;
        opacity: ${props.isSelected ? 1 : 0};
        transition: all 0.3s ease-in-out;
      }
      &.disabled:after {
        background-color: rgba(166, 166, 166, 1);
      }
    `,
  }),
);

const CheckTag: React.FC<CheckTagProps> = ({
  onClick,
  item,
  isSelected,
  disabled,
  align = 'center',
}) => {
  const { styles } = useStyles({ isSelected, align });
  return (
    <span
      className={cx(styles.tag, disabled && 'disabled')}
      onClick={() => onClick(item.Id, item)}
    >
      {item.Name}
    </span>
  );
};

const CheckTagGroup: React.FC<{
  items: SolutionEditionAndPricing[];
  onClick: (key: number, item: SolutionEditionAndPricing) => void;
  value: number;
  type?: 'grid' | 'list';
  disabled?: boolean;
}> = ({ items, onClick, value, type = 'grid', disabled }) => {
  const handleClick = (key: number) => {
    onClick(
      key,
      items.find((item) => item.Id === key) as SolutionEditionAndPricing,
    );
  };

  const span = type === 'grid' ? 8 : 20;

  return (
    <Row gutter={[20, 20]} style={{ flex: 1 }}>
      {items.map((item) => (
        <Col key={item.Id} span={span}>
          <CheckTag
            disabled={disabled}
            key={item.Id}
            item={item}
            onClick={handleClick}
            isSelected={value === item.Id}
            align={type === 'list' ? 'left' : 'center'}
          />
        </Col>
      ))}
    </Row>
  );
};

export { CheckTag };

export default CheckTagGroup;
