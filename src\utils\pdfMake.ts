import pdfMake from 'pdfmake-support-chinese-fonts/pdfmake.min';
import pdfFonts from 'pdfmake-support-chinese-fonts/vfs_fonts';
export function createAndDownload(data: any, fileName: string) {
  console.log("createAndDownload",data)
  pdfMake.vfs = pdfFonts.pdfMake.vfs;
  pdfMake.fonts = {
    Roboto: {
      normal: 'Roboto-Regular.ttf',
      bold: 'Roboto-Regular.ttf',
      italics: 'Roboto-Regular.ttf',
      bolditalics: 'Roboto-Regular.ttf',
    },
    fangzhen: {
      normal: 'fzhei-jt.TTF',
      bold: 'fzhei-jt.TTF',
      italics: 'fzhei-jt.TTF',
      bolditalics: 'fzhei-jt.TTF',
    },
  };
  return pdfMake.createPdf(data).download(fileName);
}

export function getPdfPreviewUrl(data: any) {
    pdfMake.vfs = pdfFonts.pdfMake.vfs;
    pdfMake.fonts = {
      Roboto: {
        normal: 'Roboto-Regular.ttf',
        bold: 'Roboto-Regular.ttf',
        italics: 'Roboto-Regular.ttf',
        bolditalics: 'Roboto-Regular.ttf',
      },
      fangzhen: {
        normal: 'fzhei-jt.TTF',
        bold: 'fzhei-jt.TTF',
        italics: 'fzhei-jt.TTF',
        bolditalics: 'fzhei-jt.TTF',
      },
    };
    const pdfDocGenerator = pdfMake.createPdf(data);
    console.log("pdfDocGenerator",pdfDocGenerator)
    return new Promise((resolve, reject) => {
      pdfDocGenerator.getDataUrl((dataUrl:any) => {
        if (dataUrl) {
          resolve(dataUrl);
        } else {
          reject('');
        }
      });
    });
}
  