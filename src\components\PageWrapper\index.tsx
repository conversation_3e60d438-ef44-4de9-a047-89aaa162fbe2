import { createStyles } from 'antd-style';
import { PropsWithChildren } from 'react';

const useStyles = createStyles(
  ({}, props: { style?: React.CSSProperties }) => ({
    pageWrapper: {
      position: 'relative',
      width: 1200,
      minHeight: 'calc(100vh - 340px)', // 48px是header的高度
      margin: '0 auto',
      padding: '25px 34px',
      boxSizing: 'border-box',
      borderRadius: 18,
      backgroundColor: '#fff',
      ...props.style,
    },
  }),
);

interface IPageWrapper {
  style?: React.CSSProperties;
}
const PageWrapper: React.FC<IPageWrapper & PropsWithChildren> = ({
  style: customStyle,
  children,
}) => {
  const { styles } = useStyles({ style: customStyle });
  return <div className={styles.pageWrapper}>{children}</div>;
};

export default PageWrapper;
