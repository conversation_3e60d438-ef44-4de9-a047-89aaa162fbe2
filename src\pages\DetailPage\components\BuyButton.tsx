import { Button, Flex } from 'antd';
import { createStyles } from 'antd-style';

interface BuyButtonProps {
  isLimitPurchase?: boolean;
  handleAddToCart: () => void;
  handleBuyNow: () => void;
  cartLoading: boolean;
  disableSelected: boolean;
  buyLoading: boolean;
}

const useStyles = createStyles(({ css, prefixCls }) => ({
  addCartBtn: css`
    &.${prefixCls}-btn-color-primary.${prefixCls}-btn-background-ghost {
      border-color: #2d8cf0;
      color: #2d8cf0;
    }
    &.${prefixCls}-btn-color-primary.${prefixCls}-btn-background-ghost:disabled {
      color: rgba(56, 56, 56, 0.25);
      border-color: #d9d9d9;
    }
  `,
  buyNow: css`
    &.${prefixCls}-btn-primary:not([disabled]):not(
        .${prefixCls}-btn-dangerous
      ) {
      > span {
        position: relative;
      }

      &::before {
        content: '';
        background: linear-gradient(
          139.65deg,
          rgba(45, 48, 238, 0.99) 0%,
          #2d8cf0 100%
        );
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        opacity: 0;
      }
    }
  `,
}));

const BuyButton: React.FC<BuyButtonProps> = ({
  isLimitPurchase,
  handleAddToCart,
  handleBuyNow,
  cartLoading,
  disableSelected = false,
  buyLoading = false,
}) => {
  const { styles } = useStyles();
  return (
    <div>
      {isLimitPurchase && (
        <Flex
          justify="flex-end"
          align="center"
          style={{
            color: 'rgba(125, 125, 125, 1)',
            fontWeight: 500,
            fontSize: 16,
          }}
        >
          如有购买意向，请咨询{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Flex>
      )}
      {isLimitPurchase ? null : (
        <Flex justify="flex-end" align="center" gap={20}>
          <Button
            onClick={handleAddToCart}
            loading={cartLoading}
            type="primary"
            disabled={disableSelected}
            size="large"
            ghost
            className={styles.addCartBtn}
          >
            加入购物车
          </Button>
          <Button
            disabled={disableSelected}
            onClick={handleBuyNow}
            type="primary"
            loading={buyLoading}
            className={styles.buyNow}
            size="large"
          >
            立即购买
          </Button>
        </Flex>
      )}
    </div>
  );
};
export default BuyButton;
