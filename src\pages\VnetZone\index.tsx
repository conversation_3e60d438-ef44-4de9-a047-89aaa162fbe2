import useRouteKey from '@/hooks/useRouteKey';
import { Outlet, useNavigate } from '@umijs/max';
import { Flex, Tabs } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ prefixCls, css }) => ({
  container: css`
    padding-top: 30px;
    padding-bottom: 50px;
    min-height: calc(100vh - 400px);
  `,
  list: css`
    width: 1200px;
    margin: 0 auto;
  `,
}));

const VnetZone = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const routeKey = useRouteKey();

  const handleChangeTabs = (key: string) => {
    navigate(`/vnetzone/${key}`);
  };

  return (
    <div className={styles.container}>
      <Flex vertical gap={50}>
        <div className={styles.list}>
          <Tabs
            centered
            activeKey={routeKey}
            items={[
              {
                label: '在线购买',
                key: 'online',
              },
              {
                label: '咨询专属',
                key: 'consultation',
              },
            ]}
            onChange={handleChangeTabs}
          />
          <div>
            <Outlet />
          </div>
        </div>
      </Flex>
    </div>
  );
};

export default VnetZone;
