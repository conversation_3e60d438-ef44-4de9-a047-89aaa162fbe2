import { ReactComponent as LogoSVG } from '@/assets/logo.svg';
import { Link } from '@umijs/max';
import { useSafeState } from 'ahooks';
import { Flex } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';

const useStyles = createStyles(({ css }, props: { isScroll: boolean }) => ({
  container: css`
    min-width: 1280px;
    position: sticky;
    top: 0;
    background: rgba(255, 255, 255, ${props.isScroll ? 1 : 0.5});
    width: 100%;
    height: 48px;
    padding: 0 20px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
  `,
}));

const Header = () => {
  const [affixed, setAffixed] = useSafeState(false);

  const { styles } = useStyles({ isScroll: affixed });

  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 0) {
        setAffixed(true);
      } else {
        setAffixed(false);
      }
    });
    return () => {
      window.removeEventListener('scroll', () => {});
    };
  }, []);
  return (
    <Flex className={styles.container} justify="space-between" align="center">
      <Flex gap={5} align="center">
        <Link to="/">
          <LogoSVG
            viewBox="0 0 278 39"
            style={{ width: '139', height: '39' }}
          />
        </Link>
      </Flex>
    </Flex>
  );
};

export default Header;
