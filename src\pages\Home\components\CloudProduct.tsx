import ErrorImage from '@/components/ErrorImage';
import { Link } from '@umijs/max';
import { <PERSON><PERSON>, <PERSON>, Col, Flex, Row, Skeleton, Typography } from 'antd';
import { createStyles } from 'antd-style';
import useFeature from '../hooks/useFeature';

const useStyles = createStyles(({ prefixCls, css }) => ({
  container: css`
    width: 1200px;
    margin: 0 auto;
    padding: 100px 0 100px;
  `,
  title: css`
    margin-bottom: 60px;
    text-align: center;
    h2.${prefixCls}-typography {
      color: rgba(56, 56, 56, 1)
    }
    div.${prefixCls}-typography {
      color: rgba(166, 166, 166, 1);
    }
  }
  `,
  card: css`
    width: 270px;
    height: 360px;
    padding: 16px;
    border-radius: 8px;
    bacground: #fff;
    box-shadow: 0px 2px 16px rgba(0, 63, 255, 0.1);
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    &:hover {
      box-shadow: 0px 2px 4px rgba(255, 255, 255, 0.5),
        0px 2px 8px rgba(36, 109, 227, 0.18),
        0px 2px 20px rgba(36, 109, 227, 0.18);
      transform: translateY(-4px);
    }
  `,
  image: css`
    margin-bottom: 0;
    width: 238px;
    height: 180px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  `,
  content: css`
    width: 100%;
    height: 112px;
    margin-top: 10px;
  `,
  name: css`
    width:100%;
    .ant-typography:first-child {
      font-size: 16px;
      color: rgba(59, 59, 59, 1);
      height: 47px;
      margin-bottom: 7px;
    }
    .ant-typography:last-child {
      color: rgba(166, 166, 166, 1);
      height: 41px;
      margin-bottom: 22px;
     }
    }
  `,
  description: css`
    &.${prefixCls}-typography {
      font-size: 14px;
      color: rgba(166, 166, 166, 1);
    }
  `,
}));
const CloudProduct = () => {
  const { styles } = useStyles();
  const { featureList } = useFeature();
  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <Typography.Title level={2}>主打云产品</Typography.Title>
        <Typography.Paragraph>various cloud services</Typography.Paragraph>
      </div>

      <Row gutter={[16, 16]}>
        {featureList
          ? featureList?.map((item, index) => (
              <Col span={6} key={index}>
                <Link to={`/solution/detail/${item.GlobalId}`}>
                  <Flex vertical className={styles.card}>
                    <figure className={styles.image}>
                      <ErrorImage alt={item.Name} src={item.ImageUrl} />
                    </figure>

                    <Flex
                      vertical
                      align="flex-end"
                      justify="space-between"
                      className={styles.content}
                    >
                      <div className={styles.name}>
                        <Typography.Paragraph
                          strong
                          ellipsis={{ rows: 2 }}
                          title={item.Name}
                        >
                          {item.Name}
                        </Typography.Paragraph>
                        <Typography.Paragraph
                          className={styles.description}
                          ellipsis={{ rows: 2 }}
                        >
                          {item.ShortDescription}
                        </Typography.Paragraph>
                      </div>
                      <Button type="link" size="small">
                        查看更多
                      </Button>
                    </Flex>
                  </Flex>
                </Link>
              </Col>
            ))
          : Array.from({ length: 4 }).map((item, index) => (
              <Col span={6} key={index}>
                <Card
                  hoverable
                  style={{ width: 285 }}
                  className={styles.card}
                  cover={<Skeleton active />}
                >
                  <Flex vertical align="flex-end" justify="space-between">
                    <Skeleton active />
                  </Flex>
                </Card>
              </Col>
            ))}
      </Row>
    </div>
  );
};

export default CloudProduct;
