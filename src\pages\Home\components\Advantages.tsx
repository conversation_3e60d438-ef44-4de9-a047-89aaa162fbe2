import bg from '@/assets/advantageBg.png';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { Col, Row, Typography } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  advantageBg: css`
    height: 600px;
    background: url(${bg}) no-repeat center;
  `,
  container: css`
    width: 1200px;
    margin: 0 auto;
    overflow: hidden;
  `,
  title: css`
    margin-top: 100px;
    margin-bottom: -20px;
    text-align: center;
    h3.antd-typography {
      color: rgba(56, 56, 56, 1);
    }
  `,
  description: css`
    margin-top: -60px;
    text-align: center;
    h4.ant-typography {
      color: rgba(66, 66, 66, 1);
    }
    div.ant-typography {
      color: rgba(128, 128, 128, 1);
    }
  `,
}));
const Advantages = () => {
  const { styles } = useStyles();

  // 使用自定义的滚动动画hooks
  const container = useScrollAnimation({
    selector: '.product-card',
    initialY: 50,
    initialOpacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: 'power2.out',
    start: 'top 85%',
    once: true,
  });

  return (
    <div className={styles.advantageBg}>
      <div className={styles.container}>
        <div className={styles.title}>
          <Typography.Title level={3}>平台优势</Typography.Title>
        </div>
        <Row justify={'space-around'} ref={container}>
          <Col span={8} style={{ textAlign: 'center', width: 380 }} className='product-card'>
            <img
              width={203}
              height={271}
              src={require('@/assets/adv_icon1.png')}
            />
            <div className={styles.description}>
              <Typography.Title level={4}>优选云方案</Typography.Title>
              <Typography.Paragraph>
                汇聚云时代丰富的实践和热销方案
              </Typography.Paragraph>
            </div>
          </Col>
          <Col
            span={8}
            style={{ textAlign: 'center', width: 380, paddingTop: 20 }}
            className='product-card'
          >
            <img width={269} src={require('@/assets/adv_icon2.png')} />
            <div className={styles.description}>
              <Typography.Title level={4}> 一站式交付</Typography.Title>
              <Typography.Paragraph>
                自动化流程管理，零距离交付和服务
              </Typography.Paragraph>
            </div>
          </Col>
          <Col span={8} style={{ textAlign: 'center', width: 380 }} className='product-card'>
            <img
              width={203}
              height={271}
              src={require('@/assets/adv_icon3.png')}
            />
            <div className={styles.description}>
              <Typography.Title level={4}> 融合新渠道</Typography.Title>
              <Typography.Paragraph>
                助力云产品分销共赢的渠道引擎
              </Typography.Paragraph>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Advantages;
