import { useState } from 'react';
import { SolutionEditionAndPricing } from '../services';

function useProductInfo() {
  const [areaKey, setAreaKey] = useState<number>();
  const [planKey, setPlanKey] = useState<number>();
  const [plan, setPlan] = useState<SolutionEditionAndPricing>();
  const [count, setCount] = useState<number>(1);

  const handleSelectedInfo = (
    type: 'area' | 'plan' | 'count',
    value: number,
    plan?: SolutionEditionAndPricing,
  ) => {
    if (type === 'area') {
      setAreaKey(value);
    } else if (type === 'plan') {
      setPlanKey(value);
      setPlan(plan);
    } else if (type === 'count') {
      setCount(value);
    }
  };

  return {
    areaKey,
    planKey,
    plan,
    count,
    handleSelectedInfo,
  };
}

export default useProductInfo;
