import { CloseOutlined } from '@ant-design/icons';
import { Link } from '@umijs/max';
import { Alert } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ prefixCls, css }) => ({
  reminders: css`
    &.${prefixCls}-alert {
      position: fixed;
      top: 50px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      width: 600px;
      height: 56px;
      padding: 0 32px;
      background: rgba(11, 72, 235, 0.7);
      color: #fff;
      border-radius: 28px;
      font-size: 16px;
    }
  `,
}));

const Reminders = () => {
  const { styles } = useStyles();

  return (
    <Alert
      className={styles.reminders}
      message="您需要提交企业信息和资质文件，完成企业认证"
      type="info"
      closeIcon={<CloseOutlined style={{ color: '#fff', fontSize: '14px' }} />}
      action={
        <Link
          to={'/enterprise/info'}
          style={{ color: '#fff', marginRight: 30 }}
        >
          立即申请 &gt;&gt;
        </Link>
      }
    />
  );
};

export default Reminders;
