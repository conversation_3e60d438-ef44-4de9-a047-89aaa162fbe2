import workerUrl from '@/assets/js/pdf.worker.min.js';
import { getBillDetail } from '@/services/order/PurchaseController';
import { OrderModel } from '@/types';
import { digitUppercase } from '@/utils/currencyUtil';
import { createAndDownload } from '@/utils/pdfMake';
import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { Button, Modal } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
interface IProtocol {
  confirmCallBack: (billNumber: any) => void;
}
const Protocol = forwardRef((props: IProtocol, ref: React.Ref<any>) => {
  const { confirmCallBack } = props;
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [protocolType, setProtocolType] = useState<number>(1);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [contractId, setContractId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [billNumber, setBillNumber] = useState<string>('');
  useImperativeHandle(ref, () => ({
    modalHandleShow,
  }));
  function calculateSalesTax(salesPriceIncludingTax: any, taxRate: any) {
    let temTaxRate = taxRate / 100;
    const salesTax =
      Math.round(
        (salesPriceIncludingTax / (1 + temTaxRate)) * temTaxRate * 100,
      ) / 100;
    return salesTax;
  }
  const getOrders = (data: OrderModel, isMsp = false) => {
    const filterItems = data.OrderItems.filter((item) =>
      isMsp ? item.EnumType === 2 : item.EnumType !== 2,
    );
    const list = filterItems.map((item) => [
      item.SolutionName,
      item.SolutionAttributeCombinationName,
      item.Quantity,
      item.UnitPrice,
      item.TaxRatePercent,
      item.Total,
    ]);
    const reducer = (accumulator = 0, currentValue = 0) =>
      accumulator + currentValue;
    const total = data.OrderTotal;
    const taxAmount =
      filterItems && filterItems.length > 0
        ? filterItems
            .map((item) => +calculateSalesTax(item.Total, item.TaxRatePercent))
            .reduce(reducer)
        : 0;

    const actualAmount = ((total * 100 - taxAmount * 100) / 100).toFixed(2);
    let dist: any = [];
    dist = dist
      .concat([
        ['解决方案', '名称（物料）', '数量', '含税单价￥', '税率%', '总金额￥'],
      ])
      .concat(list)
      .concat([
        [
          {
            colSpan: 6,
            text: `总计（含税总金额）：￥${total}  大写（${digitUppercase(
              total,
            )}）,其中不含税总金额￥${actualAmount}，税额￥${taxAmount}`,
            alignment: 'left',
          },
        ],
      ]);
    return {
      marginTop: 20,
      marginBottom: 20,
      table: {
        widths: [160, 160, 40, 50, 40, 40],
        heights: 20,
        body: dist,
      },
    };
  };
  const getDefalutProtocol = (data: OrderModel) => {
    const billNumber = 'ddddd';
    const billDate = 'ddddd';
    const totalAmount = 'ddddd';
    const paymentAmount = 'ddddd';
    return {
      pageSize: 'A4',
      pageMargins: [40, 40, 40, 40],
      defaultStyle: {
        font: 'fangzhen',
        fontSize: 10,
        lineHeight: 1.2,
      },
      content: [
        {
          columns: [
            {
              width: '*',
              stack: [
                {
                  text: '世纪互联',
                  style: 'header',
                  margin: [15, 5, 0, 20],
                },
                {
                  text: [
                    '上海蓝云网络科技有限公司\n',
                    '上海浦东新区科苑路88号德国中心2-701[058A]\n',
                    '税务登记号:9131011506375343XR',
                  ],
                  style: 'companyInfo',
                },
                {
                  text: '服务对象',
                  style: 'sectionTitle',
                  margin: [0, 20, 0, 10],
                },
                {
                  text: '苏州盖雅信息技术有限公司',
                  style: 'infoText',
                  margin: [0, 0, 0, 15],
                },

                { text: '付款方', style: 'sectionTitle', margin: [0, 0, 0, 5] },
                { text: '上海南洋万邦软件技术有限公司', style: 'infoText' },
              ],
            },

            // 右侧列 - 账单汇总
            {
              width: '*',
              stack: [
                {
                  text: '世纪互联云市场-账单摘要',
                  style: 'subheader',
                  alignment: 'right',
                  margin: [0, 5, 0, 20],
                },
                {
                  text: '账单汇总',
                  style: 'sectionTitle',
                  margin: [0, 0, 0, 5],
                },
                {
                  table: {
                    widths: ['*'],
                    body: [
                      [
                        {
                          text: '',
                          border: [false, true, false, false],
                          borderColor: '#dddddd',
                          borderWidth: 0.5,
                        },
                      ],
                    ],
                  },
                  margin: [0, 0, 0, 8],
                },
                {
                  table: {
                    widths: ['auto', '*'],
                    body: [
                      [
                        { text: '账单信息', style: 'billLabel' },
                        {
                          text: '上海南洋万邦软件技术有限公司',
                          alignment: 'right',
                        },
                      ],
                      [
                        { text: '账单编号', style: 'billLabel' },
                        { text: billNumber, alignment: 'right' },
                      ],
                      [
                        { text: '账单日期', style: 'billLabel' },
                        { text: billDate, alignment: 'right' },
                      ],
                      [
                        { text: '付款周期', style: 'billLabel' },
                        { text: '30天', alignment: 'right' },
                      ],
                      [
                        {
                          text: '含税总金额',
                          style: 'billLabel',
                          fillColor: '#f9fdf0',
                        },
                        {
                          text: totalAmount,
                          alignment: 'right',
                          fillColor: '#f9fdf0',
                        },
                      ],
                      [
                        {
                          text: '付款到期日',
                          style: 'billLabel',
                          fillColor: '#f9fdf0',
                        },
                        {
                          text: '2025/04/19',
                          alignment: 'right',
                          fillColor: '#f9fdf0',
                        },
                      ],
                    ],
                  },
                  layout: {
                    defaultBorder: false,
                    hLineWidth: function (i: any) {
                      return i === 0 ? 1 : 0; // 仅第一行上方显示边框
                    },
                    hLineColor: '#eeeeee',
                    paddingTop: function (i: any) {
                      return i === 0 ? 0 : 5;
                    },
                    paddingBottom: function (i: any, node: any) {
                      return i === node.table.body.length - 1 ? 0 : 5;
                    },
                  },
                },
              ],
            },
          ],
          columnGap: 20,
          margin: [0, 0, 0, 15],
        },

        // 付款信息
        {
          stack: [
            { text: '付款信息', style: 'paymentTitle' },
            {
              text: `请于2025/04/19之前通过电子转账付款支付${paymentAmount}。`,
            },
            {
              text: [
                '开户银行：中国工商银行朝阳国航大厦支行\n',
                '账户名称：上海蓝云网络科技有限公司\n',
                '银行账号：0200227919200053244',
              ],
              margin: [0, 8, 0, 0],
            },
          ],
          style: 'paymentBox',
          margin: [0, 0, 0, 15],
        },

        {
          text: '请您在完成转账后，前往世纪互联云市场账单管理页面，核对并确认付款信息。',
          style: 'noteText',
          margin: [0, 0, 0, 15],
        },

        // 账单详情
        {
          text: '账单详情',
          style: 'sectionTitle',
          margin: [0, 0, 0, 5],
        },
        {
          table: {
            widths: ['auto', '*', 'auto', 'auto', 'auto', 'auto', 'auto'],
            body: [
              [
                { text: '订单编号', style: 'tableHeader' },
                { text: '商品名称', style: 'tableHeader' },
                { text: '商品规格', style: 'tableHeader' },
                { text: '单价(含税)', style: 'tableHeader' },
                { text: '税率', style: 'tableHeader' },
                { text: '数量', style: 'tableHeader' },
                { text: '金额(含税)', style: 'tableHeader' },
              ],
              [
                '2025032055738',
                'xx安全解决方案',
                '标准版',
                '¥1000',
                '6%',
                '6',
                '¥6000',
              ],
              [
                '2025032055738',
                'xx数据备份服务',
                '标准版',
                '¥20000',
                '13%',
                '1',
                '¥20000',
              ],
              [
                { text: '含税总金额', colSpan: 6, style: 'totalLabel' },
                {},
                {},
                {},
                {},
                {},
                { text: '¥26,000.00', style: 'totalAmount' },
              ],
            ],
          },
          layout: {
            fillColor: function (rowIndex: any) {
              if (rowIndex === 0) return '#f0f7ff';
              if (rowIndex === 3) return '#f9fdf0';
              return null;
            },
            hLineWidth: function (i: any, node: any) {
              if (i === 0 || i === node.table.body.length) return 0;
              return 1;
            },
            vLineWidth: function () {
              return 0;
            },
            hLineColor: function (i: any, node: any) {
              if (i === 1 || i === node.table.body.length) return '#cccccc';
              return '#dddddd';
            },
            paddingTop: function (i: any) {
              return 5;
            },
            paddingBottom: function () {
              return 5;
            },
          },
        },

        // 底部说明
        {
          text: [
            '查看账单明细，请前往',
            {
              text: '世纪互联云市场',
              link: '',
              color: 'blue',
              decoration: 'underline',
            },
            '，请参考订单编号进行查询。',
          ],
          style: 'footerText',
          margin: [0, 15, 0, 0],
        },
        {
          text: [
            '这是一段文字，',
            {
              text: '点击这里',
              link: 'https://marketplace.vnet.com',
              color: 'blue',
              decoration: 'underline',
            },
            '可以跳转到示例网站。',
          ],
        },
        {
          text: '联系电话',
          link: 'http://localhost:8000/enterprise/bill',
          color: 'blue',
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          color: '#154077',
        },
        subheader: {
          fontSize: 14,
          bold: true,
        },
        companyInfo: {
          fontSize: 10,
          lineHeight: 1.4,
          Color: '#5E5E5E',
        },
        sectionTitle: {
          fontSize: 12,
          bold: true,
        },
        infoText: {
          fontSize: 11,
        },
        billHeader: {
          bold: true,
          fontSize: 11,
          border: [0, 0.5, 0, 0],
          borderColor: '#d8e8c9',
          padding: 10,
        },
        billLabel: {
          bold: true,
          fontSize: 11,
        },
        paymentTitle: {
          bold: true,
          fontSize: 12,
        },
        paymentBox: {
          fontSize: 11,
          border: [1, 1, 1, 1],
          borderColor: '#d8e8c9',
          borderRadius: 4,
          padding: 10,
        },
        noteText: {
          fontSize: 11,
        },
        tableHeader: {
          bold: true,
          fontSize: 10,
        },
        totalLabel: {
          bold: true,
          fontSize: 11,
        },
        totalAmount: {
          bold: true,
          fontSize: 11,
        },
        footerText: {
          fontSize: 10,
          color: '#666666',
        },
      },
    };
  };
  const getProtocolDef = (data: any) => {
    return getDefalutProtocol(data);
  };
  const dowloadClick = () => {
    let data: any = {
      OrderItems: [
        {
          SolutionName: 'Test',
        },
      ],
    };
    let result = getProtocolDef(data);
    createAndDownload(result, 'test.pdf');
  };

  const modalHandleShow = (type: number, billNumber: any) => {
    setProtocolType(type);
    setIsModalOpen(true);
    setBillNumber(billNumber);
    if (type === 2) {
      setLoading(true);
      getBillDetail(billNumber).then((res) => {
        setDownloadUrl(res.PdfUrl);
        setPdfUrl(res.PdfUrl);
        setLoading(false);
      });
    }
  };
  const modalHandleCancel = () => {
    setIsModalOpen(false);
    if (confirmCallBack) {
      confirmCallBack(billNumber);
    }
  };
  const modalFonter = () => (
    <div
      style={{
        borderTop: '1px solid rgba(229, 229, 229, 1)',
        lineHeight: '44px',
        height: '44px',
        paddingLeft: 32,
        paddingRight: 32,
      }}
    >
      {protocolType === 2 && (
        <Button
          target="_blank"
          type="link"
          href={downloadUrl}
          // onClick={dowloadClick}
        >
          下载电子账单
        </Button>
      )}
    </div>
  );
  return (
    <Modal
      destroyOnClose={false}
      title={protocolType === 1 ? '生成电子订单' : '查看电子账单'}
      open={isModalOpen}
      width={'50%'}
      footer={modalFonter}
      onCancel={modalHandleCancel}
      onOk={modalHandleCancel}
      loading={loading}
      styles={{
        content: {
          padding: 0,
        },
        header: {
          padding: 16,
        },
      }}
    >
      <div style={{ height: '60vh', overflow: 'auto', width: '100%' }}>
        {pdfUrl && (
          <Worker workerUrl={workerUrl}>
            <div style={{ width: '100%' }}>
              <Viewer fileUrl={pdfUrl} />
            </div>
          </Worker>
        )}
      </div>
    </Modal>
  );
});

export default Protocol;
