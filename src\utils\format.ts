// 示例方法，没有实际意义
export function trim(str: string) {
  return str.trim();
}
export function formatDate(date: Date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(date.getDate()).padStart(2, '0'); // 保证两位数
  return `${year}-${month}-${day}`;
}
export function formatUtcDate(date: Date) {
  date.setHours(date.getHours() + 8);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(date.getDate()).padStart(2, '0'); // 保证两位数
  return `${year}-${month}-${day}`;
}
export function formatDateTime(date: Date, isUTC = false) {
  if (isUTC) {
    date.setHours(date.getHours() + 8);
  }
  const formattedDate = new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).format(date);
  const finalDate = formattedDate.replace(/\//g, '-').replace(',', '');
  return finalDate;
}
export function formatDuring(timestamp: any) {
  const days = Math.floor(timestamp / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timestamp % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timestamp % (1000 * 60 * 60)) / (1000 * 60));
  // const seconds = Math.floor((timestamp % (1000 * 60)) / 1000);
  return (
    (days ? days + ' 天 ' : '') +
    (hours ? hours + ' 小时 ' : '') +
    minutes +
    ' 分钟'
  );
}
