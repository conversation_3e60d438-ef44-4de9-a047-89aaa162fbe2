// 运行时配置

import { matchRoutes, RequestConfig, RuntimeAntdConfig } from '@umijs/max';
import zhCN from 'antd/locale/zh_CN';
import { requestConfig } from './requestConfig';

export const request: RequestConfig = {
  timeout: 600_000,
  ...requestConfig,
};

export const antd: RuntimeAntdConfig = (memo) => {
  memo.locale ??= zhCN; // 配置 antd 的语言
  memo.theme ??= {
    token: {
      colorPrimary: '#114bed',
      colorInfo: '#114bed',
      colorError: '#ff6d4d',
      colorSuccess: '#43cf7c',
      colorWarning: '#ffc300',
      colorTextBase: '#383838',
    },
    components: {
      Table: {
        headerBg: '#F1F4FD',
      },
      Button: {
        controlHeightLG: 38,
        controlHeightSM: 28,
      },
      InputNumber: {
        borderRadius: 8,
      },
      Input: {
        borderRadius: 8,
      },
      Menu: {
        itemHoverBg: 'rgba(17, 75, 237, 0.1)',
        itemHoverColor: 'rgba(17, 75, 237, 1)',
      },
    },
  };
  // memo.theme.algorithm = theme.darkAlgorithm; // 配置 antd5 的预设 dark 算法

  return memo;
};

export function onRouteChange({ clientRoutes, location }: any) {
  const route = matchRoutes(clientRoutes, location.pathname)?.pop()?.route;
  if (route) {
    window.scrollTo(0, 0);
  }
}
