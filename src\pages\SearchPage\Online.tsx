import { transformToTree } from '@/components/CategoriesTree';
import SearchPageContent from '@/components/SearchPageContent';
import { useCategoriesStore } from '@/store/categroies';
import { useSupplierStore } from '@/store/supplier';
import { useCreation } from 'ahooks';
import { memo } from 'react';
import SearchCard from './components/SearchCard';
import useSearch from './hooks/useSearch';
import { ListResponse, OnlineListItem, searchOnline } from './services';

const Online = () => {
  const { data, loading, searchValues, updateState } =
    useSearch<ListResponse<OnlineListItem>>(searchOnline);
  const supplierStore = useSupplierStore();
  const categoriesStore = useCategoriesStore();

  const supplierOptions = useCreation(() => {
    return supplierStore.list.map((item) => ({ label: item, value: item }));
  }, [supplierStore.list]);

  const categoryTreeData = useCreation(() => {
    return transformToTree(categoriesStore.rawData);
  }, [categoriesStore.rawData]);

  return (
    <SearchPageContent
      to="solution"
      searchPanel={
        <SearchCard
          categoriesTreeData={categoryTreeData}
          supplierOptions={supplierOptions}
          searchValues={searchValues}
          updateSearchValues={updateState}
        />
      }
      searchParams={searchValues}
      updateSearchParams={updateState}
      dataSource={data?.Data || []}
      total={data?.Total || 0}
      loading={loading}
    />
  );
};

export default memo(Online);
