import { request } from '@umijs/max';

export async function vnetCategroies(suppliserName: string) {
  return request('/api/Categories', {
    method: 'GET',
    params: {
      Supplier: suppliserName,
      SolutionAvailable: true,
    },
  });
}

export async function vnetCategroieDisplayed(suppliserName: string) {
  return request('/api/Categories/displayed', {
    method: 'GET',
    params: {
      Supplier: suppliserName,
      SolutionAvailable: true,
    },
  });
}

interface Supplier {
  SupplierId: string;
  SupplierName: string;
  SupplierNumber: string;
  SupplierLogo: string;
  Description: string;
  Status: number;
  CreatedOnUtc: string;
  UpdatedOnUtc: string;
  Id: number;
}

export async function getSupplier(id: string): Promise<Supplier> {
  return request(`/api/Suppliers/${id}`);
}
