import { CretFileModel } from '../solution/solution.model';

export interface OrderModel {
  AssistantEmail: string;
  AssistantName: string;
  Id: number;
  BlueCloudAggrementNo: string;
  PaymentMethodId: number;
  OrderGlobalId: string;
  CustomOrderNumber: string;
  PlatformName: string;
  OrderSubtotal: number;
  OrderTotal: number;
  ReceiveAddress: string;
  BillingPeriodDays: number;
  OrderDiscount: number;
  OrderTotalActual: number;
  OrderStatus: string;
  OrderStatusId: number;
  PaymentPlanId: number;
  PaymentStatus: string;
  PaymentStatusId: number;
  ShippingStatus: string;
  ShippingStatusId: number;
  PaymentMethod: string;
  PaidDate: string;
  OrderItems: OrderDetailItemModel[];
  CreatedOn: string;
  CustomerName: string;
  CustomerEmail: string;
  CustomerPhone: string;
  CustomerCompany: string;
  Product: string;
  BuyUserCompanyType: string;
  BuyUserCompanyName: string;
  BuyUserName: string;
  BuyUserEmail: string;
  BuyUserPhone: string;
  BlueCloudSalesName: string;
  BlueCloudSalesEmail: string;
  BuyUserAddress: string;
  BuyUserRoleNames: string;
  ProductTotal: string;
  ISVOrderNumber: string;
  BuyUserCompanyTypeId: number;
  PictureThumbnailUrl: string;
  UnitPrice: number;
  Quantity: number;
  AttributeInfo: string;
  ProductLicense: string;
  SolutionName: string;
  IsLeads: boolean;
  CompanyType: string;
  Enum: [];
  ResellerGlobalId: string;
  DistributorGlobalId: string;
  HasInvoice: boolean;
  PromotionCode: string;
  Currency: string;
  CretFiles: CretFileModel[];
  Payments: OrderPaymentModel[];
  IsInstallment: boolean;
  OrderDiscounts: OrderDiscountModel[];
  ContractUrl: string;
  ContractUpToDate: boolean;
  PaymentTermDays: number;
  Remark: string;
  PaymentInfoRemark: string;
  ContractId: string;
  ContractStatus: number;
  ContractType: number;
  InvoiceEnabled?: boolean;
  DeliveryMethod: number;
}

export interface OrderDiscountModel {
  Currency: string;
  DiscountAmount: number;
  OrderDiscountType: number;
  Priority: number;
}

export interface OrderPaymentModel {
  ActualPaymentTime: string;
  CreatedOnUtc: string;
  OrderId: number;
  OrderItemId: number;
  PaymentAccountNumber: number;
  PaymentAmount: number;
  PaymentMethods: number;
  PaymentNumber: string;
  PaymentStatus: number;
  PlannedPaymentDate: string;
  SerialNumber: number;
  StageEndOnUtc: string;
  StageStartOnUtc: string;
  StageRemark: string;
}

export interface OrderDetailItemModel {
  AttributeInfo: string;
  Id: number;
  LogoUrl: string;
  OrderItemGlobalId: string;
  ProductLicense: string;
  PurchaseParameter: string;
  Quantity: number;
  Reviewed: boolean;
  SolutionAttributeCombinationId: number;
  SolutionComponents: string;
  SolutionGlobalId: string;
  SolutionId: number;
  SolutionName: string;
  SolutionSku: string;
  SolutionSkuName: string;
  SolutionAttributeCombinationSku: string;
  SolutionAttributeCombinationName: string;
  Total: string;
  UnitPrice: number;
  StageRemark: string;
  EnumStatus: number;
  EnumType: number;
  TaxRatePercent: number;
  AutoCompleteDeadLine: string;
}

export interface OrderParamModel {
  DeliveryMethod: number;
  CustomerId: number;
  PaymentMethod: number;
  IsLeads: boolean;
  OrderItems: OrderItemModel[];
  Region: number;
  Remark: string;
  //EstimatedStatementTime: string;
  PaymentTermDays?: number;
  // CouponCode?: string;
  ChangePrice: boolean;

  // IsInstallment?: boolean;
}

export interface OrderItemModel {
  Quantity: number;
  SolutionId: number;
  EditionId: number;
  PurchaseParameter: string;
}

export interface OrderReviewModel {
  CustomOrderNumber: string;
  OrderItemId: string;
  Rate: number;
  Comment: string;
}

export interface OrderInvoiceModel {
  OrderId: string;
  ApplyDate: string;
  FaPiaoTypeId: number;
  FaPiaoType: string;
  ExpressTypeId: number;
  ExpressType: string;
  FaPiaoPrintInfoRemark: string;
  CustomerTax: string;
  CompanyName: string;
  CompanyAddress: string;
  BillToAddress: string;
  BankAccount: string;
  CustomerOpeningBank: string;
  PhoneLocalNumber: string;
  FaPiaoInfoRemark: string;
  Addressee: string;
  AddresseeCellPhone: string;
  AddresseePhone: string;
  PostCode: string;
  BillToState: string;
  BillToCity: string;
  BillToCounty: string;
  ExpressInfoRemark: string;
  CreatedDate: string;
  Id: number;
  OrderInvoiceStatus: string;
  Amount: number;
}

export interface OrderSearchModel {
  StartDate?: string;
  EndDate?: string;
  OrderStatusId?: [number];
  PaymentStatusId?: [number];
  ShippingStatusId?: number;
  SearchBuyUserCompany?: string;
  SearchBuyUserEmail?: string;
  SearchCustomerCompany?: string;
  SearchOrderNumber?: string;
  SearchText?: string;
  PageIndex: number;
  PageSize: number;
  SortField?: string;
  SortDir?: string;
  Region?: number;
}

export interface OfflineInformationModel {
  PaymentAccountNumber: string;
  PlannedPaymentDate: string;
  OrderNumber: string;
}
export const enum ContractType {
  EPO = 0,
  FaDaDa,
}

export enum OrderDiscountType {
  ModifyPrice = 1,
  PromotionCoupon = 2,
  Incentive = 3,
}

export enum PaymentMethodType {
  Online = 1,
  Offline = 2,
  Contract = 3,
}
