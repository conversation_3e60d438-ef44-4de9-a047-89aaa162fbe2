import { useUserDeriveState } from '@/store/user';
import { transform } from '@/utils/currencyUtil';
import { Link, useLocation } from '@umijs/max';
import { Flex, Typography } from 'antd';
import { createStyles, cx } from 'antd-style';
import { FC, ReactNode } from 'react';
import ErrorImage from '../ErrorImage';

export interface ProductItemProps {
  detail: {
    id: number;
    globalId: string;
    name: string;
    description: string;
    price: ReactNode;
    currency: string;
    image: string;
    supplier: string;
  };
  type: 'solution' | 'brand' | 'yellow-pages-products' | 'vent';
  tag: 'online' | 'consultation';
}

const useStyles = createStyles(({ prefixCls, css }) => ({
  card: css`
    width: 270px;
    max-height: 360px;
    padding: 16px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 16px rgba(0, 63, 255, 0.1);
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    &:hover {
      box-shadow: 0px 2px 4px rgba(255, 255, 255, 0.5),
        0px 2px 8px rgba(36, 109, 227, 0.18),
        0px 2px 20px rgba(36, 109, 227, 0.18);
      transform: translateY(-4px);
    }
  `,
  content: css`
    width: 100%;
    margin-top: 10px;
  `,
  image: css`
    margin-bottom: 0;
    width: 238px;
    height: 180px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  `,
  title: css`
    width: 100%;
    .ant-typography:first-child {
      font-size: 16px;
      color: rgba(59, 59, 59, 1);
      height: 47px;
      margin-bottom: 7px;
    }
    .ant-typography:last-child {
      color: rgb(111, 111, 111);
      height: 41px;
      margin-bottom: 0;
    }
  `,
  price: css`
    font-size: 20px;
    font-weight: 700;
    color: #f76c59;
    .symbol {
      font-size: 18px;
    }
  `,
  supplier: css`
    font-size: 14px;
    font-weight: 400;
    color: rgba(166, 166, 166, 1);
    margin-bottom: 0;
    white-space: nowrap;
  `,
  ellipsis: css`
    overflow: hidden;
    text-overflow: ellipsis;
  `,
  consultation: css`
    font-size: 14px;
    margin-left: auto;
    margin-top: 10px;
    background: linear-gradient(90deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
  `,
}));
const ProductItem: FC<ProductItemProps> = (props) => {
  const {
    detail: { globalId, name, description, price, currency, image, supplier },
    type,
    tag,
  } = props;
  const { styles } = useStyles();
  const location = useLocation();
  const { isLogin, isRegistEntity } = useUserDeriveState();
  return (
    <Link
      to={`/${type}/detail/${globalId}`}
      state={{ from: location.pathname, search: location.search }}
    >
      <Flex
        vertical
        align="flex-end"
        justify="space-between"
        className={styles.card}
      >
        <figure className={styles.image}>
          <ErrorImage alt={name} src={image} />
        </figure>
        <Flex
          vertical
          align="flex-end"
          justify="space-between"
          className={styles.content}
        >
          <div className={styles.title}>
            <Typography.Paragraph strong ellipsis={{ rows: 2 }} title={name}>
              {name}
            </Typography.Paragraph>
            <Typography.Paragraph
              type="secondary"
              ellipsis={{ rows: 2 }}
              title={description}
            >
              {description}
            </Typography.Paragraph>
          </div>
          {tag === 'online' && isLogin && isRegistEntity && (
            <Flex
              justify="space-between"
              align="flex-end"
              style={{ width: '100%', marginTop: 22 }}
            >
              <Typography.Text type="secondary" style={{ fontWeight: 400 }}>
                售价：
              </Typography.Text>
              <span className={styles.price}>
                <span className="symbol">{transform(currency)}</span>
                {price}
              </span>
            </Flex>
          )}
          {tag === 'consultation' ? (
            location.pathname.includes('vnetzone') ? (
              <span className={styles.consultation}>咨询专属</span>
            ) : (
              <Flex style={{ width: '100%', marginTop: 22 }}>
                <span className={styles.supplier}>供应商：</span>
                <span
                  className={cx(styles.supplier, styles.ellipsis)}
                  title={supplier}
                >
                  {supplier}
                </span>
              </Flex>
            )
          ) : null}
        </Flex>
      </Flex>
    </Link>
  );
};

export default ProductItem;
