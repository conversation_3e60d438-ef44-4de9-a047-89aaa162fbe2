import Authorize from '@/components/Authorize';
import ColorTag from '@/components/ColorTag';
import { DeliveryMethod, TAG_COLORS } from '@/constants';
import useUserStore from '@/store/user';
import { transform } from '@/utils/currencyUtil';
import { Link } from '@umijs/max';
import { Flex, Space, Typography } from 'antd';
import { createStyles } from 'antd-style';
import { FC, ReactNode, useState } from 'react';

interface ProductInfoProps {
  supplierId: number;
  title: string;
  delivery: number;
  supplier: string;
  description: string;
  currency: string;
  price: ReactNode;
  categories: string[];
}

const useStyles = createStyles(({ css, prefixCls, token }) => ({
  container: css`
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 20px;
  `,
  title: css`
    &.${prefixCls}-typography {
      font-size: 26px;
      font-weight: 600;
      color: #3b3b3b;
      margin-bottom: 0;
    }
    span {
      font-size: 12px;
    }
  `,
  shortDescription: css`
    &.${prefixCls}-typography {
      margin-bottom: 0;
    }
  `,
  supplier: css`
    &.${prefixCls}-typography {
      margin-bottom: 0;
    }

    &.${prefixCls}-typography span:last-child a {
      color: #0b76dd;
    }
  `,
  price: css`
    margin-top: 20px;
    font-size: 14px;
    color: #666;
    display: inline-block;
  `,
  priceNumber: css`
    display: inline-block;
    span {
      display: inline-block;
      color: #f76c59;
    }
    span:first-child {
      font-size: 16px;
      font-weight: 700;
    }
    span:last-child {
      font-size: 26px;
      font-weight: 700;
    }
  `,
}));

const ProductInfo: FC<ProductInfoProps> = ({
  supplierId,
  title,
  delivery,
  supplier,
  description,
  price,
  currency,
  categories,
}) => {
  const { styles } = useStyles();
  const { user } = useUserStore();
  const [expanded, setExpanded] = useState(false);
  return (
    <Flex className={styles.container}>
      <Flex style={{ flex: 1 }} vertical gap={15}>
        <Typography.Title level={3} className={styles.title}>
          <Flex align="center" gap={10}>
            {title}

            <ColorTag
              textColor="#2D8CF0"
              backgroundColor="#e9f3ff"
              style={{ fontSize: 12, fontWeight: 400 }}
            >
              {
                DeliveryMethod.find(
                  (item) => item.value === delivery.toString(),
                )?.label
              }
            </ColorTag>
          </Flex>
        </Typography.Title>
        <Typography.Paragraph className={styles.supplier}>
          <span>供应商：</span>
          <span>
            <Link
              to={{
                pathname: `/supplier/${supplierId}/online`,
                search: `n=${supplier}`,
              }}
            >
              {supplier}
            </Link>
          </span>
        </Typography.Paragraph>
        <Typography.Paragraph
          ellipsis={{
            rows: 2,
            expandable: 'collapsible',
            expanded,
            onExpand(e, info) {
              setExpanded(info.expanded);
            },
          }}
          className={styles.shortDescription}
        >
          {description}
        </Typography.Paragraph>

        <Space>
          {categories.map((item, i) => (
            <ColorTag
              key={item}
              textColor={TAG_COLORS[i].textColor}
              borderColor={TAG_COLORS[i].borderColor}
              shape="round"
              style={{ padding: '6px 12px' }}
              ghost={true}
            >
              {item}
            </ColorTag>
          ))}
        </Space>
        <div className={styles.price}>
          价格：
          <div className={styles.priceNumber}>
            <Authorize error={<span style={{ fontSize: 24 }}>请登录查看</span>}>
              {user?.IsRegistEntity === false ? (
                <span style={{ fontSize: 24 }}>认证通过后查看</span>
              ) : (
                <>
                  <span>{transform(currency)}</span>
                  <span>{price}</span>
                </>
              )}
            </Authorize>
          </div>
        </div>
      </Flex>
    </Flex>
  );
};

export default ProductInfo;
