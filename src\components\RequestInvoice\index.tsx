import {
  addInvoice,
  getInvoiceAddress,
  getInvoiceTitle,
} from '@/services/order/InvoiceController';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd';
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Steps,
  Table,
} from 'antd';
import { createStyles } from 'antd-style';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  stepContent: css`
    padding: 12px 0;
    h3::after {
      content: '';
      position: absolute;
      left: 0;
    }
  `,
  stepTitle: css`
    color: #000;
    padding-bottom: 10px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
  `,
  stepInfo: css`
    color: #000;
  `,
  tableFooterText: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 25px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  `,
  tableFooterNumber: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(17, 75, 237, 1);
    text-align: left;
  `,
  tableFooterAmount: css`
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 128, 99, 1);
    text-align: left;
  `,
  formGroup: css`
    margin-bottom: 12px;
  `,
  formLabel: css`
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
  `,
  formValue: css`
    display: block;
    margin-bottom: 8px;
    color: #333;
  `,
  detailBox: css`
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 12px 15px;
    margin-top: 15px;
    border-left: 2px solid #1890ff;
    h4 {
      font-size: 14px;
      margin: 0 0 10px 0;
      color: #1890ff;
      font-weight: 500;
    }
    .detail-row {
      display: flex;
      margin-bottom: 6px;
      font-size: 13px;
      line-height: 1.4;
    }

    .detail-label {
      min-width: 110px;
      color: #666;
      font-weight: 600;
    }

    .detail-value {
      flex: 1;
      color: #333;
    }
  `,
}));
interface IOfflinePayment {
  confirmCallBack: () => void;
}
const RequestInvoice = forwardRef(
  (props: IOfflinePayment, ref: React.Ref<any>) => {
    const { confirmCallBack } = props;
    const { styles } = useStyles();
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [stepValue, setStepValue] = useState<number>(0);
    const [remark, setRemark] = useState<string>();
    const [orderDetail, setOrderDetail] = useState<any>({});
    const [titleForm] = Form.useForm();
    const [addressForm] = Form.useForm();
    const [selectInvoiceTitle, setSelectInvoiceTitle] = useState<any>();
    const [selectInvoiceAddress, setSelectInvoiceAddress] = useState<any>();
    const [invoiceType, setInvoiceType] = useState<number>(0);
    const [invoiceTitles, setInvoiceTitles] = useState<any[]>([]);
    const [invoiceAddress, setInvoiceAddress] = useState<any[]>([]);
    const solutionColumns: TableProps['columns'] = [
      {
        title: '商品',
        dataIndex: 'SolutionName',
        align: 'center',
        key: 'SolutionName',
      },
      {
        title: '规格',
        dataIndex: 'SolutionAttributeCombinationName',
        align: 'center',
        key: 'SolutionAttributeCombinationName',
      },
      {
        title: '价格',
        dataIndex: 'UnitPrice',
        align: 'center',
        key: 'UnitPrice',
        render: (item: any, data: any) => (
          <>{'￥' + formatWithThousandSeparator(data.UnitPrice)}</>
        ),
      },
      {
        title: '数量',
        dataIndex: 'Quantity',
        align: 'center',
        key: 'Quantity',
      },
      {
        title: '小计',
        dataIndex: 'sum',
        align: 'center',
        render: (item: any, data: any) => (
          <>{'￥' + formatWithThousandSeparator(data.Total)}</>
        ),
      },
    ];
    useEffect(() => {
      getInvoiceAddress({}).then((res) => {
        setInvoiceAddress(res);
      });
      getInvoiceTitle({}).then((res) => {
        setInvoiceTitles(res);
      });
    }, []);
    const stepItem = [
      {
        title: '确认订单信息',
      },
      {
        title: '发票抬头',
      },
      {
        title: '接收地址',
      },
      {
        title: '添加备注',
      },
    ];
    const solutionTableData = () => {
      if (orderDetail?.OrderItems) {
        return orderDetail.OrderItems;
      } else {
        return [];
      }
    };
    const tableFooter = () => {
      return (
        <div className={styles.tableFooterText}>
          <Row>
            <Col span={12}>
              {/* <span>
              共
              <span className={styles.tableFooterNumber}>
                {solutionTableData().length}
              </span>
              件商品，商品总金额
              <span className={styles.tableFooterNumber}>
            
                {orderDetail.OrderTotal}
              </span>
            </span> */}
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <span>
                合计&nbsp;
                <span className={styles.tableFooterAmount}>
                  ￥{orderDetail.OrderTotal}
                </span>
              </span>
            </Col>
          </Row>
        </div>
      );
    };
    const renderStepContent = () => {
      switch (stepValue) {
        case 0:
          return (
            <div>
              <div className={styles.formGroup}>
                <label className={styles.formLabel}>订单编号</label>
                <div className={styles.formValue}>
                  {orderDetail?.CustomOrderNumber}
                </div>
              </div>
              <div className={styles.formGroup}>
                <label className={styles.formLabel}>客户名称</label>
                <div className={styles.formValue}>
                  {orderDetail?.CustomerCompany}
                </div>
              </div>
              <div className={styles.formGroup}>
                <label className={styles.formLabel}>订单金额</label>
                <div className={styles.formValue}>
                  ¥{orderDetail?.OrderTotal}
                </div>
              </div>
              <h4 style={{ marginTop: 8, marginBottom: 8 }}>商品信息</h4>
              <Table
                bordered={true}
                pagination={false}
                rowKey={'SolutionId'}
                columns={solutionColumns}
                dataSource={solutionTableData()}
                footer={tableFooter}
              />
            </div>
          );
        case 1:
          return (
            <div>
              <Form layout="vertical" form={titleForm}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="发票类型"
                      name="invoiceType"
                      rules={[{ required: true, message: '发票类型' }]}
                    >
                      <Select
                        placeholder="请选择发票类型"
                        style={{ width: '100%' }}
                        options={[
                          { label: '纸质普票', value: 0 },
                          { label: '纸质专票', value: 1 },
                          { label: '全电普', value: 2 },
                          { label: '全电专', value: 3 },
                        ]}
                      ></Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="发票抬头"
                      name="invoiceTitle"
                      rules={[{ required: true, message: '发票抬头' }]}
                    >
                      <Select
                        placeholder="请选择发票抬头"
                        style={{ width: '100%' }}
                        onChange={(value) => {
                          setSelectInvoiceTitle(
                            invoiceTitles.find((c) => c.Id === value),
                          );
                        }}
                        options={invoiceTitles.map((c) => {
                          return {
                            label: c.CompanyName + ' - ' + c.Bank,
                            value: c.Id,
                          };
                        })}
                      ></Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
              {selectInvoiceTitle && (
                <div className={styles.detailBox}>
                  <h4>抬头详情</h4>
                  <div className="detail-row">
                    <div className="detail-label">公司名称：</div>
                    <div className="detail-value" id="company-name">
                      {selectInvoiceTitle.CompanyName}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">纳税人识别号：</div>
                    <div className="detail-value">
                      {selectInvoiceTitle.TaxNum}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">地址：</div>
                    <div className="detail-value" id="company-address">
                      {selectInvoiceTitle.Address}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">联系电话：</div>
                    <div className="detail-value" id="company-phone">
                      {selectInvoiceTitle.PhoneNumber}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">开户行：</div>
                    <div className="detail-value" id="bank-name">
                      {selectInvoiceTitle.Bank}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">开户行账号：</div>
                    <div className="detail-value" id="bank-account">
                      {selectInvoiceTitle.BankAccountnum}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        case 2:
          return (
            <div>
              {(invoiceType === 0 || invoiceType === 1) && (
                <div style={{ color: '#666', marginBottom: 16 }}>
                  <InfoCircleOutlined />{' '}
                  您本次选择的是纸质发票，发票将快递至您选择地址
                </div>
              )}
              {(invoiceType === 2 || invoiceType === 3) && (
                <div style={{ color: '#666', marginBottom: 16 }}>
                  <InfoCircleOutlined />{' '}
                  您本次选择的是电子发票，发票将发送至您填写的邮箱
                </div>
              )}

              <Form layout="vertical" form={addressForm}>
                <Row gutter={16}>
                  <Col span={12}>
                    {(invoiceType === 0 || invoiceType === 1) && (
                      <Form.Item
                        label="接收地址"
                        name="Address"
                        rules={[{ required: true, message: '请输入接收地址' }]}
                      >
                        <Select
                          placeholder="请选择接收地址"
                          style={{ width: '100%' }}
                          onChange={(value) => {
                            setSelectInvoiceAddress(
                              invoiceAddress.find((c) => c.Id === value),
                            );
                          }}
                          options={invoiceAddress.map((c) => {
                            return { label: c.AddrNickName, value: c.Id };
                          })}
                        ></Select>
                      </Form.Item>
                    )}
                    {(invoiceType === 2 || invoiceType === 3) && (
                      <Form.Item
                        label="接收邮箱"
                        name="ReceiveEmail"
                        rules={[
                          { required: true, message: '请输入接收邮箱' },
                          { type: 'email', message: '请输入正确的邮箱' },
                        ]}
                      >
                        <Input
                          placeholder="请输入接受邮箱"
                          style={{ width: '100%' }}
                        ></Input>
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Form>

              {selectInvoiceAddress && (
                <div className={styles.detailBox}>
                  <h4>地址详情</h4>
                  <div className="detail-row">
                    <div className="detail-label">收件人：</div>
                    <div className="detail-value" id="company-name">
                      {selectInvoiceAddress.ToName}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">联系电话：</div>
                    <div className="detail-value">
                      {selectInvoiceAddress.PhoneNumber}
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">详细地址：</div>
                    <div className="detail-value" id="company-address">
                      {selectInvoiceAddress.Address}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        case 3:
          return (
            <div>
              <h4 style={{ marginTop: 8, marginBottom: 8 }}>备注信息</h4>
              <Input.TextArea
                placeholder="请输入备注信息（选填）"
                onChange={(e) => {
                  setRemark(e.target.value);
                }}
              ></Input.TextArea>
            </div>
          );
        default:
          return null;
      }
    };

    useImperativeHandle(ref, () => ({
      modalHandleShow,
    }));

    const modalHandleShow = (orderDl: any) => {
      setIsModalOpen(true);
      setOrderDetail(orderDl);
      setStepValue(0);
      setConfirmLoading(false);
    };

    const modalHandleCancel = () => {
      setIsModalOpen(false);
    };

    const modalFonter = () => (
      <div
        style={{
          borderTop: '1px solid rgba(229, 229, 229, 1)',
          lineHeight: '44px',
          height: '44px',
          paddingLeft: 32,
          paddingRight: 32,
        }}
      >
        <div style={{ textAlign: 'right' }}>
          <Button
            color="primary"
            variant="outlined"
            style={{ width: 80 }}
            onClick={modalHandleCancel}
          >
            取消
          </Button>
          {stepValue !== 0 && (
            <Button
              disabled={confirmLoading}
              type="primary"
              style={{ width: 80, marginLeft: 16 }}
              onClick={() => {
                if (stepValue > 0) {
                  setStepValue(stepValue - 1);
                }
              }}
            >
              上一步
            </Button>
          )}
          <Button
            loading={confirmLoading}
            type="primary"
            style={{ width: 80, marginLeft: 16 }}
            onClick={nextStepClick}
          >
            {stepValue === 3 ? '提交申请' : '下一步'}
          </Button>
        </div>
      </div>
    );
    const nextStepClick = () => {
      switch (stepValue) {
        case 0:
          setStepValue(1);
          break;
        case 1:
          titleForm.validateFields().then((values) => {
            setInvoiceType(values.invoiceType);
            if (values.invoiceType === 2 || values.invoiceType === 2) {
              setSelectInvoiceAddress(null);
            }
            setStepValue(2);
          });

          break;
        case 2:
          addressForm.validateFields().then((values) => {
            if (values.ReceiveEmail) {
              setSelectInvoiceAddress({ ReceiveEmail: values.ReceiveEmail });
            }
            setStepValue(3);
          });
          break;
        case 3:
          setConfirmLoading(true);
          addInvoice(orderDetail.CustomOrderNumber, {
            InvoiceType: invoiceType,
            TitleCompanyName: selectInvoiceTitle.CompanyName,
            TIN: selectInvoiceTitle.TaxNum,
            Titleaddress: selectInvoiceTitle.Address,
            TitlePhoneNumber: selectInvoiceTitle.PhoneNumber,
            Bank: selectInvoiceTitle.Bank,
            Bankaccount: selectInvoiceTitle.BankAccountnum,
            Remark: remark,
            ReceiveEmail: selectInvoiceAddress.ReceiveEmail,
            ReceivePerson: selectInvoiceAddress.ToName,
            ReceivePhoneNumber: selectInvoiceAddress.PhoneNumber,
            ReceiveAddress: selectInvoiceAddress.Address,
          }).then(
            (res) => {
              setConfirmLoading(false);
              setIsModalOpen(false);
              confirmCallBack();
            },
            () => {
              setConfirmLoading(false);
            },
          );
          break;
        default:
          break;
      }
    };
    return (
      <Modal
        destroyOnHidden={false}
        title={'申请开票'}
        open={isModalOpen}
        width={'50%'}
        styles={{
          content: {
            padding: 0,
          },
          header: {
            padding: 32,
          },
        }}
        footer={modalFonter}
        onCancel={modalHandleCancel}
      >
        <div
          style={{
            height: '60vh',
            overflow: 'auto',
            width: '100%',
            paddingLeft: 16,
            paddingRight: 16,
          }}
        >
          <Space.Compact style={{ width: '100%' }}>
            <Steps
              current={stepValue}
              labelPlacement="vertical"
              items={stepItem}
            />
          </Space.Compact>
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}></h3>
          </div>
          <div className={styles.stepInfo}>{renderStepContent()}</div>
        </div>
      </Modal>
    );
  },
);

export default RequestInvoice;
