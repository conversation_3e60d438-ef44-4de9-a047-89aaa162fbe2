import { Link } from '@umijs/max';
import { Flex, Typography } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css, prefixCls }) => ({
  container: css`
    position: relative;
    padding-right: 10%;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background: #e8e8e8;
    }
  `,
  title: css`
    position: relative;
    margin-bottom: 0;
    font-size: 16px;
    color: #666;
    line-height: 1.2;
    padding-left: 10px;
  `,
  borderLeft: css`
    width: 4px;
    height: 100%;
    background: #2d8cf0;
    position: absolute;
    left: 0;
    top: 0;
  `,
  relationItem: css`
    padding: 2px 0;
    box-shadow: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    &:hover {
      background-color: #f9f9f9;
      padding-left: 8px;
      transform: translateX(2px);
      border-radius: 4px;
      padding-top: 2px !important;
    }
  `,
  name: css`
    font-size: 16px;
    color: #333;
  `,
  supplier: css`
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 1px;
    font-weight: 600;
  `,
  description: css`
    max-width: calc(100% - 25px);
    &.${prefixCls}-typography {
      font-size: 12px;
      color: #999;
      margin-bottom: 0;
      font-weight: 400;
    }
  `,
}));

interface IRelationProduct {
  id: string;
  name: string;
  supplier: string;
  description: string;
}

interface RelationProductProps {
  items: IRelationProduct[];
}

const RelationProduct: React.FC<RelationProductProps> = ({ items = [] }) => {
  const { styles } = useStyles();
  return (
    <Flex className={styles.container} vertical gap={15}>
      <h3 className={styles.title}>
        <span className={styles.borderLeft}></span>
        相关推荐
      </h3>

      {items.map(({ id, name, supplier, description }) => (
        <Link key={id} to={`/solution/detail/${id}`}>
          <Flex vertical gap={5} className={styles.relationItem}>
            <div className={styles.name}>{name}</div>
            <div className={styles.supplier}>
              供应商：<span>{supplier}</span>
            </div>
            <Typography.Paragraph
              ellipsis={{ rows: 3 }}
              className={styles.description}
              title={description}
            >
              {description}
            </Typography.Paragraph>
          </Flex>
        </Link>
      ))}
    </Flex>
  );
};
export default RelationProduct;
