import { Tag } from 'antd';
import { createStyles } from 'antd-style';
import React, {
  FC,
  memo,
  PropsWithChildren,
  useCallback,
  useEffect,
  useState,
} from 'react';

const { CheckableTag } = Tag;

interface TagSelectOptionProps extends PropsWithChildren {
  checked: boolean;
  onChange: (value: string, state: boolean) => void;
  value: string;
  disabled?: boolean;
}

const useStyles = createStyles(({ css }, props: { disabled: boolean }) => {
  const disabled = css`
    pointer-events: none;
    cursor: not-allowed;
    color: #cbcbcb;
  `;
  return {
    tag: css`
      font-size: 13px;
      padding: 4px 10px;
      font-weight: 400;
      ${props.disabled && disabled}
    `,
  };
});

export const TagSelectOption: FC<TagSelectOptionProps> = memo(
  ({ children, checked, onChange, value, disabled = false }) => {
    const { styles } = useStyles({ disabled });

    return (
      <CheckableTag
        className={styles.tag}
        checked={checked}
        onChange={(state) => onChange(value, state)}
      >
        {children}
      </CheckableTag>
    );
  },
);

interface TagSelectProps {
  items: { label: string; value: string; disabled?: boolean }[];
  renderItems: (
    item: {
      label: string;
      value: string;
      disabled?: boolean;
    },
    selectedKeys: string[],
  ) => React.ReactNode;
  value: string[];
  defaultValue?: string[];
  hideCheckAll?: boolean;
  onSelectAll?: (checked: boolean) => void;
}

const TagSelect: FC<TagSelectProps> = ({
  items,
  renderItems,
  value: propsValue,
  defaultValue,
  hideCheckAll = false,
  onSelectAll,
}) => {
  const [value, setValue] = useState(defaultValue || []);
  // 同步外部value变化
  useEffect(() => {
    if (propsValue !== undefined) {
      setValue(propsValue);
    }
  }, [propsValue]);

  const getAllTags = useCallback(() => {
    return items.map((item) => item.value);
  }, [items]);

  const checkedAll = getAllTags().length === value.length;

  return (
    <div>
      {hideCheckAll ? null : (
        <CheckableTag
          checked={checkedAll}
          key="tag-select-__all__"
          onChange={onSelectAll}
        >
          全部
        </CheckableTag>
      )}

      {items.map((item) => {
        return (
          <React.Fragment key={item.value}>
            {renderItems(item, value)}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default memo(TagSelect);
