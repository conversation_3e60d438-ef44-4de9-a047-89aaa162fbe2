import { request } from '@umijs/max';

export async function getHistory() {
  return request(`/api/Company/history`, {
    method: 'GET',
  });
}

export async function create(payload: any) {
  return request(`/api/Users/<USER>/email/check`, {
    method: 'POST',
    data: payload,
  });
}

export async function getCompanyInfo() {
  return request('/api/Company/info', {
    method: 'GET',
  });
}

export async function submitCompanyInfo(isCreated: boolean, payload: any) {
  let type;
  if (isCreated) {
    type = 'update';
  } else {
    type = 'create';
  }
  return request(`/api/Company/info/${type}`, {
    method: 'POST',
    data: payload,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
