export const PRODUCT_TYPES: Record<string, string> = {
  solution: '解决方案',
  brand: '品牌专区',
};

export const Regions = [
  {
    Name: '中国大陆',
    Id: 1,
    name: 'Mainland',
    Currency: '¥',
  },
  {
    Name: '中国香港',
    Id: 2,
    name: '<PERSON><PERSON><PERSON>',
    Currency: 'HK$',
  },
  {
    Name: '中国台湾',
    Id: 3,
    name: 'Taiwan',
    Currency: 'NT$',
  },
  {
    Name: '中国澳门',
    Id: 4,
    name: 'Macao',
    Currency: 'MOP$',
  },
];

export const COMPANY_STATUS = new Map([
  [5, 'red'], //禁用
  [10, 'orange'], //待审核
  [20, 'red'], //审核被拒绝
  [30, 'green'], //审核通过
  [40, 'green'], //启用
  [0, 'red'], //未激活
]);

export const TAG_COLORS = [
  {
    textColor: '#333',
    borderColor: '#2D8CF0',
  },
  {
    textColor: '#333',
    borderColor: '#36CFC9',
  },
  {
    textColor: '#333',
    borderColor: '#722ED1',
  },
];

export const DeliveryMethod: { label: string; value: string }[] = [
  {
    label: '全部产品',
    value: 'all',
  },
  {
    label: '软件',
    value: '0',
  },
  {
    label: 'AI一体机',
    value: '10',
  },
  {
    label: 'SAAS',
    value: '1',
  },
  {
    label: '服务',
    value: '2',
  },
];
