import { createStyles } from 'antd-style';
import { FC } from 'react';
import Advantages from './components/Advantages';
import Banner from './components/Banner';
import BrandZone from './components/BrandZone';
import CloudProduct from './components/CloudProduct';
import BlueCloud from './components/BlueCloud';

const useStyles = createStyles(({ css }) => ({
  container: css`
    margin-top: -48px;
    background: #fff;
  `,
}));

const HomePage: FC = () => {
  const { styles } = useStyles();
  return (
    <div className={styles.container}>
      <Banner />
      <BlueCloud />
      <CloudProduct />
      <Advantages />
      <BrandZone />
    </div>
  );
};

export default HomePage;
