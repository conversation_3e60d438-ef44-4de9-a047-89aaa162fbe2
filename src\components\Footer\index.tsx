import { Divider, Flex, Space } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  container: css`
    height: 400px;
    background: #212240;
    color: rgba(229, 229, 229, 1);
  `,
  content: css`
    width: 1200px;
    margin: 0 auto;
    a {
      color: rgba(229, 229, 229, 1);
    }
  `,
  support: css`
    font-size: 14px;
  `,
  divider: css`
    border-color: rgba(255, 255, 255, 0.4);
  `,
  text: css`
    font-size: 12px;
    color: rgba(166, 166, 166, 1);
    a {
      color: rgba(166, 166, 166, 1);
    }
  `,
}));
const Footer = () => {
  const { styles } = useStyles();
  return (
    <Flex className={styles.container} align="center">
      <Flex
        className={styles.content}
        vertical
        justify="space-between"
        align="center"
      >
        <Flex justify="space-between" align="center" style={{ width: '100%' }}>
          <Flex vertical gap={26} style={{ alignSelf: 'flex-start' }}>
            <Flex vertical gap={8}>
              <Flex gap={5} align="center">
                <img
                  src={require('@/assets/email.png')}
                  width={20}
                  alt="email"
                />
                联系我们
              </Flex>
              <a href="mailto:<EMAIL>" className={styles.support}>
                <EMAIL>
              </a>
            </Flex>
            <Flex vertical gap={8}>
              <div>蓝云官网</div>
              <a
                href="https://www.21vbluecloud.com"
                target="_blank"
                rel="noreferrer"
                className={styles.support}
              >
                https://www.21vbluecloud.com
              </a>
            </Flex>
          </Flex>
          <Flex vertical gap={8} style={{ alignSelf: 'flex-start' }}>
            <div>业务咨询</div>
            <Flex vertical gap={5}>
              <a href="mailto:<EMAIL>" className={styles.support}>
                <EMAIL>
              </a>
            </Flex>
          </Flex>
          <Flex gap={35} align="center" style={{ alignSelf: 'flex-start' }}>
            <img
              width={199}
              src={require('@/assets/bluecloud.png')}
              alt="bluecloud"
            />
            <img
              width={126}
              src={require('@/assets/wechat.png')}
              alt="wechat"
            />
          </Flex>
        </Flex>
        <Divider className={styles.divider} />
        <Flex vertical justify="center" align="center" gap={8}>
          <div className={styles.text}>
            上海蓝云网络科技有限公司 Shanghai Blue Cloud Technology Co.,Ltd
          </div>
          <div className={styles.text}>
            <Space size={20}>
              <a
                href="https://beian.miit.gov.cn/#/Integrated/recordQuery"
                target="_blank"
                rel={'noreferrer'}
              >
                京ICP备09025197号-31
              </a>
              <Space size={5}>
                <img
                  width={'14px'}
                  src={require('@/assets/beian.png')}
                  alt="beian"
                />
                <a
                  href="https://beian.mps.gov.cn/#/query/webSearch?code=11010502048708"
                  target="_blank"
                  rel="noreferrer"
                >
                  京公网安备11010502048708号
                </a>
              </Space>
            </Space>
          </div>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Footer;
