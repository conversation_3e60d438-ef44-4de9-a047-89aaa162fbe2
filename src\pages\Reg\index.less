.container {
  width: 100%;
  min-height: 100vh;
  margin-top: -48px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-image: url('@/assets/brandZoneBg.png'),
    linear-gradient(
      180deg,
      rgba(247, 249, 252, 100%) 0%,
      rgba(247, 249, 252, 0%) 100%
    );
  background-position: center;
  background-size: cover;
}

.regpan {
  width: 500px;
}

.ant-pro-page-container {
  position: relative;
  display: flex;
  align-items: center;
}

.ant-btn[type='submit'] {
  width: 440px;
}

.ant-radio {
  font-size: 12px;
}

.reg {
  width: 100%;
  height: 42px;
  border-radius: 6px;
  border: 0;
}

.reg::before {
  content: '';
  background: linear-gradient(
    132.34deg,
    rgba(17, 75, 237, 100%) 0%,
    rgba(54, 152, 217, 100%) 69.77%,
    rgba(117, 225, 255, 100%) 100%
  );
  position: absolute;
  inset: -1px;
  opacity: 1;
  transition: all 0.3s;
  border-radius: inherit;
}

.reg > span {
  position: relative;
}

.text {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 17.38px;
}

.link {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 17.38px;
  color: rgba(17, 61, 237, 100%);
}

.regsuccess {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 500px;
  height: 400px;
  justify-content: center;
  border-radius: 10px;
  gap: 20px;
  background: rgba(255, 255, 255, 100%);
  box-shadow: 0 2px 2px rgba(255, 255, 255, 25%),
    0 2px 8px rgba(36, 109, 227, 10%), 0 4px 16px rgba(36, 109, 227, 15%);
}

.loginButton {
  width: 400px;
}

.line {
  width: 0;
  height: 240px;
  border-left: 1px solid rgba(229, 229, 229, 100%);
}

.block {
  width: 150px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20.27px;
  color: rgba(56, 56, 56, 100%);
}

.space {
  display: flex;
  gap: 10px;
}

.fullpage {
  width: 100%;
  padding-top: 50px;
  background-color: #fff;
}

.privacypage {
  width: 1200px;
  margin: 0 auto;
}

.privacytitle {
  font-size: 36px;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 52.13px;
  color: rgba(56, 56, 56, 100%);
}

.privacyby {
  margin: 4px 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 23.17px;
  color: rgb(125, 125, 125);
  text-align: right;
}

.privacytext {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0;
  text-indent: 2em;
  line-height: 23.17px;
  color: rgba(56, 56, 56, 100%);
}

.title {
  font-size: 24px;
  font-weight: 500;
}

.subtitle {
  font-size: 12px;
  font-weight: 400;
}
