import { useToggle } from 'ahooks';
import { createStyles } from 'antd-style';
import ContactForm from './ContactForm';

const useStyles = createStyles(({ css, token }, props: { show: boolean }) => ({
  container: css`
    position: fixed;
    right: 0;
    bottom: 25%;
    z-index: 999;
  `,
  content: css`
    position: relative;
  `,
  button: css`
    display: ${props.show ? 'none' : 'block'};
    background: ${token.colorPrimary};
    color: #fff;
    border-radius: 8px 0 0 8px;
    padding: 16px 10px 8px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: -2px 0 8px rgba(25, 118, 210, 0.3);
    transition: all 0.3s;
    writing-mode: vertical-lr;
    text-orientation: mixed;
    letter-spacing: 4px;
    &:hover {
      background: ${token.colorPrimaryHover};
      transform: translateX(-3px);
      box-shadow: -4px 0 12px rgba(25, 118, 210, 0.4);
    }
  `,
  form: css`
    position: absolute;
    right: 10px;
    bottom: 0;
    display: ${props.show ? 'block' : 'none'};
    animation: slideUp 0.3s ease-in-out;
    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `,
}));
const ContactUs = () => {
  const [state, { toggle }] = useToggle();
  const { styles } = useStyles({ show: state });
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.button} onClick={toggle}>
          联系我们
        </div>

        <div className={styles.form}>
          {state ? <ContactForm onClose={toggle} /> : null}
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
