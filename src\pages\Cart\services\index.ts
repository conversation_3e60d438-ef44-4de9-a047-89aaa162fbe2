import { request } from '@umijs/max';
export const getCart = async (region:number) => {
  return request(`/api/ShoppingCarts/items?region=${region}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  });
};
export const putCartItemsQuantity = async (id: number, quantity: number) => {
  return request(`/api/ShoppingCarts/items/${id}/quantity?quantity=${quantity}`, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    },
  });
};
export const deleteCartItem = async (id: number) => {
  return request(`/api/ShoppingCarts/items/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
  });
};
