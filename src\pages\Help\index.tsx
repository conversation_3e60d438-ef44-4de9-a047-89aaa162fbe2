import { ArrowRightOutlined } from '@ant-design/icons';
import { Avatar, Button, List, Typography } from 'antd';
import { ReactComponent as AnswerSvg } from './answerSvg.svg';
import style from './index.less';
import { ReactComponent as QuestionSvg } from './questionSvg.svg';
export default () => {
  return (
    <div className={style.helpCenter}>
      <div className={style.banner}>
        <div className={style.box}>
          <div className={style.head}>
            <div className={style.title}>常见问题</div>
            <p className={style.p}>
              提供适合不同企业的支持计划，及覆盖上云前、中、后全周期的专业技术服务，让云上业务更高效。
            </p>
            <p className={style.p}>联系我们：<EMAIL></p>
            <Button
              type="primary"
              style={{
                margin: '17px 0 27px 0',
                width: '120px',
                height: '38px',
              }}
              onClick={() => {
                window.open(
                  'https://moonplace.blob.core.chinacloudapi.cn/docs/Marketplace-Reseller-Manual.pdf',
                  '_blank',
                );
              }}
            >
              操作手册
              <ArrowRightOutlined />
            </Button>
          </div>
        </div>
      </div>
      <div className={style.page}>
        <Typography.Title level={5}>【关于世纪互联云市场】</Typography.Title>
        <Typography.Paragraph>
          世纪互联云市场是专门为企业级客户、ISV、合作伙伴及经销商打造的云生态产品和服务交易平台，聚合了市场上众多优秀的
          AI
          与云周边第三方应用及服务，构建出全方位、多层次的数智化生态系统。世纪互联云市场致力于搭建由AI加持的、中立的、一站式的数智化解决方案宝库和高效便利的生态服务枢纽，成为推动企业客户数字化转型与业务创新的关键力量。
        </Typography.Paragraph>
        <Typography.Title level={5}>
          丰富的产品资源，满足一站式个性化需求
        </Typography.Title>
        <Typography.Paragraph>
          世纪互联云市场覆盖AI+、安全、备份容灾与迁移、基础架构、系统管理、企业应用、行业应用、数据管理共8大产品类别。
        </Typography.Paragraph>
        <Typography.Title level={5}>
          完善的保障体系，助力客户选购高效无忧
        </Typography.Title>
        <Typography.Paragraph>
          在服务模式方面，为了最大化减轻客户在产品选型和下单流程中的负担，提升商业流转效率，世纪互联云市场本着“云市场即服务”的理念，推动平台从传统的销售工具向价值交付升级，以客户需求为中心推出一站式购物服务，灵活的价格，以及完善的全流程保障体系，经销商可以帮助客户通过线上轻松实现从下单、交易、支付、交付的全流程体验，得到全方位的技术和商务服务支持。
        </Typography.Paragraph>
        <Typography.Paragraph>
          <ul>
            <li>
              <Typography.Text>
                在售前阶段，专业团队深入了解客户需求并提供专业咨询和建议，帮助客户精准选择产品和解决方案。
              </Typography.Text>
            </li>
            <li>
              <Typography.Text>
                在交易过程中，先进的技术架构确保交易快速、安全、可靠。
              </Typography.Text>
            </li>
            <li>
              <Typography.Text>
                在售后环节，提供全方位技术支持与服务，及时解决技术问题和优化需求，降低风险，提升合作保障。
              </Typography.Text>
            </li>
          </ul>
        </Typography.Paragraph>
        <Typography.Paragraph>
          未来，随着用户订单的增加，世纪互联云市场还将深度挖掘客户行为洞见，主动为客户提供符合需求的产品推荐，定制“更懂你”的智慧服务，助力业务增长；同时，为
          ISV
          厂商创造更多客户商机，拓展业务版图；为渠道及合作伙伴找到更多生态资源和解决方案，助力销售增长。
        </Typography.Paragraph>
      </div>
      <div className={style.page}>
        <List
          itemLayout="vertical"
          dataSource={[
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  &quot;世纪互联云市场&quot;是什么？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>
                    ①由世纪互联蓝云为中国云市场打造的&quot;解决方案平台&quot;，是聚合上游解决方案提供商、服务提供商和下游经销商为一体的一站式云解决方案获取平台。
                  </p>
                  <p>②服务于经销商的在线系统。</p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  &quot;世纪互联云市场&quot;上的解决方案是什么？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>
                    ①&quot;世纪互联云市场&quot;上的解决方案是公有云周边第三方产品及服务，涵盖安全、开发与运维、数据、行业应用等多个领域，覆盖云转型环境下的多个垂直行业。
                  </p>
                  <p>
                    ②解决方案既包括从国际落地到中国的云生态服务，也包括品牌厂商的畅销SaaS产品，还包括解决热门场景的多产品集成方案等。
                  </p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  如何购买&quot;世纪互联云市场&quot;上的解决方案？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>
                    ①&quot;世纪互联云市场&quot;上的解决方案通过渠道（经销商）来购买。
                  </p>
                  <p>
                    ②客户可以联系&quot;世纪互联云市场&quot;获取渠道服务或联系现有的&quot;世纪互联云市场&quot;商城渠道实现在线下单。
                  </p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  如何成为&quot;世纪互联云市场&quot;的经销商？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>
                    合法实体可以在线提交申请:{' '}
                    <a
                      href="https://marketplace.vnet.com/register"
                      target="_blank"
                      rel="noreferrer"
                    >
                      https://marketplace.vnet.com/register
                    </a>
                    ， 或发送邮件至 <EMAIL>。
                  </p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  如何把我的产品上架到&quot;世纪互联云市场&quot;？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>
                    ①合法实体可以通过&quot;世纪互联云市场&quot;ISV中心提交申请:{' '}
                    <a
                      href="https://marketops.vnet.com/customized/isv-project"
                      target="_blank"
                      rel="noreferrer"
                    >
                      https://marketops.vnet.com/customized/isv-project
                    </a>
                    ， 注册成为&quot;世纪互联云市场&quot;的合作ISV。
                  </p>
                  <p>
                    ②已注册的ISV实体可以通过&quot;世纪互联云市场&quot;ISV中心上架产品并配置产品对接服务等。
                  </p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  如何获取&quot;世纪互联云市场&quot;支持？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>请联系mp<EMAIL></p>
                </div>
              ),
            },
            {
              title: (
                <div style={{ lineHeight: '37px', fontSize: 20 }}>
                  订单线下付款到哪个账号？
                </div>
              ),
              text: (
                <div style={{ paddingTop: 10, color: '#808080' }}>
                  <p>账户名称：上海蓝云网络科技有限公司</p>
                  <p>开户行账号：0200227919200053244</p>
                  <p>开户行名称：中国工商银行朝阳国航大厦支行</p>
                </div>
              ),
            },
          ]}
          renderItem={(item, index) => (
            <List.Item className={style.antLstItem}>
              <List.Item.Meta
                className={style.meta}
                style={{
                  fontSize: 20,
                }}
                avatar={
                  <Avatar
                    src={<QuestionSvg viewBox="0 0 36 36" />}
                    className={style.avatar}
                  />
                }
                title={<a>{item.title}</a>}
              />
              <List.Item.Meta
                className={style.meta}
                avatar={
                  <Avatar
                    src={<AnswerSvg viewBox="0 0 36 36" />}
                    className={style.avatar}
                  />
                }
                description={item.text}
              />
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};
