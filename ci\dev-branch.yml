# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

variables:
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

trigger:
- dev

pool:
  name: default

steps:

  - task: Cache@2
    inputs:
      key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
      path: $(pnpm_config_cache)
    displayName: Cache pnpm

  - script: |
      npm install --global corepack@latest
      corepack enable
      corepack prepare pnpm@latest-10 --activate
      pnpm config set store-dir $(pnpm_config_cache)
    displayName: "Setup pnpm"

  - script: |
      pnpm install
      pnpm run build
    displayName: "pnpm install and build"

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: "$(Build.BinariesDirectory)"
      includeRootFolder: true
      archiveType: "zip"
      archiveFile: "$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip"
      replaceExistingArchive: true

  - task: CopyPublishBuildArtifacts@1
    inputs:
      CopyRoot: '$(Build.ArtifactStagingDirectory)'
      Contents: '$(Build.BuildId).zip'
      ArtifactName: 'drop'
      ArtifactType: 'Container'

