import { useEffect } from 'react';

/**
 * 监听浏览器关闭事件的Hook
 * @param callback 关闭浏览器时要执行的回调函数
 * @param options 配置选项
 */
const useBeforeUnload = (
  callback: () => void,
  options?: {
    /**
     * 是否捕获刷新页面事件（默认false）
     */
    captureRefresh?: boolean;
  },
) => {
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // 如果是刷新页面且不捕获刷新事件，则不执行回调
      if (event.type === 'beforeunload' && !options?.captureRefresh) {
        return;
      }

      callback();
    };

    // 添加事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);
    // 如果是捕获刷新事件，也监听unload事件
    if (options?.captureRefresh) {
      window.addEventListener('unload', handleBeforeUnload);
    }

    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      if (options?.captureRefresh) {
        window.removeEventListener('unload', handleBeforeUnload);
      }
    };
  }, [callback, options?.captureRefresh]);
};

export default useBeforeUnload;
