import { useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { notification } from 'antd';
import { addCart } from '../services';

const useOperation = () => {
  const { refreshCartCount, setCartRegion } = useModel('cart');
  const [api, contextHolder] = notification.useNotification();
  const { run, runAsync, loading } = useRequest(addCart, {
    manual: true,
    onSuccess: () => {
      api.success({
        message: '添加成功',
        description: '商品已成功添加到购物车',
      });
      refreshCartCount();
    },
  });

  return {
    notificationContextHolder: contextHolder,
    addProductToCart: run,
    addProductToCartAsync: runAsync,
    loading,
    refreshCartCount,
    setCartRegion,
  };
};

export default useOperation;
