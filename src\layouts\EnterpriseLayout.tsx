import { ReactComponent as BillSelectedSVG } from '@/assets/bill-selected.svg';
import { ReactComponent as BillSVG } from '@/assets/bill.svg';
import { ReactComponent as CustomerSelectedSVG } from '@/assets/customer-selected.svg';
import { ReactComponent as CustomerSVG } from '@/assets/customer.svg';
import { ReactComponent as EnterpriseSelectedSVG } from '@/assets/enterprise-info-selected.svg';
import { ReactComponent as EnterpriseSVG } from '@/assets/enterprise-info.svg';
import { ReactComponent as InvoiceSelectedSVG } from '@/assets/invoice-selected.svg';
import { ReactComponent as InvoiceSVG } from '@/assets/invoice.svg';
import { ReactComponent as OrderSelectedSVG } from '@/assets/order-selected.svg';
import { ReactComponent as OrderSVG } from '@/assets/order.svg';
import { ReactComponent as UserSelectedSVG } from '@/assets/user-selected.svg';
import { ReactComponent as UserSVG } from '@/assets/user.svg';
import useRouteKey from '@/hooks/useRouteKey';
import useUserStore, { useUserDeriveState } from '@/store/user';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Navigate, Outlet, useNavigate, useRouteProps } from '@umijs/max';
import { Layout, theme } from 'antd';
import { createStyles, cx } from 'antd-style';
import React, { useEffect, useState } from 'react';

const { Content, Sider } = Layout;

type MenuItem = {
  key: string;
  label: string;
  icon?: React.ReactNode;
  selectedIcon?: React.ReactNode;
  children?: Omit<MenuItem, 'children'>[];
};

const normalItems: MenuItem[] = [
  {
    label: '企业信息',
    key: 'info',
    icon: <EnterpriseSVG />,
    selectedIcon: <EnterpriseSelectedSVG />,
  },
];

const adminItems: MenuItem[] = [
  {
    label: '客户管理',
    key: 'customers',
    icon: <CustomerSVG />,
    selectedIcon: <CustomerSelectedSVG />,
  },
  {
    label: '订单管理',
    key: 'orders',
    icon: <OrderSVG />,
    selectedIcon: <OrderSelectedSVG />,
  },
  {
    label: '账单管理',
    key: 'bill',
    icon: <BillSVG />,
    selectedIcon: <BillSelectedSVG />,
  },
  {
    label: '发票管理',
    key: 'invoice',
    icon: <InvoiceSVG />,
    selectedIcon: <InvoiceSelectedSVG />,
    children: [
      {
        label: '开票历史',
        key: 'invoiceHistory',
      },
      {
        label: '抬头管理',
        key: 'invoiceTitle',
      },
      {
        label: '地址管理',
        key: 'invoiceAddress',
      },
    ],
  },
  {
    label: '用户管理',
    key: 'users',
    icon: <UserSVG />,
    selectedIcon: <UserSelectedSVG />,
  },
];

const useStyles = createStyles(({ css, prefixCls }) => ({
  override: css`
    .${prefixCls}-menu-item {
      border-radius: 0;
    }
    .${prefixCls}-menu-item-selected {
      background: #f1f4fd;
      border-right: 2px solid #114bed;
    }
  `,
  menu: css`
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-weight: 400;
    list-style: none;
  `,
  menuItem: css`
    height: 42px;

    line-height: 42px;
    text-align: left;
    padding-left: 16px;
    margin: 2px 0;
    border-right: 2px solid transparent;
    color: rgba(94, 94, 94, 1);
    transition: all 0.3s;
    cursor: pointer;
    &:hover,
    &.active {
      background: rgba(241, 244, 253, 1);
      border-right: 2px solid rgba(17, 75, 237, 1);
    }
    &.active {
      color: rgba(17, 75, 237, 1);
    }
    span {
      display: inline-block;
      margin-right: 8px;
      vertical-align: middle;
    }
  `,
  subMenu: css`
    margin-left: 20px;
    padding: 0;
    font-size: 14px;
    font-weight: 400;
    list-style: none;
    overflow: hidden;
    transition: all 0.3s;
  `,
  menuItemWithChildren: css`
    display: flex;
    justify-content: center;
    align-items: center;
  `,
  arrowIcon: css`
    margin-left: 8px;
    font-size: 10px;
    line-height: 40px;
    display: flex;
    align-items: center;
    height: 100%;
  `,
}));

const EnterpriseLayout: React.FC = () => {
  const { user } = useUserStore();
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  const { styles } = useStyles();

  const { isLogin } = useUserDeriveState();
  const routeKey = useRouteKey();
  const routesProps = useRouteProps();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [collapsedMenus, setCollapsedMenus] = useState<Record<string, boolean>>(
    { invoice: true },
  );
  const navigate = useNavigate();

  useEffect(() => {
    if (user && user.IsRegistEntity) {
      setMenuItems(normalItems.concat(adminItems));
    } else {
      setMenuItems(normalItems);
    }
  }, [user]);

  const toggleCollapse = (key: string) => {
    setCollapsedMenus((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSelect = (key: string) => {
    switch (key) {
      case 'info':
        navigate('/enterprise/info');
        return;
      case 'customers':
        navigate('/enterprise/customers');
        return;
      case 'orders':
        navigate('/enterprise/orders');
        return;
      case 'users':
        navigate('/enterprise/users');
        return;
      case 'invoiceHistory':
        navigate('/enterprise/invoice/history');
        return;
      case 'invoiceTitle':
        navigate('/enterprise/invoice/title');
        return;
      case 'invoiceAddress':
        navigate('/enterprise/invoice/address');
        return;
      case 'invoice':
        if (
          !isActive('invoiceHistory') &&
          !isActive('invoiceTitle') &&
          !isActive('invoiceAddress')
        ) {
          toggleCollapse('invoice');
        }
        return;
      case 'bill':
        navigate('/enterprise/bill');
        return;
    }
  };

  const isActive = (key: string) => {
    if (routeKey === key) return true;
    if (key === 'invoice' && routeKey?.startsWith('invoice')) return true;
    return false;
  };

  const isSubMenuActive = (key: string) => {
    return routeKey === key;
  };

  if (!isLogin) {
    return <Navigate to="/login" />;
  }
  return (
    <Layout style={{ minHeight: '100vh', background: 'transparent' }}>
      <Sider style={{ background: '#fff' }} width={138}>
        <ul className={styles.menu}>
          {menuItems.map((item) => (
            <React.Fragment key={item?.key}>
              <li
                className={cx(styles.menuItem)}
                onClick={() => handleSelect(item.key)}
              >
                <span>
                  {isActive(item.key) ? item?.selectedIcon : item?.icon}
                </span>
                {item?.label}
                {item?.children && (
                  <span className={styles.arrowIcon}>
                    {collapsedMenus[item.key] ? (
                      <UpOutlined />
                    ) : (
                      <DownOutlined />
                    )}
                  </span>
                )}
              </li>
              {item?.children && (
                <ul
                  className={styles.subMenu}
                  style={{
                    height: collapsedMenus[item.key]
                      ? 0
                      : `${item.children.length * 46}px`,
                  }}
                >
                  {item.children.map((child) => (
                    <li
                      key={child.key}
                      className={cx(styles.menuItem, {
                        active: isSubMenuActive(child.key),
                      })}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelect(child.key);
                      }}
                    >
                      {child?.label}
                    </li>
                  ))}
                </ul>
              )}
            </React.Fragment>
          ))}
        </ul>
      </Sider>
      <Layout style={{ background: 'rgba(250, 250, 250, 1)' }}>
        <Content style={{ margin: 16 }}>
          <div
            style={
              routesProps.defaultLayout === false
                ? {}
                : {
                    padding: 24,
                    minHeight: 360,
                    background: colorBgContainer,
                    borderRadius: 8,
                  }
            }
          >
            <Outlet />
            {/* <ContactUs /> */}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default EnterpriseLayout;
