import { useState } from 'react';

// 封装可复用的 Hook
function useObjectState<T extends object>(initial: T) {
  const [state, setState] = useState<T>(initial);

  const updateState = (partial: Partial<T>) => {
    setState((prev) => {
      const newState = { ...prev };
      // 深度合并嵌套对象（简化版）
      Object.entries(partial).forEach(([key, value]) => {
        if (typeof value === 'object' && !Array.isArray(value)) {
          newState[key as keyof T] = { ...prev[key as keyof T], ...value };
        } else {
          newState[key as keyof T] = value as T[keyof T];
        }
      });
      return newState;
    });
  };

  return [state, updateState] as const;
}

export default useObjectState;
