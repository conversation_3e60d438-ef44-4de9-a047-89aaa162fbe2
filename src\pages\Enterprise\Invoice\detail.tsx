import { getInvoiceDetail } from '@/services/order/InvoiceController';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { formatDateTime } from '@/utils/format';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from '@umijs/max';
import type { TableProps } from 'antd';
import {
  Button,
  Col,
  ConfigProvider,
  Flex,
  notification,
  Row,
  Skeleton,
  Table,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  titleIcon: css`
    width: 4px;
    height: 18px;
    opacity: 1;
    border-radius: 10px;
    display: inline-block;
    margin-right: 2px;
    background: linear-gradient(
      112.6deg,
      rgba(17, 75, 237, 0.1) 0%,
      rgba(17, 75, 237, 8) 100%
    );
  `,
  title: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 18px !import;
    color: rgba(56, 56, 56, 1);
    vertical-align: top;
  `,
  tableFooterText: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 25px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  `,
  tableFooterNumber: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(17, 75, 237, 1);
    text-align: left;
  `,
  tableFooterAmount: css`
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 128, 99, 1);
    text-align: left;
  `,
  orderInfo: css`
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 36px;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    padding-top: 16px;
  `,
  orderInfoTitle: css`
    display: inline-block;
    width: 100px;
  `,
  orderInfoValue: css`
    margin-left: 10px;
  `,
}));
const InvoiceDetail = () => {
  const navigate = useNavigate();
  const [api, contextHolder] = notification.useNotification();
  const { styles } = useStyles();
  const [detail, setDetail] = useState<any>({});
  const location = useLocation();
  const [loading, setLoading] = useState<boolean>(true);
  const { CustomOrderNumber, BackType } = location.state as any;
  const columns: TableProps<any>['columns'] = [
    {
      title: '操作时间',
      dataIndex: 'OperationDateUtc',
      key: 'OperationDateUtc',
      render: (value) => {
        return <span>{formatDateTime(new Date(value), true)}</span>;
      },
    },
    {
      title: '操作内容',
      dataIndex: 'OperationContent',
      key: 'OperationContent',
    },
    {
      title: '操作人',
      dataIndex: 'Operator',
      key: 'Operator',
    },
    {
      title: '备注',
      dataIndex: 'Remark',
      key: 'Remark',
    },
  ];
  useEffect(() => {
    getOrderDetailData();
  }, []);

  const solutionTableData = () => {
    let data =
      detail.InvoiceItems?.map((item: any) => ({
        ...item,
        CustomOrderNumber: detail.CustomOrderNumber,
      })) ?? [];
    return data;
  };
  const InvoiceOperationData = () => {
    return detail.InvoiceOperations?.sort(
      (a: any, b: any) =>
        new Date(b.OperationDateUtc).getTime() -
        new Date(a.OperationDateUtc).getTime(),
    );
  };
  const getOrderDetailData = () => {
    setLoading(true);
    getInvoiceDetail(CustomOrderNumber).then((res) => {
      setLoading(false);
      console.log('getInvoiceDetail', res);
      setDetail(res);
    });
  };

  const solutionColumns: TableProps['columns'] = [
    {
      title: '订单编号',
      dataIndex: 'CustomOrderNumber',
      align: 'center',
      key: 'CustomOrderNumber',
    },
    {
      title: '商品',
      dataIndex: 'SolutionName',
      align: 'center',
      key: 'SolutionName',
    },
    {
      title: '价格',
      dataIndex: 'UnitPriceIT',
      align: 'center',
      key: 'UnitPriceIT',
      render: (value: any) => <>{'￥' + formatWithThousandSeparator(value)}</>,
    },
    {
      title: '数量',
      dataIndex: 'Quantity',
      align: 'center',
      key: 'Quantity',
    },
    {
      title: '小计',
      dataIndex: 'ItemTotalIT',
      key: 'ItemTotalIT',
      align: 'center',
      render: (value: any) => <>{'￥' + formatWithThousandSeparator(value)}</>,
    },
  ];
  const tableFooter = () => {
    return (
      <div className={styles.tableFooterText}>
        <Row>
          <Col span={12}></Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <span>
              合计&nbsp;
              <span className={styles.tableFooterAmount}>
                ￥{detail.TotalAmount}
              </span>
            </span>
          </Col>
        </Row>
      </div>
    );
  };
  const backClick = () => {
    if (BackType === 1) {
      navigate('/enterprise/orders');
    } else {
      navigate('/enterprise/invoice/history', {
        state: { CustomOrderNumber: CustomOrderNumber },
      });
    }
  };
  return (
    <div>
      {contextHolder}
      <Skeleton loading={loading} active>
        <div>
          <Button
            style={{ marginLeft: '-6px' }}
            icon={<ArrowLeftOutlined />}
            type="text"
            onClick={backClick}
          ></Button>
          <span>发票详情</span>
        </div>

        <ConfigProvider
          theme={{
            components: {
              Table: {
                footerBg: '#fffff',
              },
            },
          }}
        >
          <div>
            <div>
              <Row>
                <Col span={12}>
                  <div style={{ marginTop: 16 }}>
                    <div className={styles.titleIcon}></div>
                    <span className={styles.title}>发票信息</span>
                  </div>
                  <div className={styles.orderInfo}>
                    <div>
                      <span className={styles.orderInfoTitle}>发票类型：</span>
                      <span className={styles.orderInfoValue}>
                        {detail.InvoiceType}
                      </span>
                    </div>
                    <div>
                      <span className={styles.orderInfoTitle}>发票抬头：</span>
                      <span className={styles.orderInfoValue}>
                        {detail.TitleCompanyName}
                      </span>
                    </div>
                    <div>
                      <span className={styles.orderInfoTitle}>
                        纳税人识别号：
                      </span>
                      <span className={styles.orderInfoValue}>
                        {detail.TIN}
                      </span>
                    </div>
                    <div>
                      <span className={styles.orderInfoTitle}>开户行：</span>
                      <span className={styles.orderInfoValue}>
                        {detail.Bank}
                      </span>
                    </div>
                    <div>
                      <span className={styles.orderInfoTitle}>
                        开户行账号：
                      </span>
                      <span className={styles.orderInfoValue}>
                        {detail.BankAccount}
                      </span>
                    </div>
                    <div>
                      <span className={styles.orderInfoTitle}>开票金额：</span>
                      <span className={styles.orderInfoValue}>
                        {formatWithThousandSeparator(detail.TotalAmount)}
                      </span>
                    </div>
                  </div>
                </Col>

                <Col span={12}>
                  <div style={{ marginTop: 16 }}>
                    <div className={styles.titleIcon}></div>
                    <span className={styles.title}>
                      {detail.InvoiceTypeId === 0 || detail.InvoiceTypeId === 1
                        ? '地址信息'
                        : '接收邮箱'}
                    </span>
                  </div>
                  {(detail.InvoiceTypeId === 0 ||
                    detail.InvoiceTypeId === 1) && (
                    <div className={styles.orderInfo}>
                      <div>
                        <span className={styles.orderInfoTitle}>收件人：</span>
                        <span className={styles.orderInfoValue}>
                          {detail.ReceivePerson}
                        </span>
                      </div>
                      <div>
                        <span className={styles.orderInfoTitle}>
                          联系电话：
                        </span>
                        <span className={styles.orderInfoValue}>
                          {detail.ReceivePhoneNumber}
                        </span>
                      </div>
                      <div>
                        <span className={styles.orderInfoTitle}>
                          详细地址：
                        </span>
                        <span className={styles.orderInfoValue}>
                          {detail.ReceiveAddress}
                        </span>
                      </div>
                    </div>
                  )}
                  {(detail.InvoiceTypeId === 2 ||
                    detail.InvoiceTypeId === 3) && (
                    <div className={styles.orderInfo}>
                      <div>
                        <span className={styles.orderInfoTitle}>
                          接收邮箱：
                        </span>
                        <span className={styles.orderInfoValue}>
                          {detail.ReceiveEmail}
                        </span>
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
            </div>
            <div style={{ marginTop: 32 }}>
              <div className={styles.titleIcon}></div>
              <span className={styles.title}>备注信息</span>
            </div>
            <div style={{ marginTop: 16 }}>{detail.Remark}</div>
            <div style={{ marginTop: 32 }}>
              <div className={styles.titleIcon}></div>
              <span className={styles.title}>订单信息</span>
            </div>
            <div style={{ marginTop: 16 }}>
              {/* <h4 style={{ marginTop: 16, fontWeight: 600 }}>订单编号</h4>
              <div style={{ marginTop: 16, fontWeight: 500 }}>
                {detail.CustomOrderNumber}
              </div>
              <h4 style={{ marginTop: 16, marginBottom: 16, fontWeight: 600 }}>
                商品信息
              </h4> */}
              <Table
                bordered={true}
                pagination={false}
                rowKey={'SolutionId'}
                columns={solutionColumns}
                dataSource={solutionTableData()}
                footer={tableFooter}
              />
            </div>
          </div>
          <div style={{ marginTop: 32 }}>
            <Flex
              justify="space-between"
              style={{ width: '100%', marginBottom: 10 }}
            >
              <div>
                <div className={styles.titleIcon}></div>
                <span className={styles.title}>流程记录</span>
              </div>
            </Flex>
          </div>
          <div style={{ marginTop: 16 }}>
            <Table
              style={{ marginTop: 16 }}
              rowKey={'Id'}
              bordered={true}
              pagination={false}
              columns={columns}
              dataSource={InvoiceOperationData()}
            />
          </div>
        </ConfigProvider>
      </Skeleton>
    </div>
  );
};
export default InvoiceDetail;
