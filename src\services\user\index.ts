import { request } from '@umijs/max';

export async function logout() {
  // return request('/api/login');
}

export async function login(payload: any) {
  return request('/api/Users/<USER>', {
    method: 'POST',
    data: payload,
  });
}

export async function getEmailcode(email: string) {
  return request(`/api/Users/<USER>/email/send?email=${email}`, {
    method: 'POST',
  });
}

export async function checkEmailcode(payload: any) {
  return request(`/api/Users/<USER>/email/check`, {
    method: 'POST',
    data: payload,
  });
}

export async function checkEmailExistance(email: string) {
  return request(`/api/Users/<USER>/existance?email=${email}`, {
    method: 'GET',
  });
}

export async function forgetpassword(email: string) {
  return request(`/api/Users/<USER>/reset`, {
    method: 'POST',
    data: {
      email: email,
    },
  });
}

export async function resetpassword(payload: any) {
  return request(`/api/Users/<USER>/reset`, {
    method: 'PUT',
    data: payload,
  });
}

export async function getxsrftoken(): Promise<{ Token: string }> {
  return request(`/api/Csrf/token`);
}

export async function regNewUser(payload: any, token: string) {
  return request(`/api/Users`, {
    method: 'POST',
    data: payload,
    headers: {
      'X-Csrf-Token': token,
    },
  });
}

export async function getUser() {
  return request('/api/logout');
}

export async function getVerifyCode() {
  return request('/api/Users/<USER>', {
    method: 'GET',
  });
}

export async function getUserInfo() {
  return request('/api/Users/<USER>', {
    method: 'GET',
  });
}

export async function activationsUser(data: any) {
  return request(`/api/Users/<USER>/activations`, {
    method: 'POST',
    data,
  });
}
export async function getActivationsUser(params: any) {
  return request(`/api/Users/<USER>
    method: 'GET',
    params: params,
  });
}
