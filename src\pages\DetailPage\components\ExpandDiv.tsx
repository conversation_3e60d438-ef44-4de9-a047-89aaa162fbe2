import { Space } from 'antd';
import { createStyles } from 'antd-style';
import React, { useLayoutEffect, useRef, useState } from 'react';

interface ExpandDivProps {
  children: React.ReactNode;
  maxHeight?: number;
  moreText?: string;
  lessText?: string;
}

const useStyles = createStyles(
  ({ token, css }, props: { expanded: boolean; showToggle: boolean }) => {
    const linear = css`
      background: linear-gradient(
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 1)
      );
    `;
    return {
      expandContent: css`
        position: relative;
        overflow: hidden;
        transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 50px;
          pointer-events: none;
          ${!props.showToggle || props.expanded ? '' : linear}
        }
      `,
      expandText: css`
        cursor: pointer;
        margin-top: 15px;
        font-size: 14px;
        color: #2d8cf0;
      `,
      icon: css`
        font-size: 12px;
      `,
    };
  },
);

const ExpandDiv: React.FC<ExpandDivProps> = ({
  children,
  maxHeight = 280,
  moreText = '显示更多',
  lessText = '隐藏',
}) => {
  const contentRef = useRef<HTMLDivElement>(null);

  const [showToggle, setShowToggle] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [contentHeight, setContentHeight] = useState<number>(0);

  const { styles } = useStyles({ expanded, showToggle });

  useLayoutEffect(() => {
    if (contentRef.current) {
      const scrollHeight = contentRef.current.scrollHeight;
      setContentHeight(scrollHeight);
      setShowToggle(scrollHeight > maxHeight);
    }
  }, [children, maxHeight]);

  return (
    <div>
      <div
        ref={contentRef}
        className={styles.expandContent}
        style={{
          maxHeight: expanded ? contentHeight : maxHeight,
        }}
      >
        {children}
      </div>
      {showToggle && (
        <div
          className={styles.expandText}
          onClick={() => setExpanded((prev) => !prev)}
        >
          {expanded ? (
            <Space>
              {lessText}
              <span className={styles.icon}>▲</span>
            </Space>
          ) : (
            <Space>
              {moreText}
              <span className={styles.icon}>▼</span>
            </Space>
          )}
        </div>
      )}
    </div>
  );
};

export default ExpandDiv;
