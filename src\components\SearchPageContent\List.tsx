import PriceStar from '@/components/pricestar';
import ProductList from '@/components/Product/List';
import { OnlineListItem } from '@/pages/SearchPage/services';
import { formatWithThousandSeparator } from '@/utils/currencyUtil';
import { Flex, Pagination, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { FC, memo } from 'react';

interface ListProps {
  to: 'solution' | 'yellow-pages-products';
  tag: 'online' | 'consultation';
  data: OnlineListItem[];
  pagination: {
    page: number;
    setPage: (page: number) => void;
    pageSize: number;
    total?: number;
  };
  loading: boolean;
}

const useStyles = createStyles(({ prefixCls, css }) => ({
  pagination: css`
    align-self: flex-end;
  `,
}));

const List: FC<ListProps> = ({ data = [], pagination, loading, tag, to }) => {
  const { styles } = useStyles();
  return (
    <Flex vertical gap={24}>
      <Skeleton loading={loading}>
        <ProductList
          tag={tag}
          type={to!}
          list={
            data.map((item) => ({
              id: item.Id,
              globalId: item.GlobalId,
              name: item.Name,
              description: item.ShortDescription,
              image: item.LogoUrl,
              price: item.LimitPurchase ? (
                <PriceStar />
              ) : (
                formatWithThousandSeparator(item.ShowedPrice)
              ),
              currency: item.Currency,
              supplier: item.Supplier,
            })) || []
          }
        />
      </Skeleton>
      <Pagination
        className={styles.pagination}
        hideOnSinglePage={true}
        total={pagination.total}
        current={pagination.page}
        pageSize={pagination.pageSize}
        onChange={pagination.setPage}
      />
    </Flex>
  );
};

export default memo(List);
