import { ReactComponent as SearchSVG } from '@/assets/search.svg';
import {
  deleteInvoiceAddress,
  getInvoiceAddress,
  postInvoiceAddress,
  putInvoiceAddress,
} from '@/services/order/InvoiceController';
import {
  Button,
  Flex,
  Form,
  Input,
  Modal,
  Popconfirm,
  Space,
  Table,
  TableProps,
  Typography,
  notification,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';
const useStyles = createStyles(({ css }) => ({
  search: css`
    border-radius: 16px;
  `,
}));
const InvoiceAddress = () => {
  interface DataType {
    FullName: string;
    Email: string;
    Phone: string;
    UserRoleNames: string;
    CreatedOn: string;
    LastLoginDate: string;
    Active: boolean;
    Id: number;
    RoleId: number;
  }
  const [api, contextHolder] = notification.useNotification();
  const [isAdd, setisAdd] = useState<boolean>(false);
  const [addConfirmLoading, setAddConfirmLoading] = useState<boolean>(false);
  const [updateConfirmLoading, setUpdateConfirmLoading] =
    useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [data, setData] = useState<DataType[]>([]);
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const columns: TableProps<DataType>['columns'] = [
    {
      title: '别名',
      dataIndex: 'AddrNickName',
      key: 'AddrNickName',
    },
    {
      title: '收件人',
      dataIndex: 'ToName',
      key: 'ToName',
    },
    {
      title: '联系电话',
      dataIndex: 'PhoneNumber',
      key: 'PhoneNumber',
    },
    {
      title: '地址',
      key: 'Address',
      dataIndex: 'Address',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              setIsEdit(true);
              editForm.setFieldsValue({
                ...record,
              });
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除吗？"
            onConfirm={() => {
              deleteClick(record.Id);
            }}
          >
            <Button type="link" size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const getList = () => {
    setTableLoading(true);
    let values = searchForm.getFieldsValue();
    getInvoiceAddress(values).then((res) => {
      setData(res);
      setTableLoading(false);
    });
  };

  const onAddFinished = (values: any) => {
    setAddConfirmLoading(true);
    postInvoiceAddress(values).then(
      (res) => {
        api['success']({
          message: '添加发票地址成功',
          duration: 2,
        });
        setAddConfirmLoading(false);
        setisAdd(false);
        addForm.resetFields();
        getList();
      },
      () => {
        setAddConfirmLoading(false);
      },
    );
  };
  const deleteClick = (id: any) => {
    deleteInvoiceAddress([id]).then(
      (res) => {
        api['success']({
          message: '删除发票地址成功',
          duration: 2,
        });
        getList();
      },
      () => {
        api['error']({
          message: '删除发票地址失败',
          duration: 2,
        });
      },
    );
  };
  const onEditFinished = (values: any) => {
    console.log('values', values);
    setUpdateConfirmLoading(true);
    putInvoiceAddress(values.Id, values).then(
      (res) => {
        api['success']({
          message: '编辑发票地址成功',
          duration: 2,
        });
        setUpdateConfirmLoading(false);
        setIsEdit(false);
        getList();
      },
      () => {
        setUpdateConfirmLoading(false);
        api['error']({
          message: '编辑发票地址失败',
          duration: 2,
        });
      },
    );
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <div>
      {contextHolder}

      <Typography.Title level={5}>地址管理</Typography.Title>
      <Flex
        justify="space-between"
        style={{ width: '100%', marginBottom: 20, paddingRight: 8 }}
      >
        <Form
          layout={'inline'}
          labelAlign="left"
          form={searchForm}
          style={{
            maxWidth: 'none',
            paddingTop: 16,
            paddingBottom: 16,
          }}
        >
          <Form.Item label="收件人" name="ToName">
            <Input placeholder="搜索收件人" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <Form.Item label="地址" name="Address">
            <Input placeholder="搜索地址" suffix={<SearchSVG />}></Input>
          </Form.Item>

          <div style={{ textAlign: 'left' }}>
            <Button
              type="primary"
              style={{ width: 80 }}
              onClick={() => {
                getList();
              }}
            >
              搜索
            </Button>
          </div>
        </Form>

        <Button
          type="primary"
          style={{ width: 80 }}
          onClick={(e) => {
            setisAdd(true);
          }}
        >
          新建
        </Button>
      </Flex>
      <Table<DataType>
        loading={tableLoading}
        pagination={false}
        rowKey={(record) => record.Id}
        columns={columns}
        dataSource={data}
      />

      <Modal
        title="添加发票地址"
        open={isAdd}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        onCancel={() => {
          setisAdd(false);
        }}
        confirmLoading={addConfirmLoading}
        modalRender={(dom) => (
          <Form
            form={addForm}
            name="basic"
            layout="vertical"
            onFinish={onAddFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
      >
        <Form.Item style={{ display: 'none' }} name="Id">
          <Input />
        </Form.Item>
        <Form.Item
          label="别名"
          name="AddrNickName"
          rules={[{ required: true, message: '请输入别名' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="收件人"
          name="ToName"
          rules={[
            { required: true, message: '请输入收件人' },
            { pattern: /^\S+$/, message: '收件人不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="联系电话"
          name="PhoneNumber"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^([-()+0-9]+)$/, message: '请输入正确的联系电话' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="地址"
          name="Address"
          rules={[
            { required: true, message: '请输入地址' },
            { pattern: /^\S+$/, message: '地址不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
      </Modal>
      <Modal
        title="编辑发票地址"
        open={isEdit}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="保存"
        confirmLoading={updateConfirmLoading}
        onCancel={() => {
          setIsEdit(false);
        }}
        modalRender={(dom) => (
          <Form
            form={editForm}
            layout="vertical"
            onFinish={onEditFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
        destroyOnHidden
      >
        <Form.Item label="Id" name="Id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="别名"
          name="AddrNickName"
          rules={[
            { required: true, message: '请输入别名' },
            { pattern: /^\S+$/, message: '别名不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="收件人"
          name="ToName"
          rules={[
            { required: true, message: '请输入收件人' },
            { pattern: /^\S+$/, message: '收件人不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="联系电话"
          name="PhoneNumber"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^([-()+0-9]+)$/, message: '请输入正确的联系电话' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="地址"
          name="Address"
          rules={[
            { required: true, message: '请输入地址' },
            { pattern: /^\S+$/, message: '地址不能包含空格' },
          ]}
        >
          <Input />
        </Form.Item>
      </Modal>
    </div>
  );
};

export default InvoiceAddress;
