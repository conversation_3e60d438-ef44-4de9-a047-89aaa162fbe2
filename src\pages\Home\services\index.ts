import { BrandInfo } from '@/types/brand/brand.model';
import { request } from '@umijs/max';

export type BannerItem = {
  Title: string;
  Url: string;
  ImageUrl: string;
  DisplayOrder: number;
  Remark: string;
  Type: number;
  DefaultIconUrl: string;
  SelectedIconUrl: string;
};

type BannerList = BannerItem[];
export async function getBanners(): Promise<BannerList> {
  return request(`/api/Banners?type=1`, {
    method: 'GET',
  });
}
export async function getBrands(): Promise<BrandInfo[]> {
  return request('/api/Brands', {
    method: 'GET',
  });
}

export type ProductSummary = {
  GlobalId: string;
  ImageUrl: string;
  Name: string;
  ShortDescription: string;
  LogoUrl: string;
};

export type ProductSummaryList = ProductSummary[];
export async function getFeatureSolutins(): Promise<ProductSummaryList> {
  return request(`/api/FeaturedSolutions`, {
    method: 'GET',
  });
}
