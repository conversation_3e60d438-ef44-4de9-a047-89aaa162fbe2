.customCollapseContainer {
  line-height: 22px;
}

.collapseHeader {
  display: flex;
  align-items: center;
}

.collapseTrigger {
  margin-left: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  user-select: none;
  transition: all 0.2s;
}

.collapseTrigger:hover {
  opacity: 0.8;
}

.collapseContent {
  overflow: hidden;
  transition: height 0.3s ease-out;
}

.contentInner {
  padding-top: 8px;
}

.collapseItem {
  margin-bottom: 4px;
}
