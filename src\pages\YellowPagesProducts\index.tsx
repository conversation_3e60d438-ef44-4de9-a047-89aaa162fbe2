import PageWrapper from '@/components/PageWrapper';
import ProductList from '@/components/Product/List';
import SupplierSelect from '@/components/SupplierSelect';
import TagSelect, { TagSelectOption } from '@/components/TagSelect';
import useTags from '@/components/TagSelect/hooks/useTags.';
import { DeliveryMethod } from '@/constants';
import {
  useCategoriesDeriveState,
  useCategoriesStore,
} from '@/store/categroies';
import { useSupplierStore } from '@/store/supplier';
import { useCreation, useMemoizedFn, useUnmount } from 'ahooks';
import { Flex, Pagination, Skeleton, Space } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useMemo } from 'react';
import useQueryYellowPage from './hooks/useQueryYellowPage';

const useStyles = createStyles(({ css }) => ({
  breadcrumb: {
    height: 40,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  breadcrumbContent: {
    width: 1200,
  },
  list: css`
    margin-top: 24px;
    padding: 24px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 1);
    box-shadow: inset 0px 2px 2px rgba(255, 255, 255, 0.4),
      inset 0px 2px 8px rgba(255, 255, 255, 0.4);
  `,
  pagination: css`
    align-self: flex-end;
  `,
}));

const YellowPagesProducts = () => {
  const { styles } = useStyles();
  const categroies = useCategoriesStore();

  const { currentLevelTwoMenu } = useCategoriesDeriveState();

  const supplierState = useSupplierStore();
  const options = useCreation(() => {
    return supplierState.displayedList.map((item) => ({
      label: item,
      value: item,
    }));
  }, [supplierState.displayedList]);

  const levelOneMenu = useCreation(() => {
    return [{ label: '全部分类', value: 'all', disabled: false }].concat(
      categroies.categroiesMenu?.map((item) => ({
        value: item.key,
        label: item.name as string,
        disabled: item.disabled as boolean,
      })) || [],
    );
  }, [categroies.categroiesMenu]);

  const levelTwoMenu = useCreation(() => {
    return [{ label: '全部子类', value: 'all', disabled: false }].concat(
      currentLevelTwoMenu?.map((item) => ({
        value: item.key,
        label: item.name as string,
        disabled: item.disabled as boolean,
      })) || [],
    );
  }, [currentLevelTwoMenu]);

  const { selectedKeys: selectedOneCategoryId, onTagClick: onOneTagClick } =
    useTags({
      dataSource: levelOneMenu,
      type: 'radio',
      defaultValue: [categroies.currentLevelOneId || 'all'],
    });

  const { selectedKeys: selectedTwoCategoryId, onTagClick: onTwoTagClick } =
    useTags({
      dataSource: levelTwoMenu,
      type: 'radio',
      defaultValue: [categroies.currentLevelTwoId || 'all'],
    });

  const {
    selectedKeys: selectedDeliveryMethod,
    onTagClick: onSelectedProduct,
  } = useTags({
    dataSource: DeliveryMethod,
    type: 'radio',
    defaultValue: ['all'],
  });

  const {
    yellopageList,
    loading,
    searchValues,
    updateSearchValues,
    setPage,
    setSupplier,
  } = useQueryYellowPage();

  const selectedCategoryId = useMemo(() => {
    return selectedTwoCategoryId[0] === 'all'
      ? selectedOneCategoryId
      : selectedTwoCategoryId;
  }, [selectedOneCategoryId, selectedTwoCategoryId]);

  useEffect(() => {
    updateSearchValues({
      CategoryId:
        selectedCategoryId[0] === 'all' ? undefined : selectedCategoryId[0],
      DeliveryMethod:
        selectedDeliveryMethod[0] === 'all'
          ? undefined
          : selectedDeliveryMethod[0],
      Page: 0,
    });
  }, [selectedCategoryId, selectedDeliveryMethod]);

  const handleClickOneMenu = useMemoizedFn((value, checked) => {
    categroies.currentLevelOneId = value;
    categroies.currentLevelTwoId = 'all';
    onOneTagClick(value, checked);
  });

  const handleClickTwoMenu = useMemoizedFn((value, checked) => {
    categroies.currentLevelTwoId = value;
    onTwoTagClick(value, checked);
  });

  useUnmount(() => {
    categroies.currentLevelOneId = 'all';
    categroies.currentLevelTwoId = 'all';
  });

  return (
    <>
      <PageWrapper
        style={{
          background: 'transparent',
          padding: '24px 0',
          borderRadius: 0,
        }}
      >
        <Flex vertical gap={20}>
          <TagSelect
            hideCheckAll
            items={levelOneMenu}
            renderItems={(item, selectedKeys) => (
              <TagSelectOption
                key={item.value}
                disabled={item.disabled}
                value={item.value}
                checked={selectedKeys.includes(item.value)}
                onChange={handleClickOneMenu}
              >
                {item.label}
              </TagSelectOption>
            )}
            value={selectedOneCategoryId}
          />
          {selectedOneCategoryId[0] !== 'all' ? (
            <TagSelect
              hideCheckAll
              items={levelTwoMenu}
              renderItems={(item, selectedKeys) => (
                <TagSelectOption
                  key={item.value}
                  disabled={item.disabled}
                  value={item.value}
                  checked={selectedKeys.includes(item.value)}
                  onChange={handleClickTwoMenu}
                >
                  {item.label}
                </TagSelectOption>
              )}
              value={selectedTwoCategoryId}
            />
          ) : null}
          <Flex gap={4} align="center" justify="space-between">
            <TagSelect
              hideCheckAll
              items={DeliveryMethod}
              renderItems={(item) => (
                <TagSelectOption
                  value={item.value}
                  checked={selectedDeliveryMethod.includes(item.value)}
                  onChange={onSelectedProduct}
                >
                  {item.label}
                </TagSelectOption>
              )}
              value={selectedDeliveryMethod}
            />

            <Space>
              <span>供应商：</span>
              <SupplierSelect
                options={options}
                value={searchValues.Supplier}
                onChange={setSupplier}
              />
            </Space>
          </Flex>
        </Flex>

        <Flex vertical gap={24}>
          <div className={styles.list}>
            <Skeleton loading={loading}>
              <ProductList
                tag="consultation"
                type="yellow-pages-products"
                list={
                  yellopageList?.Data?.map((item) => ({
                    id: item.Id,
                    globalId: item.GlobalId,
                    image: item.Logo,
                    name: item.SolutionName,
                    description: item.Description,
                    supplier: item.Supplier,
                    price: '',
                    currency: '',
                  })) || []
                }
              />
            </Skeleton>
          </div>

          <Pagination
            className={styles.pagination}
            hideOnSinglePage={true}
            total={yellopageList?.Total}
            current={searchValues.Page + 1}
            pageSize={searchValues.PageSize}
            onChange={setPage}
          />
        </Flex>
      </PageWrapper>
    </>
  );
};

export default YellowPagesProducts;
