import { ReactComponent as SearchSvg } from '@/assets/search.svg';
import {
  deleteUsers,
  getUsers,
  postInvite,
  putEntitiesAadmin,
  putUsers,
  putUsersActive,
} from '@/services/user/UserController';
import useUserStore, { getUser } from '@/store/user';
import { formatDate, formatDateTime } from '@/utils/format';
import {
  CloseCircleOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  PoweroffOutlined,
} from '@ant-design/icons';
import {
  Button,
  Flex,
  Form,
  Input,
  Modal,
  Pagination,
  PaginationProps,
  Popconfirm,
  Select,
  Space,
  Table,
  TableProps,
  Tag,
  Typography,
  notification,
} from 'antd';
import { createStyles } from 'antd-style';
import { useEffect, useState } from 'react';

const useStyles = createStyles(({ css }) => ({
  search: css`
    border-radius: 16px;
  `,
}));
const Users = () => {
  interface DataType {
    FullName: string;
    Email: string;
    Phone: string;
    UserRoleNames: string;
    CreatedOn: string;
    LastLoginDate: string;
    Active: boolean;
    Id: number;
    RoleId: number;
  }
  const { user } = useUserStore();
  const [api, contextHolder] = notification.useNotification();
  const { styles } = useStyles();
  const [isSelectOwner, setIsSelectOwner] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [isAdd, setisAdd] = useState<boolean>(false);
  const [addConfirmLoading, setAddConfirmLoading] = useState<boolean>(false);
  const [updateConfirmLoading, setUpdateConfirmLoading] =
    useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [data, setData] = useState<DataType[]>([]);
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const pageSize = 10;
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [total, setTotal] = useState<number>(1);
  const [selectUser, setSelectUser] = useState<any>({});
  const [tableLoading, setTableLoading] = useState<boolean>(true);

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '姓名',
      dataIndex: 'FullName',
      key: 'FullName',
      render: (text) => <a>{text}</a>,
    },
    {
      title: '邮箱',
      dataIndex: 'Email',
      key: 'Email',
    },
    {
      title: '电话',
      dataIndex: 'Phone',
      key: 'Phone',
    },
    {
      title: '角色',
      key: 'UserRoleNames',
      dataIndex: 'UserRoleNames',
    },
    {
      title: '创建日期',
      key: 'CreatedOn',
      dataIndex: 'CreatedOn',
      render: (value) => {
        return formatDate(new Date(value));
      },
    },
    {
      title: '最后登录日期',
      key: 'LastLoginDate',
      dataIndex: 'LastLoginDate',
      render: (value) => {
        return formatDateTime(new Date(value));
      },
    },
    {
      title: '是否激活',
      key: 'isActive',
      render: (_, record) => {
        if (record.Active) {
          return <Tag color="success">已激活</Tag>;
        } else {
          return <Tag color="error">未激活</Tag>;
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              editForm.setFieldsValue({
                FirstName: record.FullName,
                Phone: record.Phone,
                Id: record.Id,
                RolesIds: record.RoleId ?? 5,
              });
              setSelectUser(record);
              setIsEdit(true);
            }}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  const getList = (page?: number, searchText = '') => {
    setPageIndex(page ?? 1);
    setTableLoading(true);
    getUsers(page, pageSize, searchText).then((res) => {
      setTotal(res.Total);
      setData(res.Data);
      setTableLoading(false);
    });
  };
  const deleteClick = () => {
    deleteUsers(selectedRowKeys).then((res) => {
      getList();
    });
  };
  const activeClick = () => {
    putUsersActive({
      Active: true,
      SelectedIds: selectedRowKeys,
    }).then((res) => {
      getList();
    });
  };
  const disableClick = () => {
    putUsersActive({
      Active: false,
      SelectedIds: selectedRowKeys,
    }).then((res) => {
      getList();
    });
  };
  const onAddFinished = (values: any) => {
    setAddConfirmLoading(true);
    postInvite(values).then(
      (res) => {
        api['success']({
          message: '添加用户成功',
          duration: 2,
        });
        setAddConfirmLoading(false);
        setisAdd(false);
        getList();
      },
      () => {
        setAddConfirmLoading(false);
      },
    );
  };

  const onEditFinished = (values: any) => {
    setUpdateConfirmLoading(true);
    putUsers(values.Id, values).then(
      (res) => {
        api['success']({
          message: '编辑用户成功',
          duration: 2,
        });
        setUpdateConfirmLoading(false);
        setIsEdit(false);
        getList();
        if (selectUser.Email === user?.Email) {
          getUser();
        }
      },
      () => {
        api['error']({
          message: '编辑用户失败',
          duration: 2,
        });
      },
    );
  };
  const entitiesAdminClick = () => {
    setUpdateConfirmLoading(true);
    putEntitiesAadmin(selectUser.Id).then(
      (res) => {
        api['success']({
          message: '转移管理员成功',
          duration: 2,
        });
        setIsEdit(false);
        setUpdateConfirmLoading(false);
        getList();
      },
      () => {
        api['success']({
          message: '转移管理员失败',
          duration: 2,
        });
      },
    );
  };
  const onPageChange: PaginationProps['onChange'] = (page) => {
    getList(page);
  };
  useEffect(() => {
    getList(1);
  }, []);
  useEffect(() => {
    let selectItems = data.filter((c) => selectedRowKeys.includes(c.Id));
    let ownerItem = selectItems.filter((c) => c.Email === user?.Email);
    if (ownerItem.length === 0) {
      setIsSelectOwner(false);
    } else {
      setIsSelectOwner(true);
    }
  }, [selectedRowKeys]);
  const modalFonter = () => (
    <Flex justify="space-between" style={{ width: '100%' }}>
      {user?.UserRoleNames === '管理员' && selectUser.Email !== user?.Email && (
        <Popconfirm
          title="您确定要转移管理员吗吗？"
          onConfirm={entitiesAdminClick}
        >
          <Button
            loading={updateConfirmLoading}
            type="primary"
            disabled={!selectUser.Active}
            style={{
              width: 120,
              marginLeft: 16,
              background: '#43CF7C',
              color: '#fff',
            }}
          >
            转移管理员
          </Button>
        </Popconfirm>
      )}
      {(user?.UserRoleNames !== '管理员' ||
        selectUser.Email === user?.Email) && <span></span>}

      <Button
        loading={updateConfirmLoading}
        type="primary"
        style={{ width: 120, marginLeft: 16 }}
        onClick={() => {
          editForm.validateFields().then((values: any) => {
            onEditFinished(values);
          });
        }}
      >
        确认
      </Button>
    </Flex>
  );
  return (
    <div>
      {contextHolder}
      <Flex justify="space-between" style={{ width: '100%', marginBottom: 20 }}>
        <Typography.Title level={5}>用户管理</Typography.Title>
        <Space>
          <Input
            className={styles.search}
            onPressEnter={(e: any) => {
              getList(1, e.target.value);
            }}
            placeholder="输入姓名/公司/邮箱/电话进行搜索"
            prefix={<SearchSvg />}
          />
          {(user?.UserRoleNames === '管理员' ||
            user?.UserRoleNames === '协同管理员') && (
            <Button
              type="primary"
              icon={<PlusCircleOutlined />}
              onClick={() => {
                addForm.resetFields();
                setisAdd(true);
              }}
            >
              添加
            </Button>
          )}

          {user?.UserRoleNames === '管理员' && (
            <Popconfirm title="您确定要删除吗？" onConfirm={deleteClick}>
              <Button
                disabled={isSelectOwner || selectedRowKeys.length === 0}
                type="primary"
                icon={<CloseCircleOutlined />}
                danger
              >
                删除
              </Button>
            </Popconfirm>
          )}

          {(user?.UserRoleNames === '管理员' ||
            user?.UserRoleNames === '协同管理员') && (
            <Button
              disabled={isSelectOwner || selectedRowKeys.length === 0}
              onClick={activeClick}
              type="text"
              style={{ background: '#43CF7C', color: '#fff' }}
              icon={<PoweroffOutlined />}
            >
              启用
            </Button>
          )}

          {(user?.UserRoleNames === '管理员' ||
            user?.UserRoleNames === '协同管理员') && (
            <Button
              disabled={isSelectOwner || selectedRowKeys.length === 0}
              onClick={disableClick}
              type="text"
              style={{ background: '#FFC300', color: '#fff' }}
              icon={<MinusCircleOutlined />}
            >
              禁用
            </Button>
          )}
        </Space>
      </Flex>
      <Table<DataType>
        loading={tableLoading}
        pagination={false}
        rowKey={(record) => record.Id}
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
          },
        }}
        columns={columns}
        dataSource={data}
      />
      {total > pageSize && (
        <Pagination
          style={{ marginTop: 16 }}
          align="end"
          current={pageIndex}
          pageSize={pageSize}
          onChange={onPageChange}
          total={total}
        />
      )}

      <Modal
        title="添加用户"
        open={isAdd}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="发送激活邮件"
        onCancel={() => {
          setisAdd(false);
        }}
        confirmLoading={addConfirmLoading}
        modalRender={(dom) => (
          <Form
            form={addForm}
            name="basic"
            layout="vertical"
            onFinish={onAddFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
      >
        <Form.Item
          label="邮箱"
          name="Email"
          rules={[
            { required: true, message: '请输入email' },
            { type: 'email', message: '请输入正确的邮箱' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="角色"
          name="RolesIds"
          rules={[{ required: true, message: '请选择角色' }]}
          initialValue={user?.UserRoleNames === '管理员' ? 3 : 5}
        >
          <Select>
            {user?.UserRoleNames === '管理员' && (
              <Select.Option value={3}>协同管理员</Select.Option>
            )}
            <Select.Option value={5}>采购</Select.Option>
          </Select>
        </Form.Item>
      </Modal>
      <Modal
        title="编辑用户"
        open={isEdit}
        okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
        okText="保存"
        confirmLoading={updateConfirmLoading}
        footer={modalFonter}
        onCancel={() => {
          setIsEdit(false);
        }}
        modalRender={(dom) => (
          <Form
            form={editForm}
            layout="vertical"
            onFinish={onEditFinished}
            autoComplete="off"
          >
            {dom}
          </Form>
        )}
        destroyOnHidden
      >
        <Form.Item style={{ display: 'none' }} name="Id">
          <Input />
        </Form.Item>

        <Form.Item
          label="姓名"
          name="FirstName"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="电话"
          name="Phone"
          rules={[
            { required: true, message: '请输入电话' },
            { pattern: /^([-()+ 0-9]+)$/, message: '请输入正确的电话' },
          ]}
        >
          <Input />
        </Form.Item>
        {user?.UserRoleNames === '管理员' &&
          selectUser?.Email !== user?.Email && (
            <Form.Item
              label="角色"
              name="RolesIds"
              rules={[{ required: true, message: '请输入角色' }]}
            >
              <Select>
                <Select.Option value={3}>协同管理员</Select.Option>
                <Select.Option value={5}>采购</Select.Option>
              </Select>
            </Form.Item>
          )}
      </Modal>
    </div>
  );
};

export default Users;
