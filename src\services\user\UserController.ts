import { request } from '@umijs/max';

export async function getUsers(
  PageIndex: number = 1,
  PageSize: number = 1,
  SearchText = '',
) {
  return request(
    `/api/Users?SortDir=asc&PageIndex=${PageIndex}&PageSize=${PageSize}0&SearchText=${SearchText}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
}

export async function putUsersActive(param: any) {
  return request(`/api/Users`, {
    method: 'PUT',
    data: param,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function putEntitiesAadmin(userId: number) {
  return request(`/api/Users/<USER>/admin?userId=${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function deleteUsers(ids: number[]) {
  return request(`/api/Users`, {
    method: 'DELETE',
    data: ids,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function postInvite(params: any) {
  return request(`/api/Users/<USER>
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function putUsers(id: number, params: any) {
  return request(`/api/Users/<USER>
    method: 'PUT',
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
