import { Button, Flex } from 'antd';
import { createStyles } from 'antd-style';
import { FC } from 'react';
import { CategoryItem } from '../services';

interface CategoryMenuProps {
  menu: Pick<CategoryItem, 'Id' | 'Name'>[] | undefined;
  selectedKey: number;
  onSelect: (key: number) => void;
}

const useStyles = createStyles(({ prefixCls, css, token }) => ({
  catBtn: css`
    &.${prefixCls}-btn {
      cursor: pointer;
      color: #5e5e5e;
      font-size: 13px;
      font-weight: 400;
      padding: 4px 10px;
      &.${prefixCls}-btn-text:hover, &.selected {
        background: #fff;
        color: ${token.colorPrimary};
      }
    }
  `,
}));
const CategoryMenu: FC<CategoryMenuProps> = ({
  menu = [],
  onSelect,
  selectedKey,
}) => {
  const { styles, cx } = useStyles();
  const handleClick = (key: number) => {
    onSelect(key);
  };
  return (
    <Flex align="center" gap={10} wrap>
      {menu.map((item) => (
        <Button
          key={item.Id}
          type="text"
          className={cx(styles.catBtn, {
            ['selected']: selectedKey === item.Id,
          })}
          onClick={() => handleClick(item.Id)}
        >
          {item.Name}
        </Button>
      ))}
    </Flex>
  );
};

export default CategoryMenu;
