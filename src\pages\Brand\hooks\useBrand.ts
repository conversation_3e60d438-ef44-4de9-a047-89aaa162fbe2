import { useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { getBrand } from '../services';

const useBrand = () => {
  const params = useParams();
  const {
    data: brand,
    loading,
    error,
  } = useRequest(() => getBrand(Number(params.id)), {
    refreshDeps: [params.id],
    staleTime: 1000 * 60,
  });

  return {
    brand,
    loading,
    error,
  };
};

export default useBrand;
