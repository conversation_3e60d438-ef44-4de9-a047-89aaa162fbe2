import { OrderParamModel, OrderSearchModel } from '@/types';
import { request } from '@umijs/max';

export async function getCustomers(
  params: {
    PageIndex: number;
    PageSize: number;
    SearchText?: string;
  },
  options?: { [key: string]: any },
) {
  return request(`/api/Customers`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
export async function postCustomers(
  body: any,
  options?: { [key: string]: any },
) {
  return request('/api/Customers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function putCustomers(
  id: number,
  body?: any,
  options?: { [key: string]: any },
) {
  return request(`/api/Customers/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function deleteCustomer(
  ids: number[],
  options?: { [key: string]: any },
) {
  return request(`/api/Customers`, {
    method: 'DELETE',
    data: ids,
    ...(options || {}),
  });
}
export async function postOrders(param: OrderParamModel) {
  return request(`/api/Orders`, {
    method: 'POST',
    data: param,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function getOrderDetail(
  customOrderNumber: string,
  groupOrderItem: boolean = false,
) {
  return request(
    `/api/Orders/detail/${customOrderNumber}?groupOrderItem=${groupOrderItem}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
}
export async function putOrderContract(
  customOrderNumber: string,
  contractId: string,
  param: any,
) {
  const formData = new FormData();
  for (let key in param) {
    if (param.hasOwnProperty(key)) {
      formData.append(key, param[key]);
    }
  }
  const response = await request(
    `/api/Orders/${customOrderNumber}/contract?contractId=${contractId}`,
    {
      method: 'PUT',
      data: formData,
    },
  );

  return response;
}
export async function putPayments(paymentNumber: string, param: any) {
  return request(`/api/Payments/${paymentNumber}`, {
    method: 'PUT',
    data: param,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function postOfflineInformation(
  paymentNumber: string,
  param: any,
) {
  return request(`/api/${paymentNumber}/offlineInformation`, {
    method: 'POST',
    data: param,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function getOrders(param: OrderSearchModel) {
  console.log('getOrders', param);
  return request(`/api/Orders`, {
    method: 'GET',
    params: param,
    paramsSerializer: (params) => {
      return Object.keys(params)
        .map((key) => {
          const value = params[key];
          if (Array.isArray(value)) {
            // 过滤数组中的空值
            return value
              .filter((v) => v !== null && v !== undefined && v !== '')
              .map((v) => `${key}=${v}`)
              .join('&');
          } else if (value !== null && value !== undefined && value !== '') {
            let finalValue = value;
            if (typeof finalValue === 'string') {
              finalValue = finalValue.trim(); // 去除字符串前后空格
            }
            return `${key}=${finalValue}`;
          }
          return ''; // 空值返回空字符串
        })
        .filter(Boolean) // 过滤掉空字符串
        .join('&');
    },
  });
}
export async function getOrderContract(customOrderNumber: string) {
  return request(`/api/Orders/${customOrderNumber}/contract`, {
    method: 'GET',
  });
}
export async function getPDFData(url: string) {
  return request(url, {
    method: 'GET',
  });
}
export async function putOrderSingleComplete(OrderItemGlobalId: string) {
  return request(`/api/Orders/items/${OrderItemGlobalId}/complete`, {
    method: 'PUT',
  });
}
export async function putOrderComplete(customerOrderNumber: string) {
  return request(`/api/Orders/${customerOrderNumber}/complete`, {
    method: 'PUT',
  });
}
export async function putCancelOrder(customOrderNumber: string) {
  return request(`/api/Orders/cancel/${customOrderNumber}`, {
    method: 'PUT',
  });
}
export const deleteCartItem = async (id: number) => {
  return request(`/api/ShoppingCarts/items/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });
};
export async function getBillDetail(billNumber: string) {
  return request(`/api/Orders/${billNumber}/bills`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
export async function postBillOfflineInformation(
  paymentNumber: string,
  param: any,
) {
  return request(`/api/${paymentNumber}/billOfflineInformation`, {
    method: 'POST',
    data: param,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
