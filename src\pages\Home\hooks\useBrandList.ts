import { useRequest, useSafeState } from 'ahooks';
import { useEffect } from 'react';
import { getBrands } from '../services';

const useBrandList = () => {
  const [selectedKey, setSelectedKey] = useSafeState<number>();
  const { data: brands } = useRequest(getBrands);

  useEffect(() => {
    if (brands?.length) {
      setSelectedKey(brands?.[0]?.Id);
    }
  }, [JSON.stringify(brands)]);

  return {
    brands,
    selectedKey,
    setSelectedKey,
  };
};

export default useBrandList;
