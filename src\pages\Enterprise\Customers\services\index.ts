import { CamelCaseKeys } from '@/types';
import { request } from '@umijs/max';

// 获取客户列表
export async function getCustomersList(params: {
  SearchText?: string;
  Region: number;
  PageIndex: number;
  PageSize: number;
}): Promise<{ Data: Customer[]; Total: number }> {
  return request('/api/Customers', {
    method: 'GET',
    params: params,
  });
}

// 客户接口
export interface Customer {
  Id: number;
  Name: string;
  Email: string;
  Company: string;
  PhoneNumber: string;
  Address: string;
  Region: number;
  Industry: number;
  Competitor: string;
}
// 添加客户
export async function addCustomer(params: Customer) {
  return request(`/api/Customers`, {
    method: 'POST',
    data: params,
  });
}

// 删除客户
export async function deleteCustomer(params: number[]) {
  return request(`/api/Customers`, {
    method: 'DELETE',
    data: params,
  });
}

// 更新客户
export async function updateCustomer(params: CamelCaseKeys<Customer>) {
  return request(`/api/Customers/${params.id}`, {
    method: 'PUT',
    data: params,
  });
}
